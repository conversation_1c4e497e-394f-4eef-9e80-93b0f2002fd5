#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
系统监控模块
提供系统状态监控、性能统计和健康检查功能
"""

import time
import psutil
import threading
from typing import Dict, List, Optional, Callable
from dataclasses import dataclass, asdict
from datetime import datetime, timedelta

from PySide6.QtCore import QObject, Signal, QTimer

from config import get_config
from logger import get_logger, LoggerMixin
from exceptions import MicroscopeException


@dataclass
class SystemMetrics:
    """系统指标数据类"""
    timestamp: float
    cpu_percent: float
    memory_percent: float
    memory_used_mb: float
    memory_total_mb: float
    disk_usage_percent: float
    disk_free_gb: float
    process_count: int
    thread_count: int
    
    def to_dict(self) -> dict:
        """转换为字典"""
        return asdict(self)


@dataclass
class ComponentStatus:
    """组件状态数据类"""
    name: str
    status: str  # "healthy", "warning", "error", "unknown"
    last_check: float
    error_count: int
    warning_count: int
    details: dict
    
    @property
    def is_healthy(self) -> bool:
        """是否健康"""
        return self.status == "healthy"
    
    @property
    def needs_attention(self) -> bool:
        """是否需要关注"""
        return self.status in ["warning", "error"]


class PerformanceTracker:
    """性能跟踪器"""
    
    def __init__(self, max_history: int = 1000):
        self.max_history = max_history
        self.metrics_history: List[SystemMetrics] = []
        self.operation_times: Dict[str, List[float]] = {}
        self.lock = threading.Lock()
    
    def add_metrics(self, metrics: SystemMetrics):
        """添加系统指标"""
        with self.lock:
            self.metrics_history.append(metrics)
            if len(self.metrics_history) > self.max_history:
                self.metrics_history.pop(0)
    
    def record_operation_time(self, operation: str, duration: float):
        """记录操作时间"""
        with self.lock:
            if operation not in self.operation_times:
                self.operation_times[operation] = []
            
            self.operation_times[operation].append(duration)
            if len(self.operation_times[operation]) > self.max_history:
                self.operation_times[operation].pop(0)
    
    def get_average_operation_time(self, operation: str) -> Optional[float]:
        """获取操作平均时间"""
        with self.lock:
            times = self.operation_times.get(operation, [])
            return sum(times) / len(times) if times else None
    
    def get_recent_metrics(self, count: int = 10) -> List[SystemMetrics]:
        """获取最近的指标"""
        with self.lock:
            return self.metrics_history[-count:] if self.metrics_history else []
    
    def get_performance_summary(self) -> dict:
        """获取性能摘要"""
        with self.lock:
            recent_metrics = self.metrics_history[-10:] if self.metrics_history else []
            
            if not recent_metrics:
                return {}
            
            avg_cpu = sum(m.cpu_percent for m in recent_metrics) / len(recent_metrics)
            avg_memory = sum(m.memory_percent for m in recent_metrics) / len(recent_metrics)
            
            operation_summary = {}
            for op, times in self.operation_times.items():
                if times:
                    operation_summary[op] = {
                        "avg_time": sum(times) / len(times),
                        "min_time": min(times),
                        "max_time": max(times),
                        "count": len(times)
                    }
            
            return {
                "avg_cpu_percent": avg_cpu,
                "avg_memory_percent": avg_memory,
                "metrics_count": len(self.metrics_history),
                "operations": operation_summary
            }


class SystemMonitor(QObject, LoggerMixin):
    """系统监控器"""
    
    # 信号
    metrics_updated = Signal(object)  # SystemMetrics
    component_status_changed = Signal(str, str)  # component_name, status
    alert_triggered = Signal(str, str, str)  # level, component, message
    
    def __init__(self, update_interval: int = 5000):  # 5秒更新间隔
        super().__init__()
        self.config = get_config()
        self.update_interval = update_interval
        
        # 性能跟踪器
        self.performance_tracker = PerformanceTracker()
        
        # 组件状态
        self.component_status: Dict[str, ComponentStatus] = {}
        
        # 监控定时器
        self.monitor_timer = QTimer()
        self.monitor_timer.timeout.connect(self._update_metrics)
        
        # 健康检查回调
        self.health_check_callbacks: Dict[str, Callable[[], ComponentStatus]] = {}
        
        # 告警阈值
        self.alert_thresholds = {
            "cpu_percent": 80.0,
            "memory_percent": 85.0,
            "disk_usage_percent": 90.0,
            "error_count": 10,
            "warning_count": 50
        }
        
        # 初始化组件状态
        self._initialize_component_status()
    
    def start_monitoring(self):
        """开始监控"""
        self.logger.info(f"开始系统监控，更新间隔: {self.update_interval}ms")
        self.monitor_timer.start(self.update_interval)
    
    def stop_monitoring(self):
        """停止监控"""
        self.monitor_timer.stop()
        self.logger.info("停止系统监控")
    
    def _initialize_component_status(self):
        """初始化组件状态"""
        components = ["camera", "motor", "autofocus", "scan", "ui"]
        current_time = time.time()
        
        for component in components:
            self.component_status[component] = ComponentStatus(
                name=component,
                status="unknown",
                last_check=current_time,
                error_count=0,
                warning_count=0,
                details={}
            )
    
    def _update_metrics(self):
        """更新系统指标"""
        try:
            # 收集系统指标
            metrics = self._collect_system_metrics()
            
            # 添加到性能跟踪器
            self.performance_tracker.add_metrics(metrics)
            
            # 检查告警
            self._check_alerts(metrics)
            
            # 更新组件状态
            self._update_component_status()
            
            # 发送信号
            self.metrics_updated.emit(metrics)
            
        except Exception as e:
            self.logger.error("更新系统指标时发生错误", exception=e)
    
    def _collect_system_metrics(self) -> SystemMetrics:
        """收集系统指标"""
        # CPU使用率
        cpu_percent = psutil.cpu_percent(interval=0.1)
        
        # 内存使用情况
        memory = psutil.virtual_memory()
        memory_percent = memory.percent
        memory_used_mb = memory.used / (1024 * 1024)
        memory_total_mb = memory.total / (1024 * 1024)
        
        # 磁盘使用情况
        disk = psutil.disk_usage('/')
        disk_usage_percent = (disk.used / disk.total) * 100
        disk_free_gb = disk.free / (1024 * 1024 * 1024)
        
        # 进程和线程数
        process_count = len(psutil.pids())
        thread_count = sum(p.num_threads() for p in psutil.process_iter(['num_threads']) 
                          if p.info['num_threads'] is not None)
        
        return SystemMetrics(
            timestamp=time.time(),
            cpu_percent=cpu_percent,
            memory_percent=memory_percent,
            memory_used_mb=memory_used_mb,
            memory_total_mb=memory_total_mb,
            disk_usage_percent=disk_usage_percent,
            disk_free_gb=disk_free_gb,
            process_count=process_count,
            thread_count=thread_count
        )
    
    def _check_alerts(self, metrics: SystemMetrics):
        """检查告警"""
        # CPU告警
        if metrics.cpu_percent > self.alert_thresholds["cpu_percent"]:
            self._trigger_alert("warning", "system", 
                              f"CPU使用率过高: {metrics.cpu_percent:.1f}%")
        
        # 内存告警
        if metrics.memory_percent > self.alert_thresholds["memory_percent"]:
            self._trigger_alert("warning", "system", 
                              f"内存使用率过高: {metrics.memory_percent:.1f}%")
        
        # 磁盘告警
        if metrics.disk_usage_percent > self.alert_thresholds["disk_usage_percent"]:
            self._trigger_alert("warning", "system", 
                              f"磁盘使用率过高: {metrics.disk_usage_percent:.1f}%")
    
    def _update_component_status(self):
        """更新组件状态"""
        current_time = time.time()
        
        for component_name, callback in self.health_check_callbacks.items():
            try:
                status = callback()
                old_status = self.component_status.get(component_name)
                
                if old_status and old_status.status != status.status:
                    self.component_status_changed.emit(component_name, status.status)
                    self.logger.info(f"组件 {component_name} 状态变更: {old_status.status} -> {status.status}")
                
                self.component_status[component_name] = status
                
                # 检查组件告警
                if status.error_count > self.alert_thresholds["error_count"]:
                    self._trigger_alert("error", component_name, 
                                      f"错误数量过多: {status.error_count}")
                elif status.warning_count > self.alert_thresholds["warning_count"]:
                    self._trigger_alert("warning", component_name, 
                                      f"警告数量过多: {status.warning_count}")
                
            except Exception as e:
                self.logger.error(f"更新组件 {component_name} 状态时发生错误", exception=e)
                
                # 设置为错误状态
                self.component_status[component_name] = ComponentStatus(
                    name=component_name,
                    status="error",
                    last_check=current_time,
                    error_count=self.component_status.get(component_name, ComponentStatus(
                        component_name, "unknown", 0, 0, 0, {})).error_count + 1,
                    warning_count=0,
                    details={"error": str(e)}
                )
    
    def _trigger_alert(self, level: str, component: str, message: str):
        """触发告警"""
        self.logger.warning(f"告警 [{level.upper()}] {component}: {message}")
        self.alert_triggered.emit(level, component, message)
    
    def register_health_check(self, component_name: str, 
                            callback: Callable[[], ComponentStatus]):
        """注册健康检查回调"""
        self.health_check_callbacks[component_name] = callback
        self.logger.info(f"注册组件 {component_name} 的健康检查")
    
    def unregister_health_check(self, component_name: str):
        """取消注册健康检查回调"""
        if component_name in self.health_check_callbacks:
            del self.health_check_callbacks[component_name]
            self.logger.info(f"取消注册组件 {component_name} 的健康检查")
    
    def get_system_status(self) -> dict:
        """获取系统状态"""
        recent_metrics = self.performance_tracker.get_recent_metrics(1)
        current_metrics = recent_metrics[0] if recent_metrics else None
        
        component_summary = {}
        for name, status in self.component_status.items():
            component_summary[name] = {
                "status": status.status,
                "error_count": status.error_count,
                "warning_count": status.warning_count,
                "last_check": datetime.fromtimestamp(status.last_check).isoformat()
            }
        
        return {
            "timestamp": datetime.now().isoformat(),
            "current_metrics": current_metrics.to_dict() if current_metrics else None,
            "performance_summary": self.performance_tracker.get_performance_summary(),
            "components": component_summary,
            "monitoring_active": self.monitor_timer.isActive()
        }
    
    def record_operation_time(self, operation: str, duration: float):
        """记录操作时间"""
        self.performance_tracker.record_operation_time(operation, duration)
    
    def set_alert_threshold(self, metric: str, threshold: float):
        """设置告警阈值"""
        if metric in self.alert_thresholds:
            self.alert_thresholds[metric] = threshold
            self.logger.info(f"设置告警阈值 {metric}: {threshold}")


if __name__ == "__main__":
    # 测试系统监控
    from logger import setup_logging
    from PySide6.QtWidgets import QApplication
    import sys
    
    app = QApplication(sys.argv)
    setup_logging("monitor_test.log", "DEBUG")
    
    monitor = SystemMonitor(update_interval=2000)  # 2秒更新
    
    # 注册测试健康检查
    def test_health_check():
        return ComponentStatus(
            name="test",
            status="healthy",
            last_check=time.time(),
            error_count=0,
            warning_count=0,
            details={"test": "ok"}
        )
    
    monitor.register_health_check("test", test_health_check)
    
    # 连接信号
    monitor.metrics_updated.connect(lambda m: print(f"CPU: {m.cpu_percent:.1f}%, Memory: {m.memory_percent:.1f}%"))
    monitor.alert_triggered.connect(lambda l, c, m: print(f"Alert [{l}] {c}: {m}"))
    
    monitor.start_monitoring()
    
    # 运行5秒后停止
    QTimer.singleShot(5000, lambda: (monitor.stop_monitoring(), app.quit()))
    
    sys.exit(app.exec())
