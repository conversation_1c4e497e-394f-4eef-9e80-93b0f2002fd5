#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
优化的相机控制模块
提供高性能的相机操作和图像处理功能
"""

import time
import threading
from queue import Queue, Empty
from typing import Optional, Callable, Any
import numpy as np

from PySide6.QtCore import QThread, Signal, QMutex, QWaitCondition, QTimer
from PySide6.QtGui import QImage

from Camera import Tucam
from config import get_config
from logger import get_logger, LoggerMixin
from exceptions import (
    CameraException, CameraConnectionException, CameraCaptureException,
    ExceptionHandler, safe_execute, retry_on_exception
)


class FrameBuffer:
    """高性能帧缓冲区"""
    
    def __init__(self, max_size: int = 10):
        self.max_size = max_size
        self.frames = Queue(maxsize=max_size)
        self.lock = threading.Lock()
        self._dropped_frames = 0
    
    def put_frame(self, frame: QImage) -> bool:
        """添加帧到缓冲区"""
        try:
            with self.lock:
                if self.frames.full():
                    # 丢弃最旧的帧
                    try:
                        self.frames.get_nowait()
                        self._dropped_frames += 1
                    except Empty:
                        pass
                
                self.frames.put_nowait(frame)
                return True
        except Exception:
            return False
    
    def get_frame(self, timeout: float = 0.1) -> Optional[QImage]:
        """从缓冲区获取帧"""
        try:
            return self.frames.get(timeout=timeout)
        except Empty:
            return None
    
    def clear(self):
        """清空缓冲区"""
        with self.lock:
            while not self.frames.empty():
                try:
                    self.frames.get_nowait()
                except Empty:
                    break
    
    @property
    def dropped_frames(self) -> int:
        """获取丢帧数量"""
        return self._dropped_frames
    
    @property
    def size(self) -> int:
        """获取当前缓冲区大小"""
        return self.frames.qsize()


class CameraController(LoggerMixin):
    """优化的相机控制器"""
    
    def __init__(self):
        super().__init__()
        self.config = get_config()
        self.camera: Optional[Tucam] = None
        self.is_connected = False
        self.is_capturing = False
        
        # 帧缓冲区
        self.frame_buffer = FrameBuffer(self.config.camera.frame_buffer_size)
        
        # 性能统计
        self.frame_count = 0
        self.last_fps_time = time.time()
        self.current_fps = 0.0
        
        # 回调函数
        self.frame_callback: Optional[Callable[[QImage], None]] = None
        self.fps_callback: Optional[Callable[[float], None]] = None
        
    @retry_on_exception(max_retries=3, delay=1.0)
    def connect(self, camera_index: int = 0) -> bool:
        """连接相机"""
        try:
            if self.is_connected:
                self.logger.warning("相机已连接")
                return True
            
            self.camera = Tucam()
            if not self.camera.OpenCamera(camera_index):
                raise CameraConnectionException(f"无法打开相机索引 {camera_index}")
            
            # 设置默认参数
            self._configure_camera()
            
            self.is_connected = True
            self.logger.info(f"相机连接成功 (索引: {camera_index})")
            return True
            
        except Exception as e:
            handled_exception = ExceptionHandler.handle_camera_exception(e, "连接")
            self.logger.error("相机连接失败", exception=handled_exception)
            raise handled_exception
    
    def disconnect(self):
        """断开相机连接"""
        try:
            if self.is_capturing:
                self.stop_capture()
            
            if self.camera and self.is_connected:
                self.camera.CloseCamera()
                self.camera.UnInitApi()
                self.camera = None
            
            self.is_connected = False
            self.frame_buffer.clear()
            self.logger.info("相机断开连接")
            
        except Exception as e:
            self.logger.error("断开相机连接时发生错误", exception=e)
    
    def _configure_camera(self):
        """配置相机参数"""
        if not self.camera:
            return
        
        config = self.config.camera
        
        # 设置曝光参数
        if config.auto_exposure:
            self.camera.set_auto_exposure(1)
        else:
            self.camera.set_auto_exposure(0)
            self.camera.set_exposure_time(config.default_exposure_time)
        
        # 设置增益
        self.camera.set_global_gain(config.default_gain)
        
        self.logger.info(f"相机配置完成: 自动曝光={config.auto_exposure}, "
                        f"曝光时间={config.default_exposure_time}ms, 增益={config.default_gain}")
    
    def start_capture(self) -> bool:
        """开始捕获"""
        try:
            if not self.is_connected or not self.camera:
                raise CameraConnectionException("相机未连接")
            
            if self.is_capturing:
                self.logger.warning("相机已在捕获中")
                return True
            
            self.camera.StartCapture()
            self.is_capturing = True
            
            # 重置性能统计
            self.frame_count = 0
            self.last_fps_time = time.time()
            self.frame_buffer.clear()
            
            self.logger.info("开始相机捕获")
            return True
            
        except Exception as e:
            handled_exception = ExceptionHandler.handle_camera_exception(e, "开始捕获")
            self.logger.error("开始捕获失败", exception=handled_exception)
            raise handled_exception
    
    def stop_capture(self):
        """停止捕获"""
        try:
            if self.camera and self.is_capturing:
                self.camera.StopCapture()
                self.is_capturing = False
                self.frame_buffer.clear()
                self.logger.info("停止相机捕获")
        except Exception as e:
            self.logger.error("停止捕获时发生错误", exception=e)
    
    def get_frame(self, timeout: float = 0.1) -> Optional[QImage]:
        """获取最新帧"""
        if not self.is_capturing or not self.camera:
            return None
        
        try:
            # 从相机获取帧
            frame = self.camera.get_frame()
            if frame is not None:
                # 添加到缓冲区
                self.frame_buffer.put_frame(frame)
                
                # 更新性能统计
                self._update_fps_stats()
                
                # 调用回调函数
                if self.frame_callback:
                    safe_execute(self.frame_callback, frame, logger=self.logger)
                
                return frame
            
            # 如果相机没有新帧，从缓冲区获取
            return self.frame_buffer.get_frame(timeout)
            
        except Exception as e:
            self.logger.error("获取帧时发生错误", exception=e)
            return None
    
    def _update_fps_stats(self):
        """更新FPS统计"""
        self.frame_count += 1
        current_time = time.time()
        elapsed = current_time - self.last_fps_time
        
        if elapsed >= self.config.camera.fps_update_interval:
            self.current_fps = self.frame_count / elapsed
            self.frame_count = 0
            self.last_fps_time = current_time
            
            # 调用FPS回调
            if self.fps_callback:
                safe_execute(self.fps_callback, self.current_fps, logger=self.logger)
    
    def capture_image(self, filename: str) -> bool:
        """拍摄单张图片"""
        try:
            if not self.is_connected or not self.camera:
                raise CameraConnectionException("相机未连接")
            
            result = self.camera.capture_image(filename)
            if result:
                self.logger.info(f"图片保存成功: {filename}")
            else:
                self.logger.error(f"图片保存失败: {filename}")
            
            return result
            
        except Exception as e:
            handled_exception = ExceptionHandler.handle_camera_exception(e, "拍摄图片")
            self.logger.error(f"拍摄图片失败: {filename}", exception=handled_exception)
            return False
    
    def set_exposure_time(self, exposure_time: int) -> bool:
        """设置曝光时间"""
        try:
            if not self.camera:
                return False
            
            self.camera.set_exposure_time(exposure_time)
            self.logger.info(f"曝光时间设置为: {exposure_time}ms")
            return True
            
        except Exception as e:
            self.logger.error(f"设置曝光时间失败: {exposure_time}ms", exception=e)
            return False
    
    def set_gain(self, gain: int) -> bool:
        """设置增益"""
        try:
            if not self.camera:
                return False
            
            self.camera.set_global_gain(gain)
            self.logger.info(f"增益设置为: {gain}")
            return True
            
        except Exception as e:
            self.logger.error(f"设置增益失败: {gain}", exception=e)
            return False
    
    def set_resolution(self, resolution: int) -> bool:
        """设置分辨率"""
        try:
            if not self.camera:
                return False
            
            self.camera.set_resolution(resolution)
            self.logger.info(f"分辨率设置为: {resolution}")
            return True
            
        except Exception as e:
            self.logger.error(f"设置分辨率失败: {resolution}", exception=e)
            return False
    
    def get_camera_info(self) -> dict:
        """获取相机信息"""
        info = {
            "connected": self.is_connected,
            "capturing": self.is_capturing,
            "fps": self.current_fps,
            "buffer_size": self.frame_buffer.size,
            "dropped_frames": self.frame_buffer.dropped_frames
        }
        return info
    
    def set_frame_callback(self, callback: Callable[[QImage], None]):
        """设置帧回调函数"""
        self.frame_callback = callback
    
    def set_fps_callback(self, callback: Callable[[float], None]):
        """设置FPS回调函数"""
        self.fps_callback = callback


class OptimizedCameraThread(QThread, LoggerMixin):
    """优化的相机线程"""

    frame_ready = Signal(object)  # 图像帧信号
    fps_updated = Signal(float)   # FPS更新信号
    photo_captured = Signal(str, bool)  # 照片拍摄信号
    error_occurred = Signal(str)  # 错误信号

    def __init__(self, camera_controller: CameraController):
        super().__init__()
        self.camera_controller = camera_controller
        self.running = False
        self.mutex = QMutex()
        self.wait_condition = QWaitCondition()

        # 设置回调函数
        self.camera_controller.set_frame_callback(self._on_frame_received)
        self.camera_controller.set_fps_callback(self._on_fps_updated)

        # 性能优化参数
        self.frame_skip_count = 0
        self.max_frame_skip = 2  # 最多跳过2帧以保持流畅性

    def run(self):
        """线程主循环"""
        self.running = True
        self.logger.info("相机线程开始运行")

        try:
            while self.running:
                self.mutex.lock()

                if not self.running:
                    self.mutex.unlock()
                    break

                # 获取帧
                frame = self.camera_controller.get_frame(timeout=0.05)

                if frame is not None:
                    # 帧跳过逻辑，减少UI更新频率
                    if self.frame_skip_count <= 0:
                        self.frame_ready.emit(frame)
                        self.frame_skip_count = self.max_frame_skip
                    else:
                        self.frame_skip_count -= 1

                self.mutex.unlock()

                # 短暂休眠，避免CPU占用过高
                self.msleep(1)

        except Exception as e:
            self.logger.error("相机线程运行时发生错误", exception=e)
            self.error_occurred.emit(str(e))

        self.logger.info("相机线程结束运行")

    def stop(self):
        """停止线程"""
        self.mutex.lock()
        self.running = False
        self.wait_condition.wakeAll()
        self.mutex.unlock()

        # 等待线程结束
        if not self.wait(5000):  # 等待5秒
            self.logger.warning("相机线程未能在5秒内正常结束")
            self.terminate()
            self.wait(1000)

    def capture_photo(self, filename: str):
        """拍摄照片"""
        try:
            success = self.camera_controller.capture_image(filename)
            self.photo_captured.emit(filename, success)
        except Exception as e:
            self.logger.error(f"拍摄照片失败: {filename}", exception=e)
            self.photo_captured.emit(filename, False)

    def _on_frame_received(self, frame: QImage):
        """帧接收回调"""
        # 这个回调在相机控制器中被调用，不需要额外处理
        pass

    def _on_fps_updated(self, fps: float):
        """FPS更新回调"""
        self.fps_updated.emit(fps)

    def set_frame_skip(self, skip_count: int):
        """设置帧跳过数量"""
        self.max_frame_skip = max(0, skip_count)


if __name__ == "__main__":
    # 测试相机控制器
    from logger import setup_logging

    setup_logging("camera_test.log", "DEBUG")

    controller = CameraController()

    try:
        # 连接相机
        controller.connect(0)

        # 开始捕获
        controller.start_capture()

        # 获取几帧图像
        for i in range(10):
            frame = controller.get_frame()
            if frame:
                print(f"获取到第 {i+1} 帧")
            time.sleep(0.1)

        # 拍摄图片
        controller.capture_image("test.jpg")

        # 获取相机信息
        info = controller.get_camera_info()
        print(f"相机信息: {info}")

    finally:
        controller.disconnect()
