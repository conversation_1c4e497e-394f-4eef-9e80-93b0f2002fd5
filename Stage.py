# -*- coding: utf-8 -*-

# --- 基础库导入 ---
import sys
import time
import traceback
import numpy as np
import json
import os
from typing import Optional, List, Tuple

# --- Qt导入 ---
from DBDynamics import BeeS
from PySide6.QtWidgets import (
    QApplication, QWidget, QVBoxLayout, QLabel, QMessageBox, QSizePolicy,
    QHBoxLayout, QPushButton, QTableView, QHeaderView, QGridLayout, QLineEdit, QComboBox, QMenu
)
from PySide6.QtGui import QFont, QStandardItemModel, QStandardItem, QIntValidator
from PySide6.QtCore import Qt, QPointF, Slot, QThread, Signal, QObject, QTimer
import pyqtgraph as pg

# --- 新的模块导入 ---
from config import Axis, get_config, CoordinateConverter
from logger import get_logger, LoggerMixin
from exceptions import (
    MotorException, MotorConnectionException, MotorMovementException,
    ExceptionHandler, safe_execute, retry_on_exception
)

# =============================================================================
# 网格类 - 简化版
# =============================================================================
class Grid:
    def __init__(self, width: float = None, height: float = None, overlap: float = 0.15):
        config = get_config()
        self.width = width or config.scan.default_grid_width
        self.height = height or config.scan.default_grid_height
        self.overlap = overlap
        self.effective_width = self.width * (1 - overlap)
        self.effective_height = self.height * (1 - overlap)

    def create_grid(self, coordinates: List[Tuple[float, float]], use_zigzag: bool = False) -> List[List[Tuple[float, float]]]:
        """创建2D网格"""
        if not coordinates or len(coordinates) == 1:
            return [coordinates] if coordinates else []

        coords = np.array(coordinates)
        min_x, max_x = np.min(coords[:, 0]), np.max(coords[:, 0])
        min_y, max_y = np.min(coords[:, 1]), np.max(coords[:, 1])

        nx = max(1, int(np.ceil((max_x - min_x) / self.effective_width)))
        ny = max(1, int(np.ceil((max_y - min_y) / self.effective_height)))
        
        total_width = nx * self.effective_width + self.width * self.overlap
        total_height = ny * self.effective_height + self.height * self.overlap
        
        start_x = max_x + (total_width - (max_x - min_x)) / 2
        start_y = min_y - (total_height - (max_y - min_y)) / 2

        grid_centers = []
        for row in range(ny):
            row_centers = []
            x_range = range(nx-1, -1, -1) if (not use_zigzag and row % 2 == 1) else range(nx)
                
            for col in x_range:
                x = start_x - self.width/2 - col * self.effective_width
                y = start_y + self.height/2 + row * self.effective_height
                row_centers.append((x, y))
            grid_centers.append(row_centers)
        
        return grid_centers

    def interpolate_z(self, known_points: List[Tuple[float, float, float]], target_point: Tuple[float, float]) -> float:
        """IDW插值计算Z值"""
        if not known_points:
            return 0.0
        if len(known_points) == 1:
            return known_points[0][2]
            
        weights_sum = weighted_z_sum = 0.0
        
        for kx, ky, kz in known_points:
            distance = np.sqrt((target_point[0] - kx)**2 + (target_point[1] - ky)**2)
            if distance == 0:
                return kz
            weight = 1.0 / (distance ** 2)
            weights_sum += weight
            weighted_z_sum += weight * kz
            
        return weighted_z_sum / weights_sum if weights_sum else 0.0

    def create_3d_grid(self, points_3d: List[Tuple[float, float, float]], use_zigzag: bool = False) -> List[List[Tuple[float, float, float]]]:
        """创建3D网格"""
        points_2d = [(x, y) for x, y, z in points_3d]
        grid_2d = self.create_grid(points_2d, use_zigzag)
        
        grid_3d = []
        for row in grid_2d:
            row_3d = []
            for x, y in row:
                z = self.interpolate_z(points_3d, (x, y))
                row_3d.append((x, y, z))
            grid_3d.append(row_3d)
        return grid_3d


        
    

# =============================================================================
#  电机控制器类 (MotorController) - 优化版
# =============================================================================
class MotorController(LoggerMixin):
    """
    封装 DBDynamics BeeS 库操作的电机控制器类。
    提供了坐标转换、电机控制和移动功能。
    """

    def __init__(self, com_port: str):
        """
        初始化 MotorController。
        Args:
            com_port (str): BeeS 设备连接的串口号 (例如: 'com8')。
        """
        super().__init__()
        self.config = get_config()
        self.m: Optional[BeeS] = None # BeeS 实例
        self.motor_sum: List[int] = [] # 检测到的电机ID列表
        self.is_initialized: bool = False # 标记控制器是否成功初始化
        self.axis_connected = {axis: False for axis in Axis}  # 使用Axis枚举跟踪连接状态

        try:
            # 尝试初始化 BeeS 实例并连接设备
            self.m = BeeS(com_port)
            self.logger.info(f"成功连接到端口 {com_port} 上的 BeeS 设备")

            # 扫描连接的电机设备
            self.motor_sum = self.m.scanDevices()
            self.logger.info(f"发现电机的个数: {len(self.motor_sum)}")
            self.logger.info(f"已连接电机ID: {self.motor_sum}")

            # 检查连接了哪些轴的电机
            self.axis_connected[Axis.X] = Axis.X in self.motor_sum
            self.axis_connected[Axis.Y] = Axis.Y in self.motor_sum
            self.axis_connected[Axis.Z] = Axis.Z in self.motor_sum

            # 输出连接的轴信息
            axes_info = []
            if self.axis_connected[Axis.X]: axes_info.append("X轴")
            if self.axis_connected[Axis.Y]: axes_info.append("Y轴")
            if self.axis_connected[Axis.Z]: axes_info.append("Z轴")
            self.logger.info(f"已连接电机: {', '.join(axes_info)}")

            # 检查是否至少找到一个电机
            if not self.motor_sum:
                raise MotorConnectionException("未找到任何电机")

            # 初始化找到的每个电机
            soft_limits = self.config.motor.soft_limit_pulse
            for i in self.motor_sum:
                # 检查是否有对应的软限位配置
                if i < len(soft_limits):
                    self.set_limit(i, soft_limits[i][0], soft_limits[i][1])
                    self.power_on(i)
                    self.set_speed(i, self.config.motor.default_speed, self.config.motor.default_acc_time)
                    self.set_position_mode(i)
                else:
                    self.logger.warning(f"未找到电机ID {i} 的软限位配置")
            self.is_initialized = True

        except Exception as e:
            handled_exception = ExceptionHandler.handle_motor_exception(e, "初始化")
            self.logger.error(f"初始化 BeeS 设备时出错 (端口: {com_port})", exception=handled_exception)
            self.m = None
            self.is_initialized = False
            raise handled_exception
    
    def power_on(self, motor_id: int, limit_soft: int = 1, limit_off: int = 0, auto_recovery: int = 1):
        """电机使能"""
        self.m.setPowerOnPro(id=motor_id, limit_soft=limit_soft, limit_off=limit_off, auto_recovery=auto_recovery)      

    def power_off(self, motor_id: int):
        """电机失能"""
        self.m.setPowerOff(motor_id)

    def stop(self):
        """停止com通讯。"""
        self.m.stop()    

    def set_speed(self, motor_id: int, target_velocity: int, acc_time: int):
        """设置电机的目标速度和加减速时间。"""
        self.m.setTargetVelocity(motor_id, int(target_velocity))
        self.m.setAccTime(motor_id, int(acc_time))

    def set_limit(self, motor_id: int, n_limit: int, p_limit: int):
        """设置电机的正负软件限位 (单位: 脉冲)。"""
        self.m.setLimitPositionN(motor_id, int(n_limit)) # 负限位
        self.m.setLimitPositionP(motor_id, int(p_limit)) # 正限位
           
    def set_position_mode(self, motor_id: int):
        """设置电机为位置控制模式。"""
        self.m.setPositionMode(motor_id)  

    def move_to_target_position(self, motor_id: int, position_pulse: int):
        """移动到目标位置 (单位: 脉冲)。"""
        self.m.setTargetPosition(motor_id, int(position_pulse)) # 发送位置指令

    def wait_target_position_reached(self, motor_id: int):
        """等待电机完成移动。"""
    
        self.m.waitTargetPositionReached(motor_id) # 等待电机完成信号

    def get_position(self, motor_id: int) -> Optional[int]:
        """获取电机当前位置 (单位: 脉冲)。"""
        return self.m.getActualPosition(motor_id)
    
    def back_zero(self, motor_id: int, direction=-1, level=0, home_speed=500):
        """电机回零"""
        self.m.setTargetVelocity(motor_id, home_speed)
        self.m.setHomingDirection(motor_id, direction)
        self.m.setHomingLevel(motor_id, level)
        self.m.setHomingMode(motor_id)
        self.m.waitHomingDone(motor_id)
    

    
    def move_to_xyz_position(self, x, y, z):
        """将XYZ三轴电机移动到指定位置"""
        
        self.move_to_target_position(Axis.X, x)
        self.move_to_target_position(Axis.Y, y)
        self.move_to_target_position(Axis.Z, z)
        self.wait_target_position_reached(Axis.X)
        self.wait_target_position_reached(Axis.Y)
        self.wait_target_position_reached(Axis.Z)
        
    def move_to_xy_position(self, x, y):
        """将XY两轴电机移动到指定位置"""
        self.move_to_target_position(Axis.X, x)
        self.move_to_target_position(Axis.Y, y)
        self.wait_target_position_reached(Axis.X)
        self.wait_target_position_reached(Axis.Y)  

    def get_xyz_position(self):
        """获取XYZ三轴电机当前位置"""
        x = self.get_position(Axis.X)
        y = self.get_position(Axis.Y)
        z = self.get_position(Axis.Z)
        return x, y, z
    
    def get_xyz_position_mm(self) -> tuple:
        """获取XYZ物理位置"""
        x_pulse, y_pulse, z_pulse = self.get_xyz_position()
        return CoordinateConverter.xyz_pulse_to_physical(x_pulse, y_pulse, z_pulse)

    @retry_on_exception(max_retries=2, delay=0.5)
    def initialize(self):
        """重新初始化电机"""
        if not self.m:
            raise MotorConnectionException("BeeS 设备未初始化")

        try:
            # 对每个电机进行初始化操作
            soft_limits = self.config.motor.soft_limit_pulse
            for motor_id in self.motor_sum:
                # 1. 先失能
                self.power_off(motor_id)
                time.sleep(0.1)

                # 2. 重新使能
                self.power_on(motor_id)
                time.sleep(0.1)

                # 3. 设置位置模式
                self.set_position_mode(motor_id)

                # 4. 设置软限位
                if motor_id < len(soft_limits):
                    self.set_limit(motor_id, soft_limits[motor_id][0],
                                  soft_limits[motor_id][1])

                # 5. 设置速度和加减速时间
                self.set_speed(motor_id, self.config.motor.default_speed,
                             self.config.motor.default_acc_time)

            self.logger.info("电机初始化完成")
        except Exception as e:
            handled_exception = ExceptionHandler.handle_motor_exception(e, "重新初始化")
            self.logger.error("执行电机初始化操作时出错", exception=handled_exception)
            raise handled_exception

# =============================================================================
#  电机控制工作者类 (MotorWorker) - 优化版
# =============================================================================
class MotorWorker(QObject):
    """
    电机控制工作线程，负责在后台执行电机操作，避免阻塞GUI线程
    """
    movementFinished = Signal()
    positionUpdated = Signal(int, int, int)

    def __init__(self, motor_controller: MotorController):
        super().__init__()
        self.controller = motor_controller

    @Slot(int, int, int)
    def perform_move(self, target_x_pulse: int, target_y_pulse: int, target_z_pulse: int):
        """执行电机移动操作"""

        # 执行移动
        self.controller.move_to_xyz_position(target_x_pulse, target_y_pulse, target_z_pulse)
        
        # 仅发送移动完成信号，不立即请求位置更新
        self.movementFinished.emit()
        
        # 直接更新位置，减少扫描过程中的等待时间
        self.request_position_update(-1)
        
    @Slot(int)
    def request_position_update(self, motor_id: int = -1):
        """请求更新位置信息"""
        # 获取位置并发送更新
        x_pos, y_pos, z_pos = self.controller.get_xyz_position()
        self.positionUpdated.emit(x_pos, y_pos, z_pos)
            


# =============================================================================
#  绘图控件类 (PyQtGraphPlotter)
# =============================================================================
class PyQtGraphPlotter(QWidget, LoggerMixin):
    """
    使用 pyqtgraph 实现的绘图控件，用于显示XY平面，处理点击，
    并显示目标位置（红叉）和实际位置（蓝点）。
    """
    # 信号：当用户在绘图区域内点击时发出，携带目标位置 (单位: mm)
    targetPositionClicked = Signal(QPointF)

    def __init__(self, parent=None):
        super().__init__(parent)
        self.config = get_config()

        # 定义移动范围限制 (单位: mm)
        self.x_limit_min = 0
        self.x_limit_max = self.config.motor.max_x_mm
        self.y_limit_min = 0
        self.y_limit_max = self.config.motor.max_y_mm

        # 存储位置数据
        self.actual_pos_mm = QPointF(0.0, 0.0)
        self.current_z_micron = 0.0
        self.table_points = []
        self.user_points = []
        self.grid_items = []
        self.grid_path = None
        self.current_highlight_index = None
        self.highlight_rect = None

        # 设置布局
        self._setup_plot_widget()
        self._setup_layout()
        self._setup_markers()
        self._setup_events()

    def _setup_plot_widget(self):
        """创建和配置绘图控件"""
        # 创建 PlotWidget
        self.plot_widget = pg.PlotWidget()
        self.plot_widget.setBackground('w')
        self.plot_widget.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)

        # 获取和配置 PlotItem
        self.plot_item = self.plot_widget.getPlotItem()
        self.plot_item.setLabel('bottom', "X (mm)")
        self.plot_item.setLabel('left', "Y (mm)")
        self.plot_item.showGrid(x=True, y=True, alpha=0.3)
        
        # 设置轴范围和方向
        self.plot_item.setXRange(-3, 57)
        self.plot_item.setYRange(-8, 52)
        self.plot_item.setAspectLocked(True)  # 设置XY轴等比例显示
        self.plot_item.invertX(True)
        self.plot_item.invertY(True)
        
        # 添加坐标信息文本
        self._add_position_text()
        
        # 添加边界矩形
        self._add_boundary_rect()

    def _add_position_text(self):
        """添加位置信息文本"""
        self.position_text = pg.TextItem(
            text="当前坐标：X: 0.000 mm, Y: 0.000 mm, Z: 0. um",
            color=(0, 0, 0),
            anchor=(0.5, 0),
            fill=(255, 255, 255, 200)
        )
        self.position_text.setFont(QFont("Arial", 10, QFont.Bold))
        self.plot_item.addItem(self.position_text)
        self.position_text.setPos((self.x_limit_min + self.x_limit_max)/2, -1)

    def _add_boundary_rect(self, max_x=None, max_y=None):
        """添加边界矩形框"""
        if max_x is None:
            max_x = self.config.motor.max_x_mm
        if max_y is None:
            max_y = self.config.motor.max_y_mm

        rect_vertices = [
            (0, 0), (max_x, 0), (max_x, max_y), (0, max_y), (0, 0)
        ]
        x_vals = [x for x, y in rect_vertices]
        y_vals = [y for x, y in rect_vertices]

        self.boundary_rect = pg.PlotCurveItem(
            x=x_vals, y=y_vals,
            pen=pg.mkPen('r', width=2)
        )
        self.plot_item.addItem(self.boundary_rect)

    def _setup_markers(self):
        """创建标记点"""
        # 目标点（红叉）
        self.target_scatter = pg.ScatterPlotItem(
            size=10, pen=pg.mkPen(color='r', width=2),
            brush=None, symbol='x'
        )
        
        # 实际位置（蓝点）
        self.actual_scatter = pg.ScatterPlotItem(
            size=8, pen=pg.mkPen(color='b'),
            brush=pg.mkBrush(color='b'), symbol='o'
        )
        
        # 表格中的点（绿点）
        self.table_points_scatter = pg.ScatterPlotItem(
            size=8, pen=pg.mkPen(color='g'),
            brush=pg.mkBrush(color='g'), symbol='o'
        )
        
        # 用户添加的点（红色）
        self.user_points_scatter = pg.ScatterPlotItem(
            size=8, pen=pg.mkPen(color='red'),
            brush=pg.mkBrush(color='red'), symbol='+'
        )
        
        # 添加所有散点图到绘图区
        self.plot_item.addItem(self.target_scatter)
        self.plot_item.addItem(self.actual_scatter)
        self.plot_item.addItem(self.table_points_scatter)
        self.plot_item.addItem(self.user_points_scatter)

    def _setup_layout(self):
        """设置布局"""
        layout = QVBoxLayout(self)
        layout.addWidget(self.plot_widget)
        layout.setContentsMargins(0, 0, 0, 0)

    def _setup_events(self):
        """设置事件处理"""
        self.click_proxy = pg.SignalProxy(
            self.plot_widget.scene().sigMouseClicked,
            rateLimit=60,
            slot=self._on_plot_clicked
        )

    def _on_plot_clicked(self, event):
        """处理绘图区域的鼠标点击事件"""
        if event[0].button() == Qt.MouseButton.LeftButton:
            # 将点击位置从场景坐标转换为视图坐标
            scene_pos = event[0].scenePos()
            view_pos = self.plot_item.vb.mapSceneToView(scene_pos)
            clicked_pos_mm = QPointF(view_pos.x(), view_pos.y())
            
            # 检查是否在限制范围内
            if not (self.x_limit_min <= clicked_pos_mm.x() <= self.x_limit_max and 
                    self.y_limit_min <= clicked_pos_mm.y() <= self.y_limit_max):
                print(f"点击位置 ({clicked_pos_mm.x():.3f}, {clicked_pos_mm.y():.3f}) 超出安全范围，已忽略")
                return

            # 更新目标标记并发送信号
            self.update_target_marker(clicked_pos_mm)
            self.targetPositionClicked.emit(clicked_pos_mm)

    def update_target_marker(self, pos_mm: QPointF):
        """更新目标位置标记（红叉）的位置"""
        self.target_scatter.setData(pos=[(pos_mm.x(), pos_mm.y())])

    @Slot(int, int, int)
    def update_actual_position(self, x_pulse: int, y_pulse: int, z_pulse: int):
        """更新实际位置标记和坐标文本"""
        # 使用CoordinateConverter类转换脉冲值到物理单位
        current_x, current_y, current_z = CoordinateConverter.xyz_pulse_to_physical(x_pulse, y_pulse, z_pulse)

        # 更新内部存储的位置
        self.actual_pos_mm.setX(current_x)
        self.actual_pos_mm.setY(current_y)
        self.current_z_micron = current_z

        # 更新UI显示
        self.actual_scatter.setData(pos=[(current_x, current_y)])
        self.position_text.setText(f"当前坐标：X: {current_x:.3f} mm, Y: {current_y:.3f} mm, Z: {current_z:.1f} um")

    def update_table_points(self, points_list):
        """更新表格点在绘图区的显示"""
        self.table_points = points_list
        self.table_points_scatter.setData(pos=points_list if points_list else [])

    def update_user_points(self, points):
        """更新用户添加的点在绘图区的显示"""
        self.user_points = points
        self.user_points_scatter.setData(pos=points if points else [])

    def highlight_current_point(self, index):
        """高亮显示指定索引的网格点"""
        self.current_highlight_index = index
        
        # 移除之前的高亮矩形
        if self.highlight_rect is not None:
            self.plot_item.removeItem(self.highlight_rect)
            self.highlight_rect = None
        
        # 如果索引无效，直接返回
        if index is None or index >= len(self.table_points) or not self.table_points:
            return

        try:
            # 获取当前点的坐标并创建高亮矩形
            x, y = self.table_points[index]
            rect_size = 0.8
            self.highlight_rect = pg.RectROI(
                [x - rect_size/2, y - rect_size/2], 
                [rect_size, rect_size],
                pen=pg.mkPen('r', width=2),
                movable=False
            )
            # 移除调整大小的控制点
            self.highlight_rect.removeHandle(0)
            
            # 添加到绘图区
            self.plot_item.addItem(self.highlight_rect)
        except (IndexError, TypeError) as e:
            print(f"无法高亮显示点 {index}: {e}")

    def clear_grid(self):
        """清除当前显示的网格"""
        # 移除所有网格相关项
        for item in self.grid_items:
            self.plot_item.removeItem(item)
        self.grid_items = []
        
        # 移除网格路径线
        if self.grid_path is not None:
            self.plot_item.removeItem(self.grid_path)
            self.grid_path = None
        
        # 清除高亮
        if self.highlight_rect is not None:
            self.plot_item.removeItem(self.highlight_rect)
            self.highlight_rect = None

    def display_grid(self, grid_centers: List[List[Tuple[float, float]]], width: float, height: float):
        """在坐标系中显示网格"""
        # 首先清除旧的网格
        self.clear_grid()
        
        # 添加网格矩形
        for row in grid_centers:
            for center in row:
                x, y = center
                rect = pg.RectROI(
                    [x - width/2, y - height/2], 
                    [width, height],
                    pen=pg.mkPen('k', width=1.2, style=Qt.DashLine),
                    movable=False
                )
                rect.removeHandle(0)
                self.plot_item.addItem(rect)
                self.grid_items.append(rect)
        
        # 使用一个平面列表存储所有路径点
        path_points = [(x, y) for row in grid_centers for x, y in row]
        
        # 创建路径数据数组
        if path_points:
            path_data = np.array(path_points)
            self.grid_path = pg.PlotDataItem(
                path_data, 
                pen=pg.mkPen('b', width=2),
                symbol='o',
                symbolSize=6,
                symbolBrush=pg.mkBrush('b')
            )
            self.plot_item.addItem(self.grid_path)
            self.grid_items.append(self.grid_path)
        
            # 添加方向箭头
            self._add_path_arrows(path_points, width, height)
        
        print(f"显示网格，共 {len(path_points)} 个点")

    def _add_path_arrows(self, path_points, width, height):
        """添加路径方向箭头"""
        if len(path_points) <= 1:
            return
            
        for i in range(len(path_points) - 1):
            x1, y1 = path_points[i]
            x2, y2 = path_points[i + 1]
            
            # 计算箭头点（路径中点）
            arrow_x = (x1 + x2) / 2
            arrow_y = (y1 + y2) / 2
            
            # 计算方向向量
            dx = x2 - x1
            dy = y2 - y1
            norm = np.sqrt(dx*dx + dy*dy)
            if norm == 0:
                continue
            
            # 归一化
            dx, dy = dx/norm, dy/norm
            
            # 箭头参数
            arrow_size = min(width, height) * 0.3
            
            # 创建箭头的两条线
            arrow_line1 = pg.PlotDataItem(
                [arrow_x, arrow_x - dx*arrow_size*0.7 - dy*arrow_size*0.3],
                [arrow_y, arrow_y - dy*arrow_size*0.7 + dx*arrow_size*0.3],
                pen=pg.mkPen('r', width=1.5)
            )
            self.plot_item.addItem(arrow_line1)
            self.grid_items.append(arrow_line1)
            
            arrow_line2 = pg.PlotDataItem(
                [arrow_x, arrow_x - dx*arrow_size*0.7 + dy*arrow_size*0.3],
                [arrow_y, arrow_y - dy*arrow_size*0.7 - dx*arrow_size*0.3],
                pen=pg.mkPen('r', width=1.5)
            )
            self.plot_item.addItem(arrow_line2)
            self.grid_items.append(arrow_line2)

# =============================================================================
#  主窗口类 (MainWindow) - 使用 PyQtGraphPlotter
# =============================================================================
class MainWindow(QWidget):
    """应用程序的主窗口。"""
    # --- 定义信号 ---
    requestMove = Signal(int, int, int)  # 修改为包含Z轴脉冲值
    requestPosition = Signal(int)
    movementFinished = Signal()  # 添加移动完成的信号
    
    def __init__(self, com_port: str):
        super().__init__()
        self.setWindowTitle("XY 平台电机控制")  # 移除端口显示

        # --- 1. 电机控制器将从外部传入 ---
        self.motor_controller = None  # 会在脚本主入口处被设置
        
        # 状态文件路径
        self.state_file = os.path.join(os.path.dirname(os.path.abspath(__file__)), "stage_state.json")
        
        # --- 2. 创建 GUI 控件 ---
        # 绘图控件
        self.plotter_widget = PyQtGraphPlotter(self)
        
        # 状态标签
        self.status_label = QLabel("状态: 初始化...")
        
        # 表格模型与视图
        self.table_model = QStandardItemModel(0, 4)  # 4列: No, X, Y, Z
        self.table_model.setHorizontalHeaderLabels(["No", "X", "Y", "Z"])
        self.table_view = QTableView()
        self.table_view.setModel(self.table_model)
        self.table_view.horizontalHeader().setSectionResizeMode(QHeaderView.ResizeMode.Stretch)
        self.table_view.setSelectionBehavior(QTableView.SelectionBehavior.SelectRows)
        
        # 启用右键菜单
        self.table_view.setContextMenuPolicy(Qt.ContextMenuPolicy.CustomContextMenu)
        self.table_view.customContextMenuRequested.connect(self.show_table_context_menu)
        
        # 按钮
        self.add_btn = QPushButton("Add")
        
        # 添加扫描速度和加速度
        speed_acc_layout = QHBoxLayout()
        
        # 扫描速度
        speed_acc_layout.addWidget(QLabel("速度:"))
        self.scan_speed_edit = QLineEdit("3000")  # 默认为3000
        self.scan_speed_edit.setFixedWidth(50)
        self.scan_speed_edit.setValidator(QIntValidator(0, 5000))
        self.scan_speed_edit.editingFinished.connect(self.apply_scan_speed_settings)
        speed_acc_layout.addWidget(self.scan_speed_edit)
        
        # 扫描加速度
        speed_acc_layout.addWidget(QLabel("加速度:"))
        self.scan_acc_edit = QLineEdit("100")  # 默认为100
        self.scan_acc_edit.setFixedWidth(40)
        self.scan_acc_edit.setValidator(QIntValidator(50, 10000))  # 限制最小值为50
        self.scan_acc_edit.editingFinished.connect(self.apply_scan_speed_settings)
        speed_acc_layout.addWidget(self.scan_acc_edit)
        
        # 添加重叠度控制
        speed_acc_layout.addWidget(QLabel("重叠度:"))
        self.overlap_edit = QLineEdit("10")  # 默认为10%
        self.overlap_edit.setFixedWidth(40)  # 减小宽度从60到40
        self.overlap_edit.setValidator(QIntValidator(0, 100))  # 限制0-100%
        self.overlap_edit.editingFinished.connect(self.apply_scan_speed_settings)
        speed_acc_layout.addWidget(self.overlap_edit)
        speed_acc_layout.addWidget(QLabel("%"))
        
        
        
        # 添加扫描轨迹选择
        speed_acc_layout.addWidget(QLabel("轨迹:"))
        self.scan_pattern_combo = QComboBox()
        self.scan_pattern_combo.addItems(["蛇形", "之形"])
        self.scan_pattern_combo.setCurrentIndex(0)  # 默认选择蛇形
        speed_acc_layout.addWidget(self.scan_pattern_combo)
        
        # 创建Add, Update和Delete按钮
        self.add_btn = QPushButton("Add")
        self.update_btn = QPushButton("Update")
        self.delete_btn = QPushButton("Delete")

        self.create_grid_btn = QPushButton("New Grid")
        self.create_3d_grid_btn = QPushButton("Create Grid")
        self.scan_btn = QPushButton("Scan")
        
        # 连接按钮信号
        self.add_btn.clicked.connect(self.on_add_clicked)
        self.update_btn.clicked.connect(self.on_update_clicked)
        self.delete_btn.clicked.connect(self.on_delete_clicked)
        self.create_grid_btn.clicked.connect(self.on_new_grid_clicked)
        self.create_3d_grid_btn.clicked.connect(self.on_create_grid_clicked)
        self.scan_btn.clicked.connect(self.on_scan_clicked)
        
        # --- 3. 设置窗口布局 ---
        # 主布局
        main_layout = QHBoxLayout()
        
        # 左侧布局（绘图区和按钮）
        left_layout = QVBoxLayout()
        left_layout.addWidget(self.plotter_widget)
        
        # 按钮布局 - 所有控件在同一行
        button_layout = QHBoxLayout()
        button_layout.addLayout(speed_acc_layout)
        button_layout.addWidget(self.add_btn)
        button_layout.addWidget(self.create_3d_grid_btn)
        button_layout.addWidget(self.scan_btn)
        
        left_layout.addLayout(button_layout)
        left_layout.addWidget(self.status_label)
        
        # 右侧布局（表格和方向控制）
        right_layout = QVBoxLayout()

        # 添加第一个表格（点坐标）
        right_layout.addWidget(QLabel("添加点坐标表:"))
        right_layout.addWidget(self.table_view)
        
        # 添加按钮在两个表格之间
        new_grid_layout = QHBoxLayout()
        new_grid_layout.addWidget(self.update_btn)
        new_grid_layout.addWidget(self.delete_btn)
        new_grid_layout.addWidget(self.create_grid_btn)
        new_grid_layout.addStretch()
        right_layout.addLayout(new_grid_layout)

        # 添加第二个表格（网格点）
        self.grid_model = QStandardItemModel(0, 4)  # 4列: No, X, Y, Z
        self.grid_model.setHorizontalHeaderLabels(["No", "X", "Y", "Z"])
        self.grid_table_view = QTableView()
        self.grid_table_view.setModel(self.grid_model)
        self.grid_table_view.horizontalHeader().setSectionResizeMode(QHeaderView.ResizeMode.Stretch)
        self.grid_table_view.setSelectionBehavior(QTableView.SelectionBehavior.SelectRows)

        # 连接网格表格双击事件
        self.grid_table_view.doubleClicked.connect(self.on_grid_table_right_clicked)

        # 添加网格表格标签和视图
        right_layout.addWidget(QLabel("生成网格点表:"))
        right_layout.addWidget(self.grid_table_view)

        # 方向控制面板 - 使用水平布局放置XY控制和Z控制
        navigation_layout = QHBoxLayout()
        # 移除"移动控制:"标签
        # right_layout.addWidget(QLabel("移动控制:"))
        
        # XY控制面板 - 使用垂直布局
        xy_control_layout = QVBoxLayout()
        
        # 添加X-Y轴控制标签，与Z轴控制标签保持一致
        xy_title_layout = QHBoxLayout()
        xy_title_layout.addWidget(QLabel("X-Y轴控制:"))
        xy_control_layout.addLayout(xy_title_layout)
        
        # 创建圆形按钮布局 - 更紧凑的布局
        direction_grid = QGridLayout()
        direction_grid.setSpacing(2)  # 减小按钮间距
        
        # 创建所有方向按钮
        self.up_left_btn = QPushButton("↖")
        self.up_btn = QPushButton("↑")
        self.up_right_btn = QPushButton("↗")
        self.left_btn = QPushButton("←")
        self.central_btn = QPushButton("○")
        self.right_btn = QPushButton("→")
        self.down_left_btn = QPushButton("↙")
        self.down_btn = QPushButton("↓")
        self.down_right_btn = QPushButton("↘")
        
        # 设置所有按钮为圆形 - 更紧凑的尺寸
        direction_buttons = [
            self.up_left_btn, self.up_btn, self.up_right_btn,
            self.left_btn, self.central_btn, self.right_btn,
            self.down_left_btn, self.down_btn, self.down_right_btn
        ]
        
        for btn in direction_buttons:
            btn.setMinimumSize(36, 36)
            btn.setMaximumSize(36, 36)
            btn.setStyleSheet("""
                QPushButton {
                    border-radius: 18px;
                    background-color: #E0E0E0;
                    color: black;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: #C0C0C0;
                }
                QPushButton:pressed {
                    background-color: #A0A0A0;
                }
            """)
        
        # 添加按钮到网格布局
        direction_grid.addWidget(self.up_left_btn, 0, 0)
        direction_grid.addWidget(self.up_btn, 0, 1)
        direction_grid.addWidget(self.up_right_btn, 0, 2)
        direction_grid.addWidget(self.left_btn, 1, 0)
        direction_grid.addWidget(self.central_btn, 1, 1)
        direction_grid.addWidget(self.right_btn, 1, 2)
        direction_grid.addWidget(self.down_left_btn, 2, 0)
        direction_grid.addWidget(self.down_btn, 2, 1)
        direction_grid.addWidget(self.down_right_btn, 2, 2)
        
        # 连接所有方向按钮信号
        self.up_btn.clicked.connect(self.on_up_clicked)
        self.down_btn.clicked.connect(self.on_down_clicked)
        self.left_btn.clicked.connect(self.on_left_clicked)
        self.right_btn.clicked.connect(self.on_right_clicked)
        self.up_left_btn.clicked.connect(self.on_up_left_clicked)
        self.up_right_btn.clicked.connect(self.on_up_right_clicked)
        self.down_left_btn.clicked.connect(self.on_down_left_clicked)
        self.down_right_btn.clicked.connect(self.on_down_right_clicked)
        self.central_btn.clicked.connect(self.on_central_clicked)
        
        xy_control_layout.addLayout(direction_grid)
        
        # Z轴控制布局 - 改为与XY控件对齐
        z_control_layout = QVBoxLayout()
        z_control_layout.setAlignment(Qt.AlignCenter)
        
        # 添加Z标签标题
        z_title_layout = QHBoxLayout()
        z_title_layout.addWidget(QLabel("Z轴控制:"))
        z_control_layout.addLayout(z_title_layout)
        
        # 创建一个水平布局，左侧是Z轴按钮，右侧是Z标签
        z_horizontal_layout = QHBoxLayout()
        
        # 创建Z轴按钮网格
        z_grid = QGridLayout()
        z_grid.setSpacing(2)  # 减小按钮间距，保持与XY方向键相同
        
        # 创建Z轴按钮
        self.z_up_btn = QPushButton("▲")
        self.z_down_btn = QPushButton("▼")
        self.z_center_btn = QPushButton("●")
        
        
        
        self.z_up_btn.setMinimumSize(36, 36)
        self.z_up_btn.setMaximumSize(36, 36)
        self.z_down_btn.setMinimumSize(36, 36)
        self.z_down_btn.setMaximumSize(36, 36)
        self.z_center_btn.setMinimumSize(36, 36)
        self.z_center_btn.setMaximumSize(36, 36)
        
        self.z_up_btn.setStyleSheet("""
            QPushButton {
                border-radius: 18px;
                background-color: #E0E0E0;
                color: black;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #C0C0C0;
            }
            QPushButton:pressed {
                background-color: #A0A0A0;
            }
        """)
        self.z_down_btn.setStyleSheet("""
            QPushButton {
                border-radius: 18px;
                background-color: #E0E0E0;
                color: black;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #C0C0C0;
            }
            QPushButton:pressed {
                background-color: #A0A0A0;
            }
        """)
        self.z_center_btn.setStyleSheet("""
            QPushButton {
                border-radius: 18px;
                background-color: #E0E0E0;
                color: black;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #C0C0C0;
            }
            QPushButton:pressed {
                background-color: #A0A0A0;
            }
        """)
        
        
        # 将Z上键与Y上键对齐
        z_grid.addWidget(self.z_up_btn, 0, 0)
        # 在中间放Z轴居中按钮
        z_grid.addWidget(self.z_center_btn, 1, 0)
        # 将Z下键与Y下键对齐
        z_grid.addWidget(self.z_down_btn, 2, 0)
        
        # 连接Z轴按钮信号
        self.z_up_btn.clicked.connect(self.on_z_up_clicked)
        self.z_down_btn.clicked.connect(self.on_z_down_clicked)
        self.z_center_btn.clicked.connect(self.on_z_center_clicked)
        
        # 将Z按钮网格和Z标签添加到水平布局
        z_horizontal_layout.addLayout(z_grid)
        
        # 添加回零控件布局
        home_layout = QVBoxLayout()
        
        # 添加"回零"标签
        home_label = QLabel("回零")
        home_label.setAlignment(Qt.AlignCenter)
        home_layout.addWidget(home_label)
        
        # 创建三个回零按钮
        self.x_home_btn = QPushButton("X回零")
        self.y_home_btn = QPushButton("Y回零")
        self.z_home_btn = QPushButton("Z回零")
        
        # 设置按钮样式和大小
        home_buttons = [self.x_home_btn, self.y_home_btn, self.z_home_btn]
        for btn in home_buttons:
            btn.setMinimumHeight(30)
            btn.setStyleSheet("""
                QPushButton {
                    background-color: #E0E0E0;
                    color: black;
                }
                QPushButton:hover {
                    background-color: #C0C0C0;
                }
                QPushButton:pressed {
                    background-color: #A0A0A0;
                }
            """)
        
        # 添加按钮到回零布局
        home_layout.addWidget(self.x_home_btn)
        home_layout.addWidget(self.y_home_btn)
        home_layout.addWidget(self.z_home_btn)
        
        # 连接按钮信号
        self.x_home_btn.clicked.connect(self.on_x_home_clicked)
        self.y_home_btn.clicked.connect(self.on_y_home_clicked)
        self.z_home_btn.clicked.connect(self.on_z_home_clicked)
        
        # 将回零布局添加到水平布局
        z_horizontal_layout.addLayout(home_layout)
        z_control_layout.addLayout(z_horizontal_layout)
        
        # 将XY控制和Z控制添加到水平布局
        navigation_layout.addLayout(xy_control_layout)
        # 添加水平间距
        navigation_layout.addSpacing(20)  # 在XY和Z控件之间添加20像素的间距
        navigation_layout.addLayout(z_control_layout)
        right_layout.addLayout(navigation_layout)
        
        # 步距选择 - 添加到右侧面板的Y轴下方
        step_layout = QVBoxLayout()
        
        # 添加步距标签
        step_label_layout = QHBoxLayout()
        step_label_layout.addWidget(QLabel("步距xy:mm, z:100um:"))
        step_layout.addLayout(step_label_layout)
        
        # 创建步距按钮布局
        step_buttons_layout = QHBoxLayout()
        self.step_buttons = []
        self.step_values = ["0.001", "0.01", "0.1", "1", "2", "5"]
        for step_value in self.step_values:
            btn = QPushButton(step_value)
            btn.setMinimumWidth(40)
            btn.setCheckable(True)
            btn.clicked.connect(lambda checked, v=step_value: self.on_step_selected(v))
            self.step_buttons.append(btn)
            step_buttons_layout.addWidget(btn)
        
        # 默认选中0.1
        self.step_buttons[2].setChecked(True)
        self.current_step = 0.1
        
        step_layout.addLayout(step_buttons_layout)
        right_layout.addLayout(step_layout)
        main_layout.addLayout(left_layout, 2)  # 占用2/3的空间
        main_layout.addLayout(right_layout, 1)  # 占用1/3的空间
        self.setLayout(main_layout)

        # --- 4. 设置后台工作线程 ---
        self.setup_worker_and_connections()

        # --- 7. 调整窗口大小和初始状态 ---
        self.resize(900, 600)  # 调整窗口大小以适应表格

        # 表格中当前的行数（用于No列）
        self.point_counter = 1

        # 存储生成的网格点 - 物理单位(mm/μm)
        self.grid_points = []
        # 存储生成的网格点 - 脉冲单位，用于直接移动
        self.grid_points_pulse = []
        # 当前扫描的点索引
        self.current_scan_index = 0
        # 扫描计时器
        self.scan_timer = QTimer(self)
        self.scan_timer.timeout.connect(self.scan_next_position)
        
        # 扫描计时相关
        self.scan_start_time = None

        # 添加网格尺寸参数，默认值为5x倍率下的图片尺寸
        config = get_config()
        self.grid_width = config.scan.default_grid_width   # 网格宽度 (mm)
        self.grid_height = config.scan.default_grid_height  # 网格高度 (mm)
        self.grid_overlap = config.scan.default_overlap  # 网格重合度

        # 添加当前坐标属性
        self.current_x_mm = 0.0
        self.current_y_mm = 0.0
        self.current_z_micron = 0.0  # Z轴位置，单位为微米
        
        # 加载上次保存的状态
        self.load_state()

    def setup_worker_and_connections(self):
        """设置工作线程和信号连接"""
        if not self.motor_controller or not self.motor_controller.is_initialized:
            self.status_label.setText("状态: 错误 (控制器未就绪)")
            return
            
        self.worker_thread = QThread(self)
        self.motor_worker = MotorWorker(self.motor_controller)
        self.motor_worker.moveToThread(self.worker_thread)

        # --- 连接信号与槽 ---
        self.requestMove.connect(self.motor_worker.perform_move)
        self.requestPosition.connect(self.motor_worker.request_position_update)
        self.motor_worker.movementFinished.connect(self.on_movement_finished)
        self.motor_worker.positionUpdated.connect(self.plotter_widget.update_actual_position)
        self.plotter_widget.targetPositionClicked.connect(self.on_plotter_clicked)

        self.worker_thread.start()
        
        self.status_label.setText("状态: 就绪。请在绘图区点击。")
        
       
        if hasattr(self, 'scan_speed_edit') and hasattr(self, 'scan_acc_edit'):
            speed = int(self.scan_speed_edit.text())
            acc = int(self.scan_acc_edit.text())
            for motor_id in self.motor_controller.motor_sum:
                self.motor_controller.set_speed(motor_id, speed, acc)
        self.apply_scan_speed_settings()
        print("主线程: 获取初始位置...")
        QTimer.singleShot(500, lambda: self.requestPosition.emit(-1))

    # --- 辅助方法 ---
    def sync_table_to_plot(self):
        """将表格中的点同步到绘图区"""
        points = []
        for row in range(self.table_model.rowCount()):
            try:
                x = float(self.table_model.item(row, 1).text())
                y = float(self.table_model.item(row, 2).text())
                points.append((x, y))
            except (ValueError, AttributeError):
                print(f"警告: 表格行 {row+1} 包含无效数据")
        self.plotter_widget.update_user_points(points)

    # --- 定义槽函数 ---
    @Slot(QPointF)
    def on_plotter_clicked(self, target_pos_mm: QPointF):
        """处理绘图区点击事件，移动到目标位置"""
        if not self.motor_controller or not self.motor_controller.is_initialized:
            self.status_label.setText("状态: 错误 (控制器未就绪)")
            return
            
        x_mm = target_pos_mm.x()
        y_mm = target_pos_mm.y()
        x_pulse = CoordinateConverter.physical_to_pulse(x_mm, Axis.X)
        y_pulse = CoordinateConverter.physical_to_pulse(y_mm, Axis.Y)

        try:
            current_z_pulse = self.motor_controller.get_position(Axis.Z)
            if current_z_pulse is None:
                current_z_pulse = CoordinateConverter.physical_to_pulse(self.plotter_widget.current_z_micron, Axis.Z)
        except Exception:
            current_z_pulse = 0

        config = get_config()
        soft_limits = config.motor.soft_limit_pulse
        x_min, x_max = soft_limits[0]
        y_min, y_max = soft_limits[1]
        
        if not (x_min <= x_pulse <= x_max):
            QMessageBox.warning(self, "目标无效", f"X位置 ({x_mm:.2f}mm) 超出限位范围")
            return
            
        if not (y_min <= y_pulse <= y_max):
            QMessageBox.warning(self, "目标无效", f"Y位置 ({y_mm:.2f}mm) 超出限位范围")
            return
        
        self.plotter_widget.update_target_marker(target_pos_mm)
        self.status_label.setText(f"状态: 移动至 X={x_mm:.2f}, Y={y_mm:.2f}...")
        self.requestMove.emit(x_pulse, y_pulse, current_z_pulse)

    def closeEvent(self, event):
        # 保存当前状态
        self.save_state()
        
        if self.worker_thread.isRunning():
            print("正在停止工作线程...")
            self.worker_thread.quit()
            if not self.worker_thread.wait(3000):
                print("警告：工作线程未能正常退出，将强制终止。")
                self.worker_thread.terminate()
            else:
                print("工作线程已成功停止。")

        if self.motor_controller and self.motor_controller.is_initialized:
            for motor_id in self.motor_controller.motor_sum:
                self.motor_controller.power_off(motor_id)
                time.sleep(0.05)
            self.motor_controller.stop()
        event.accept()
        
    def save_state(self):
        """保存用户添加的坐标点和应用设置到文件"""
        try:
            # 收集用户添加的点
            user_points = []
            for row in range(self.table_model.rowCount()):
                try:
                    point_id = self.table_model.item(row, 0).text()
                    x = float(self.table_model.item(row, 1).text())
                    y = float(self.table_model.item(row, 2).text())
                    z = float(self.table_model.item(row, 3).text())
                    user_points.append({"id": point_id, "x": x, "y": y, "z": z})
                except (ValueError, AttributeError, IndexError) as e:
                    print(f"保存点 {row} 时出错: {e}")
            
            # 收集扫描设置
            scan_settings = {
                "speed": self.scan_speed_edit.text(),
                "acc": self.scan_acc_edit.text(),
                "overlap": self.overlap_edit.text(),
                "pattern": self.scan_pattern_combo.currentIndex()
            }
            
            # 创建状态数据
            state = {
                "user_points": user_points,
                "scan_settings": scan_settings,
                "point_counter": self.point_counter,
                "current_step": self.current_step,
                "version": "1.0"  # 添加版本号方便将来兼容性处理
            }
            
            # 保存到临时文件，然后重命名，避免保存过程中断导致数据损坏
            temp_file = f"{self.state_file}.tmp"
            with open(temp_file, 'w') as f:
                json.dump(state, f, indent=2)
                
            # 确保写入完成后原子性地重命名文件
            if os.path.exists(self.state_file):
                backup_file = f"{self.state_file}.bak"
                if os.path.exists(backup_file):
                    os.remove(backup_file)
                os.rename(self.state_file, backup_file)
                
            os.rename(temp_file, self.state_file)
            print(f"状态已保存到: {self.state_file}")
            
        except Exception as e:
            print(f"保存状态时出错: {e}")
            traceback.print_exc()
    
    def load_state(self):
        """从文件加载用户添加的坐标点和应用设置"""
        if not os.path.exists(self.state_file):
            # 检查是否有备份文件
            backup_file = f"{self.state_file}.bak"
            if os.path.exists(backup_file):
                print(f"使用备份状态文件: {backup_file}")
                self.state_file = backup_file
            else:
                print(f"状态文件不存在: {self.state_file}")
                return
        
        try:
            with open(self.state_file, 'r') as f:
                state = json.load(f)
            
            # 加载用户添加的点
            if "user_points" in state and isinstance(state["user_points"], list):
                self.table_model.setRowCount(0)  # 清空当前表格
                user_points = []
                
                for point in state["user_points"]:
                    if not isinstance(point, dict) or not all(k in point for k in ["id", "x", "y", "z"]):
                        continue  # 跳过无效数据
                        
                    try:
                        # 创建表格行
                        row = [
                            QStandardItem(str(point["id"])),
                            QStandardItem(f"{float(point['x']):.3f}"),
                            QStandardItem(f"{float(point['y']):.3f}"),
                            QStandardItem(f"{float(point['z']):.3f}")
                        ]
                        self.table_model.appendRow(row)
                        
                        # 添加到绘图点列表
                        user_points.append((float(point["x"]), float(point["y"])))
                    except (ValueError, KeyError) as e:
                        print(f"加载点数据时出错: {e}")
                
                # 同步到绘图区
                self.plotter_widget.update_user_points(user_points)
            
            # 加载扫描设置
            if "scan_settings" in state and isinstance(state["scan_settings"], dict):
                settings = state["scan_settings"]
                # 验证并设置速度
                config = get_config()
                try:
                    speed = int(settings.get("speed", config.motor.default_speed))
                    self.scan_speed_edit.setText(str(speed))
                except ValueError:
                    self.scan_speed_edit.setText(str(config.motor.default_speed))

                # 验证并设置加速度
                try:
                    acc = int(settings.get("acc", config.motor.default_acc_time))
                    self.scan_acc_edit.setText(str(acc))
                except ValueError:
                    self.scan_acc_edit.setText(str(config.motor.default_acc_time))

                # 验证并设置重叠度
                try:
                    overlap = int(settings.get("overlap", int(config.scan.default_overlap * 100)))
                    self.overlap_edit.setText(str(overlap))
                except ValueError:
                    self.overlap_edit.setText(str(int(config.scan.default_overlap * 100)))
                
                # 验证并设置扫描模式
                try:
                    pattern = int(settings.get("pattern", 0))
                    if 0 <= pattern <= 1:  # 只有两种扫描模式
                        self.scan_pattern_combo.setCurrentIndex(pattern)
                except ValueError:
                    self.scan_pattern_combo.setCurrentIndex(0)
            
            # 加载计数器
            if "point_counter" in state:
                try:
                    self.point_counter = max(1, int(state["point_counter"]))
                except ValueError:
                    self.point_counter = 1
            
            # 加载步进值
            if "current_step" in state:
                try:
                    step_value = float(state["current_step"])
                    step_str = str(step_value)
                    # 查找匹配的步进按钮
                    button_found = False
                    for btn in self.step_buttons:
                        if btn.text() == step_str:
                            btn.setChecked(True)
                            button_found = True
                            break
                    
                    if not button_found and self.step_buttons:
                        # 默认选中第一个按钮
                        self.step_buttons[0].setChecked(True)
                        step_value = float(self.step_buttons[0].text())
                    
                    self.current_step = step_value
                except ValueError:
                    # 默认选择第一个按钮
                    if self.step_buttons:
                        self.step_buttons[0].setChecked(True)
                        self.current_step = float(self.step_buttons[0].text())
            
            print(f"状态已加载: {len(state.get('user_points', [])) if 'user_points' in state else 0} 个用户点")
            
        except json.JSONDecodeError as e:
            print(f"解析状态文件时出错: {e}")
            # 尝试使用备份文件
            backup_file = f"{self.state_file}.bak"
            if os.path.exists(backup_file):
                print(f"尝试使用备份文件: {backup_file}")
                self.state_file = backup_file
                self.load_state()  # 递归尝试加载备份
            
        except Exception as e:
            print(f"加载状态时出错: {e}")
            traceback.print_exc()

    # --- 按钮处理函数 ---
    def on_add_clicked(self):
        """将当前坐标添加到表格中"""
        # 同步当前位置
        self.update_position_from_plotter()
        
        # 获取当前实际位置
        x = self.current_x_mm
        y = self.current_y_mm
        z = self.current_z_micron
        
        print(f"添加点坐标: X={x:.3f}mm, Y={y:.3f}mm, Z={z:.3f}微米")
        
        # 创建表格行
        row = [
            QStandardItem(str(self.point_counter)),
            QStandardItem(f"{x:.3f}"),
            QStandardItem(f"{y:.3f}"),
            QStandardItem(f"{z:.3f}")  # Z坐标
        ]
        
        # 添加到表格
        self.table_model.appendRow(row)
        
        # 更新状态标签
        self.status_label.setText(f"状态: 已添加点 #{self.point_counter} ({x:.3f}, {y:.3f}, {z:.3f})")
        self.point_counter += 1
        
        # 同步到绘图区
        self.sync_table_to_plot()
        
    
    def on_delete_clicked(self):
        """删除所选坐标点"""
        indexes = self.table_view.selectionModel().selectedRows()
        if not indexes:
            self.status_label.setText("状态: 请先选择要删除的点")
            return
            
        # 从后向前删除，以避免索引变化
        rows = sorted([index.row() for index in indexes], reverse=True)
        for row in rows:
            self.table_model.removeRow(row)
            
        self.status_label.setText(f"状态: 已删除选中的点")
        
        # 同步到绘图区
        self.sync_table_to_plot()
        
    
    def move_z_direction(self, z_dir: int):
        """Z轴方向移动"""
        # 检查Z轴电机连接状态
        if not self.motor_controller.axis_connected.get(Axis.Z, False):
            self.status_label.setText("状态: Z轴电机未连接")
            return False
        
        # 计算Z轴目标位置 - 步距值对应Z轴100微米
        z_micron = self.plotter_widget.current_z_micron + (z_dir * self.current_step * 100)
        
        # 获取当前XY位置
        current_pos = self.plotter_widget.actual_pos_mm
        
        # 检查Z轴限位
        z_pulse = CoordinateConverter.physical_to_pulse(z_micron, Axis.Z)
        config = get_config()
        soft_limits = config.motor.soft_limit_pulse
        z_min, z_max = soft_limits[Axis.Z]
        if not (z_min <= z_pulse <= z_max):
            self.status_label.setText(f"状态: Z轴目标位置超出限位范围")
            return False
            
        # 移动到目标位置
        direction_text = "向上" if z_dir < 0 else "向下"
        self.status_label.setText(f"Z轴{direction_text}移动中...")
        return self.move_to_position(x_mm=current_pos.x(), y_mm=current_pos.y(), z_micron=z_micron)

    def on_create_grid_clicked(self):
        """创建3D网格点阵并显示在当前坐标系中"""
        # 首先清除旧的网格
        self.plotter_widget.clear_grid()
        
        # 检查表格中是否有至少三个点且包含Z坐标
        if self.table_model.rowCount() < 2:
            self.status_label.setText("状态: 需要至少三个点来创建3D网格插值，请先添加更多点")
            return
            
        # 从表格中获取3D坐标点
        points_3d = []
        for row in range(self.table_model.rowCount()):
            try:
                x = float(self.table_model.item(row, 1).text())
                y = float(self.table_model.item(row, 2).text())
                z = float(self.table_model.item(row, 3).text())
                points_3d.append((x, y, z))
            except (ValueError, AttributeError) as e:
                print(f"警告: 表格行 {row+1} 包含无效数据: {e}")
        
        # 创建网格实例，使用当前设置的网格尺寸
        grid_width = self.grid_width   # 使用主窗口设置的网格宽度
        grid_height = self.grid_height  # 使用主窗口设置的网格高度
        try:
            overlap = float(self.overlap_edit.text()) / 100.0  # 从百分比转换为小数
        except ValueError:
            config = get_config()
            overlap = config.scan.default_overlap  # 默认值10%
            self.overlap_edit.setText("10")
        
        print(f"创建3D网格: 宽度={grid_width}mm, 高度={grid_height}mm, 重合度={overlap}")
        
        grid = Grid(width=grid_width, height=grid_height, overlap=overlap)
        
        # 使用选择的扫描路径
        scan_path_index = self.scan_pattern_combo.currentIndex()  # 0代表蛇形，1代表之形
        use_zigzag = scan_path_index == 1
        
        # 创建3D网格
        grid_3d = grid.create_3d_grid(points_3d, use_zigzag)
        
        # 获取扫描路径名称
        scan_path_name = "之形" if use_zigzag else "蛇形"
        
        # 提取2D网格用于显示
        grid_2d = [[(x, y) for x, y, z in row] for row in grid_3d]
        
        # 直接在当前绘图控件中显示网格
        self.plotter_widget.display_grid(grid_2d, grid_width, grid_height)
        
        # 将3D网格点展平并存储
        self.grid_points = [(x, y, z) for row in grid_3d for x, y, z in row]
        
        # 预先计算脉冲值 - 这是优化的关键部分
        self.grid_points_pulse = []
        for x, y, z in self.grid_points:
            x_pulse, y_pulse, z_pulse = CoordinateConverter.xyz_physical_to_pulse(x, y, z)
            self.grid_points_pulse.append((x_pulse, y_pulse, z_pulse))
            
        print(f"生成3D网格点 {len(self.grid_points)} 个，并预计算了对应的脉冲值")
        
        # 更新绘图区中的点 - 使用绿色点显示网格点
        self.plotter_widget.update_table_points([(x, y) for x, y, z in self.grid_points])
        
        # 更新网格表格
        self.update_grid_table()
        
        # 计算网格的行数和列数
        rows = len(grid_3d)
        cols = len(grid_3d[0]) if rows > 0 else 0
        total_points = len(self.grid_points)
        
        # 更新状态显示行列信息
        self.status_label.setText(f"状态: 已创建{scan_path_name}网格点阵, {rows}行 × {cols}列, 共 {total_points} 个点")
        
        # 重置扫描索引
        self.current_scan_index = 0
    
    def on_move_to_clicked(self):
        """移动到表格中选中的坐标点"""
        # 获取选中的行
        indexes = self.table_view.selectionModel().selectedRows()
        if not indexes:
            self.status_label.setText("状态: 请先选择要移动到的点")
            return
            
        # 获取所选行的第一行
        row = indexes[0].row()
        
        try:
            # 获取X、Y和Z坐标
            x_mm = float(self.table_model.item(row, 1).text())
            y_mm = float(self.table_model.item(row, 2).text())
            z_um = float(self.table_model.item(row, 3).text())
            
            # 计算脉冲值
            x_pulse = CoordinateConverter.physical_to_pulse(x_mm, Axis.X)
            y_pulse = CoordinateConverter.physical_to_pulse(y_mm, Axis.Y)
            z_pulse = CoordinateConverter.physical_to_pulse(z_um, Axis.Z)
            
            # 创建并更新目标位置
            target_pos = QPointF(x_mm, y_mm)
            self.plotter_widget.update_target_marker(target_pos)
            
            # 使用move_to_xyz_position方法直接移动XYZ轴
            try:
                self.plotter_widget.setEnabled(False)  # 禁用界面
                self.status_label.setText(f"状态: 移动到点 ({x_mm:.3f}, {y_mm:.3f}, {z_um:.1f})...")
                
                # 直接调用move_to_xyz_position实现XYZ同步移动
                self.motor_controller.move_to_xyz_position(x_pulse, y_pulse, z_pulse)
                
                # 移动完成后解锁界面
                self.plotter_widget.setEnabled(True)
                self.status_label.setText(f"状态: 已移动到点 ({x_mm:.3f}, {y_mm:.3f}, {z_um:.1f})")
                
                # 手动触发移动完成信号
                if hasattr(self, 'movementFinished'):
                    self.movementFinished.emit()
                    
            except Exception as e:
                self.status_label.setText(f"状态: 移动错误! ({str(e)})")
                print(f"移动到坐标 ({x_mm:.3f}, {y_mm:.3f}, {z_um:.1f}) 时出错: {e}")
                self.plotter_widget.setEnabled(True)
                
                # 如果出错，使用旧的方法作为后备
                self.on_plotter_clicked(target_pos)
            
        except (ValueError, IndexError) as e:
            self.status_label.setText(f"状态: 无法移动到选定点: {e}")
    

    def set_motor_controller(self, controller):
        """设置电机控制器并初始化工作线程"""
        self.motor_controller = controller
        
        if controller and controller.is_initialized:
            self.setup_worker_and_connections()
            self.plotter_widget.setEnabled(True)
        else:
            self.status_label.setText("状态: 错误！控制器未初始化。")
            self.plotter_widget.setEnabled(False)

    def update_grid_table(self):
        """更新网格表格数据"""
        # 清空现有数据
        self.grid_model.setRowCount(0)
        
        # 检查是否有3D点（包含Z坐标）
        has_z_coords = False
        if self.grid_points and len(self.grid_points[0]) == 3:
            has_z_coords = True
        
        # 添加数据到表格
        for i, point in enumerate(self.grid_points):
            if has_z_coords:
                x, y, z = point
                row = [
                    QStandardItem(str(i+1)),
                    QStandardItem(f"{x:.3f}"),
                    QStandardItem(f"{y:.3f}"),
                    QStandardItem(f"{z:.3f}")  # 使用实际的Z坐标
                ]
            else:
                x, y = point
                row = [
                    QStandardItem(str(i+1)),
                    QStandardItem(f"{x:.3f}"),
                    QStandardItem(f"{y:.3f}"),
                    QStandardItem("0.000")  # Z坐标默认为0
                ]
            self.grid_model.appendRow(row)

    def on_grid_table_right_clicked(self, index):
        """处理网格表格双击事件，移动到选中的网格点"""
        if not self.motor_controller or not self.motor_controller.is_initialized:
            self.status_label.setText("状态: 错误 (控制器未就绪)")
            return
            
        # 获取选中的行
        row = index.row()
        if row >= len(self.grid_points):
            return
        
        # 高亮显示当前选中点
        self.plotter_widget.highlight_current_point(row)
        
        # 确保表格行被选中
        self.grid_table_view.selectRow(row)
        self.current_scan_index = row
        
        # 获取网格点信息
        x_mm = float(self.grid_model.item(row, 1).text())
        y_mm = float(self.grid_model.item(row, 2).text())
        z_um = float(self.grid_model.item(row, 3).text())
        
        # 直接使用物理坐标移动
        self.move_to_position(x_mm, y_mm, z_um)

    # --- 方向控制 - 优化版 ---
    def on_up_clicked(self): self.move_direction(0, -1)
    def on_down_clicked(self): self.move_direction(0, 1)
    def on_left_clicked(self): self.move_direction(1, 0)
    def on_right_clicked(self): self.move_direction(-1, 0)
    def on_up_left_clicked(self): self.move_direction(1, -1)
    def on_up_right_clicked(self): self.move_direction(-1, -1)
    def on_down_left_clicked(self): self.move_direction(1, 1)
    def on_down_right_clicked(self): self.move_direction(-1, 1)
        
    def on_central_clicked(self):
        """回到中心位置"""
        config = get_config()
        self.move_to_position(config.motor.center_x_mm, config.motor.center_y_mm)
        
    def on_z_up_clicked(self): self.move_z_direction(-1)
    def on_z_down_clicked(self): self.move_z_direction(1)
        
    def on_z_center_clicked(self):
        """Z轴居中"""
        if not self.motor_controller or not self.motor_controller.is_initialized:
            self.status_label.setText("状态: 错误 (控制器未就绪)")
            return
            
        if not self.motor_controller.axis_connected[Axis.Z]:
            self.status_label.setText("状态: Z轴电机未连接")
            return
            
        # 获取当前XY位置
        x_mm = self.plotter_widget.actual_pos_mm.x()
        y_mm = self.plotter_widget.actual_pos_mm.y()
        
        # 移动到Z轴中心位置
        config = get_config()
        self.move_to_position(x_mm, y_mm, config.motor.center_z_micron)

    def _home_axis(self, axis: Axis, axis_name: str):
        
        try:
            self.status_label.setText(f"状态: {axis_name}轴正在回零...")
            
            # 保存当前速度设置
            original_speed = int(self.scan_speed_edit.text())
            
            # 使用固定的回零速度
            self.motor_controller.back_zero(axis, home_speed=500)
            
            # 恢复原来的速度
            self.motor_controller.set_speed(axis, original_speed, int(self.scan_acc_edit.text()))
            
            self.status_label.setText(f"状态: {axis_name}轴回零完成")
            # 更新位置显示
            self.requestPosition.emit(-1)
        except Exception as e:
            self.status_label.setText(f"状态: {axis_name}轴回零出错: {str(e)}")
            print(f"{axis_name}轴回零错误: {e}")

    def on_x_home_clicked(self): self._home_axis(Axis.X, "X")
    def on_y_home_clicked(self): self._home_axis(Axis.Y, "Y")
    def on_z_home_clicked(self): self._home_axis(Axis.Z, "Z")

    def on_step_selected(self, step_value):
        """
        选择步距
        
        Args:
            step_value: 选中的步距值
        """
        self.current_step = float(step_value)  
        # 更新按钮状态
        for btn in self.step_buttons:
            btn.setChecked(btn.text() == step_value)   
        self.status_label.setText(f"状态: 步距设置为 {step_value} mm")

    def apply_scan_speed_settings(self):
        """应用扫描速度和加速度设置到电机控制器"""
        if not self.motor_controller or not self.motor_controller.is_initialized:
            self.status_label.setText("状态: 错误 (控制器未就绪)")
            return      
        try:
            # 获取速度和加速度值
            speed = int(self.scan_speed_edit.text())
            acc = int(self.scan_acc_edit.text())
            
            # 应用到所有电机
            for motor_id in self.motor_controller.motor_sum:
                self.motor_controller.set_speed(motor_id, speed, acc)
                
            self.status_label.setText(f"状态: 速度 {speed} 和加速度 {acc} 已应用")
            print(f"扫描速度和加速度已应用，速度: {speed}, 加速度: {acc}")
        except Exception as e:
            self.status_label.setText(f"状态: 应用速度和加速度失败")
            print(f"应用扫描速度和加速度设置时出错: {e}")

    def show_table_context_menu(self, position):
        """显示表格的右键菜单"""
        # 获取选中的行
        selected_rows = self.table_view.selectionModel().selectedRows()
        if not selected_rows:
            return
            
        # 创建右键菜单
        menu = QMenu(self)
        move_to_action = menu.addAction("移动至")
        delete_action = menu.addAction("删除选中点")
        
        # 显示菜单并获取用户选择
        action = menu.exec(self.table_view.viewport().mapToGlobal(position))
        
        # 处理用户选择的操作
        if action == delete_action:
            # 从后向前删除，以避免索引变化
            rows = sorted([index.row() for index in selected_rows], reverse=True)
            for row in rows:
                self.table_model.removeRow(row)
            
            # 更新状态
            self.status_label.setText(f"状态: 已删除选中的点")
            
            # 同步到绘图区
            self.sync_table_to_plot()
        elif action == move_to_action:
            # 移动到选中的坐标点
            row = selected_rows[0].row()
            try:
                # 获取坐标值
                x_mm = float(self.table_model.item(row, 1).text())
                y_mm = float(self.table_model.item(row, 2).text())
                z_um = float(self.table_model.item(row, 3).text())
                
                # 使用move_to_position直接移动
                self.move_to_position(x_mm=x_mm, y_mm=y_mm, z_micron=z_um)
            except (ValueError, IndexError) as e:
                self.status_label.setText(f"状态: 无法移动到选定点: {e}")
                
    def on_new_grid_clicked(self):
        """清除数据点和网格"""
        # 清除绘图区域中的网格和点
        self.plotter_widget.clear_grid()
        
        # 清除表格数据
        self.table_model.setRowCount(0)
        self.grid_model.setRowCount(0)
        
        # 清除网格点列表
        self.grid_points = []
        
        # 更新绘图区
        self.plotter_widget.update_user_points([])
        self.plotter_widget.update_table_points([])
        
        # 更新状态
        self.status_label.setText("状态: 已清除所有数据点和网格")
        print("已清除所有数据点和网格")
        
        # 重置扫描索引
        self.current_scan_index = 0

    def on_update_clicked(self):
        """更新表格中选中行的坐标为当前位置"""
        # 获取选中的行
        indexes = self.table_view.selectionModel().selectedRows()
        if not indexes:
            self.status_label.setText("状态: 请先选择要更新的点")
            return  
        # 获取所选行的第一行
        row = indexes[0].row()
        # 同步当前位置
        self.update_position_from_plotter()
        
        # 获取当前实际位置
        x = self.current_x_mm
        y = self.current_y_mm
        z = self.current_z_micron
        
        # 更新表格中的坐标
        self.table_model.setItem(row, 1, QStandardItem(f"{x:.3f}"))
        self.table_model.setItem(row, 2, QStandardItem(f"{y:.3f}"))
        self.table_model.setItem(row, 3, QStandardItem(f"{z:.3f}"))
        
        # 更新状态标签
        point_num = self.table_model.item(row, 0).text()
        self.status_label.setText(f"状态: 已更新点 #{point_num} 为 ({x:.3f}, {y:.3f}, {z:.3f})")
        # 同步到绘图区
        self.sync_table_to_plot()

    def move_direction(self, x_dir: int, y_dir: int):
        """按指定方向移动电机"""
        # 获取当前位置
        current_pos = self.plotter_widget.actual_pos_mm
        x_mm = current_pos.x() + x_dir * self.current_step
        y_mm = current_pos.y() + y_dir * self.current_step
        # 使用统一的移动函数
        self.move_to_position(x_mm=x_mm, y_mm=y_mm)
     
    def move_to_position(self, x_mm, y_mm, z_micron=None):
        """Simplified movement function"""
        # Default Z to current position if not specified
        if z_micron is None:
            z_micron = self.plotter_widget.current_z_micron
            
        # Convert coordinates to pulse values
        x_pulse, y_pulse, z_pulse = CoordinateConverter.xyz_physical_to_pulse(x_mm, y_mm, z_micron)
        
        # Update UI and send request
        self.requestMove.emit(x_pulse, y_pulse, z_pulse)
        self.plotter_widget.update_target_marker(QPointF(x_mm, y_mm))
        return True

    def update_position_from_plotter(self):
        """从plotter_widget更新Stage窗口的坐标显示"""
        if hasattr(self, 'plotter_widget'):
            self.current_x_mm = self.plotter_widget.actual_pos_mm.x()
            self.current_y_mm = self.plotter_widget.actual_pos_mm.y()
            self.current_z_micron = self.plotter_widget.current_z_micron
            
            # 这里是旧的更新方式，如果 Stage 窗口有直接的 QLabel，可能需要更新
            # ... existing code ...

    # --- 方向控制和移动优化 ---
    
    @Slot()
    def on_movement_finished(self):
        """处理电机移动完成事件"""
        # 如果正在扫描，先进行拍照，然后移动到下一个点
        if hasattr(self, 'scan_btn') and self.scan_btn.text() == "Stop":
            # 检查是否有主窗口的拍照回调函数
            if hasattr(self, 'main_window') and self.main_window and hasattr(self.main_window, 'scan_capture_slot'):
                # 调用主窗口的拍照功能
                if callable(self.main_window.scan_capture_slot):
                    self.main_window.scan_capture_slot()
                    
            # 移动到下一个点
            self.current_scan_index += 1
            # 直接调用，不使用QTimer
            self.scan_next_position()
            return
        
        # 仅在非扫描状态下更新UI
        if hasattr(self, 'grid_table_view') and self.grid_table_view.selectionModel().hasSelection():
            row = self.grid_table_view.selectionModel().selectedRows()[0].row()
            if row < len(self.grid_points):
                x_mm = float(self.grid_model.item(row, 1).text())
                y_mm = float(self.grid_model.item(row, 2).text())
                self.status_label.setText(f"状态: 已移动到网格点 #{row+1} ({x_mm:.3f}, {y_mm:.3f})")
        
        # 通知其他组件移动已完成
        self.movementFinished.emit()

    def on_scan_clicked(self):
        """开始或停止扫描网格点阵列"""
        # 检查是否有网格点
        if not self.grid_points_pulse:
            self.status_label.setText("状态: 请先创建网格点")
            return
        
        button_text = self.scan_btn.text()
        total_points = len(self.grid_points)
        
        # 状态切换
        if button_text == "Scan":
            # 开始扫描
            self.scan_start_time = time.time()
            
            # 应用速度设置确保速度正确
            self.apply_scan_speed_settings()
            
            # 开始扫描第一个点
            self.current_scan_index = 0
            self.scan_next_position()
            
            status_msg = f"状态: 开始扫描网格点 (总计: {total_points}个点)"
            self.scan_btn.setText("Stop")
        else:  # Stop
            # 停止扫描
            status_msg = f"状态: 扫描已停止，当前位置: {self.current_scan_index}/{total_points}"
            self.scan_btn.setText("Scan")
            self.status_label.setText(status_msg)
            return
        
        self.status_label.setText(status_msg)

    def scan_next_position(self):
        """执行网格点扫描的下一个位置"""
        # 检查是否扫描完成
        if self.current_scan_index >= len(self.grid_points_pulse):
            self.complete_scan()
            return
            
        # 获取当前点信息，直接使用预计算的脉冲值提高效率
        index = self.current_scan_index
        x_pulse, y_pulse, z_pulse = self.grid_points_pulse[index]
        
        # 直接发送移动请求，不经过中间转换
        self.requestMove.emit(x_pulse, y_pulse, z_pulse)
        
        # 在表格中选择当前行
        self.grid_table_view.selectRow(index)
        
        # 在绘图区域高亮显示当前扫描点
        self.plotter_widget.highlight_current_point(index)
        
        x, y, z = self.grid_points[index]
        self.status_label.setText(f"状态: 扫描点 {index+1}/{len(self.grid_points)}: ({x:.3f}, {y:.3f}, {z:.1f})")

    def complete_scan(self):
        """完成扫描过程"""
        # 更新UI状态
        self.scan_btn.setText("Scan")
        
        # 计算并显示扫描时间
        if self.scan_start_time is not None:
            elapsed_time = time.time() - self.scan_start_time
            minutes, seconds = divmod(int(elapsed_time), 60)
            avg_time = elapsed_time / len(self.grid_points) if self.grid_points else 0
            total_points = len(self.grid_points)
            
            self.status_label.setText(
                f"状态: 扫描完成，共{total_points}个点，"
                f"耗时: {minutes}分{seconds}秒，平均每点: {avg_time:.3f}秒")
        else:
            self.status_label.setText(f"状态: 扫描完成，共{len(self.grid_points)}个点")
        
        # 清除UI状态
        self.plotter_widget.highlight_current_point(None)
        self.grid_table_view.clearSelection()
        
        # 先调用主窗口的完成扫描方法，再重置scan_start_time
        if hasattr(self, 'main_window') and self.main_window and hasattr(self.main_window, 'finish_scan'):
            if callable(self.main_window.finish_scan):
                self.main_window.finish_scan(self)
        
        # 重置扫描开始时间（在主窗口处理完成后）
        self.scan_start_time = None

# --- 程序主入口 ---
if __name__ == "__main__":
    # Configure pyqtgraph settings (optional, but recommended for Qt)
    pg.setConfigOptions(antialias=True)
    # !!! 修改这里为你实际的串口号 !!!
    TARGET_COM_PORT = 'com4' # 例如 'com3', '/dev/ttyUSB0' 等
    # 创建 QApplication 实例
    app = QApplication(sys.argv)
    
    # 尝试创建真实的电机控制器
    try:
        motor_controller = MotorController(TARGET_COM_PORT)
        if not motor_controller.is_initialized:
            print(f"错误：无法初始化电机控制器，请检查硬件连接")
            sys.exit(1)
    except Exception as e:
        print(f"初始化电机控制器时出错: {e}")
        print("请检查硬件连接和串口设置")
        sys.exit(1)
    
    # 创建主窗口实例，并传入电机控制器
    window = MainWindow(TARGET_COM_PORT)
    window.set_motor_controller(motor_controller)
    
    # 显示窗口并运行应用
    window.show()
    sys.exit(app.exec())