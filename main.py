#!/usr/bin/env python
# coding: utf-8
import sys
import traceback  # 添加traceback模块导入
from PySide6.QtWidgets import (QApplication, QMainWindow, QPushButton, QSlider, QLabel, 
                              QVBoxLayout, QHBoxLayout, QWidget, QTabWidget, QCheckBox,
                              QGroupBox, QLineEdit, QComboBox, QToolButton, QSplitter,
                              QFrame, QGridLayout, QScrollArea, QSizePolicy, QRadioButton,
                              QButtonGroup, QFileDialog, QMessageBox, QSpinBox, QProgressDialog,
                              QDoubleSpinBox)
from PySide6.QtGui import QImage, QPixmap, QIcon, QColor, QFont, QIntValidator, QDoubleValidator, QStandardItemModel, QStandardItem
from PySide6.QtCore import Qt, QTimer, QSize, QRect, QPointF, QThread, Signal, Slot, QMutex, QWaitCondition

# 引入stage模块和相机控制模块
import Stage
from Stage import Axis, Config  # 引入Axis枚举类型和Config类
import Camera
from TUCam import TUIMG_FORMATS
import time
import os
# 引入自动对焦模块
from Autofocus import PyTorchAutoFocus

# 相机线程类
class CameraThread(QThread):
    frame_ready = Signal(object)  # 图像帧信号
    fps_updated = Signal(float)   # FPS更新信号
    photo_captured = Signal(str, bool)  # 照片拍摄信号 (路径, 是否成功)
    
    def __init__(self, tucam):
        super().__init__()
        self.tucam = tucam
        self.running = False
        self.mutex = QMutex()
        self.frame_count = 0
        self.last_fps_time = time.time()
        
    def run(self):
        self.running = True
        self.last_fps_time = time.time()
        self.frame_count = 0
        
        try:
            while self.running:
                frame = self.tucam.get_frame()
                if frame is not None:
                    self.frame_ready.emit(frame)
                    
                    # FPS计算 - 每秒更新一次
                    self.frame_count += 1
                    current_time = time.time()
                    elapsed = current_time - self.last_fps_time
                    
                    if elapsed >= 1.0:
                        fps = self.frame_count / elapsed
                        self.fps_updated.emit(fps)
                        self.frame_count = 0
                        self.last_fps_time = current_time
                
                # 适度休眠，避免CPU占用过高
                #QThread.msleep(0.5)
        except Exception as e:
            print(f"相机线程错误: {e}")
            traceback.print_exc()
    
    def stop(self):
        self.mutex.lock()
        self.running = False
        self.mutex.unlock()
        self.wait(1000)  # 等待线程结束，最多1秒
        
    def capture_photo(self, save_path):
        """拍摄照片并保存
        
        Args:
            save_path: 保存文件路径
            
        Returns:
            bool: 通过信号返回是否成功
        """
        # 使用相机的SaveImage方法保存当前帧
        self.tucam.SaveImage(save_path)  
        # 发送信号通知结果
        self.photo_captured.emit(save_path)
        
       

class MicroscopeControlUI(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("显微镜控制")
        
        # 设置应用程序图标
        # self.setWindowIcon(QIcon("icons/microscope_icon.png"))
        
        # 初始化属性
        self.stage_window = None  # 舞台窗口引用
        self.motor_controller = None  # 电机控制器引用
        self.motor_connected = False  # 电机连接状态
        self.scan_capture_slot = None  # 扫描捕获回调函数
        self.is_scanning = False  # 添加扫描状态标志，用于在扫描时禁用对焦评分计算
        
        # 相机属性
        self.tucam = None  # 相机引用
        self.camera_open = False  # 相机打开状态
        self.camera_resolution = None  # 相机分辨率
        self.frame_error_count = 0  # 帧错误计数
        self.frame_count = 0  # 帧计数器用于对焦评分更新
        
        # 坐标信息
        self.current_x_mm = 0.0
        self.current_y_mm = 0.0
        self.current_z_um = 0.0
        
        # 自动对焦属性
        self.autofocus = None  # 自动对焦对象
        self.focus_method = "opencv_laplacian"  # 默认对焦方法
        self.current_focus_btn = None  # 当前选中的对焦按钮
        
        # 初始化线程属性
        self.camera_thread = None  # 相机线程
        
        # 初始化UI
        self.initUI()
        
        # 设置更新定时器
        self.update_position_timer = QTimer()
        self.update_position_timer.timeout.connect(self.update_motor_position)
        self.update_position_timer.start(500)  # 每500ms更新一次位置显示
        
        # 初始化相机
        try:
            self.init_camera()
        except Exception as e:
            self.camera_status_label.setText(f"相机状态: 初始化失败")
            self.camera_status_label.setStyleSheet("color: red;")
            print(f"相机初始化出错: {e}")
            self.camera_open = False
            
        # 自动连接电机
        QTimer.singleShot(100, self.auto_connect_motors)  # 延迟500ms后自动连接电机
        
        # 设置默认图片格式为JPG
        if hasattr(self, 'format_combo'):
            self.format_combo.setCurrentIndex(0)  # JPG是第一个选项
        
        # 状态文件路径
        self.state_file = os.path.join(os.path.dirname(os.path.abspath(__file__)), "microscope_state.json")
        
        # 储存加载的状态
        self.saved_stage_points = None
        
        # 加载上次保存的状态
        self.load_state()
    
    def initUI(self):
        self.setWindowTitle("显微镜控制系统")
        self.setGeometry(50, 50, 1500, 1100)
        
        # 设置单选按钮样式 - 空心样式，选中时才有颜色
        self.setStyleSheet("""
            QRadioButton::indicator {
                width: 15px;
                height: 15px;
                border-radius: 7px;
                border: 2px solid #444;
            }
            QRadioButton::indicator:checked {
                border: 2px solid #e93d3d;
                background-color: #e93d3d;
            }
        """)
        
        # 创建主部件和布局
        main_widget = QWidget()
        self.setCentralWidget(main_widget)
        main_layout = QHBoxLayout(main_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        
        # 创建左侧控制面板和右侧图像显示区的分割器
        main_splitter = QSplitter(Qt.Horizontal)
        main_layout.addWidget(main_splitter)
        
        # 左侧控制面板 - 占30%宽度
        left_panel = QWidget()
        left_panel.setMinimumWidth(350)
        left_panel.setMaximumWidth(400)
        left_scroll = QScrollArea()
        left_scroll.setWidgetResizable(True)
        left_scroll.setWidget(left_panel)
        
        # 右侧显示区域 - 占70%宽度
        right_panel = QWidget()
        
        # 添加到分割器
        main_splitter.addWidget(left_scroll)
        main_splitter.addWidget(right_panel)
        main_splitter.setSizes([350, 930])  # 设置初始比例
        
        # 左侧面板布局
        left_layout = QVBoxLayout(left_panel)
        left_layout.setContentsMargins(5, 5, 5, 5)
        #left_layout.setSpacing(10)
        
        # 创建拍摄和停止按钮，稍后会添加到基础参数组中
        # 拍摄按钮
        self.capture_btn = QPushButton()
        self.capture_btn.setIcon(QIcon.fromTheme("camera-photo"))
        self.capture_btn.setIconSize(QSize(32, 32))
        self.capture_btn.setText("拍摄")
        self.capture_btn.setFixedHeight(40)
        self.capture_btn.clicked.connect(self.on_capture_clicked)
        
        # 停止按钮
        self.stop_btn = QPushButton()
        self.stop_btn.setIcon(QIcon.fromTheme("media-playback-pause"))
        self.stop_btn.setIconSize(QSize(32, 32))
        self.stop_btn.setText("停止")
        self.stop_btn.setFixedHeight(40)
        self.stop_btn.clicked.connect(self.on_stop_start_clicked)
        
        left_layout.addWidget(self.capture_btn)
        left_layout.addWidget(self.stop_btn)
        
        # 物镜倍数组 - 移到相机基础参数组之前
        objective_group = QGroupBox("物镜倍数")
        objective_layout = QVBoxLayout(objective_group)
        
        # 倍数单选按钮，放在一行
        magnification_layout = QHBoxLayout()
        self.mag_button_group = QButtonGroup()
        self.mag_5x_radio = QRadioButton("5x")
        self.mag_5x_radio.setChecked(True)  # 默认选择5x
        self.mag_10x_radio = QRadioButton("10x")
        self.mag_20x_radio = QRadioButton("20x")
        self.mag_50x_radio = QRadioButton("50x")
        self.mag_100x_radio = QRadioButton("100x")
        
        # 为物镜单选按钮设置特殊样式
        mag_radio_style = """
            QRadioButton {
                padding: 2px;
            }
            QRadioButton::indicator {
                width: 13px;
                height: 13px;
                border-radius: 6px;
                border: 2px solid #444;
            }
            QRadioButton::indicator:checked {
                border: 2px solid #e93d3d;
                background-color: #e93d3d;
            }
        """
        self.mag_5x_radio.setStyleSheet(mag_radio_style)
        self.mag_10x_radio.setStyleSheet(mag_radio_style)
        self.mag_20x_radio.setStyleSheet(mag_radio_style)
        self.mag_50x_radio.setStyleSheet(mag_radio_style)
        self.mag_100x_radio.setStyleSheet(mag_radio_style)
        
        self.mag_button_group.addButton(self.mag_5x_radio)
        self.mag_button_group.addButton(self.mag_10x_radio)
        self.mag_button_group.addButton(self.mag_20x_radio)
        self.mag_button_group.addButton(self.mag_50x_radio)
        self.mag_button_group.addButton(self.mag_100x_radio)
        
        magnification_layout.addWidget(self.mag_5x_radio)
        magnification_layout.addWidget(self.mag_10x_radio)
        magnification_layout.addWidget(self.mag_20x_radio)
        magnification_layout.addWidget(self.mag_50x_radio)
        magnification_layout.addWidget(self.mag_100x_radio)
        magnification_layout.addStretch()
        
        objective_layout.addLayout(magnification_layout)
        
        # 添加图片尺寸设置
        size_layout = QHBoxLayout()
        size_layout.addWidget(QLabel("图片尺寸:"))
        
        # 添加宽度输入
        size_layout.addWidget(QLabel("宽度:"))
        self.width_edit = QLineEdit("2.4202")
        self.width_edit.setFixedWidth(60)
        size_layout.addWidget(self.width_edit)
        size_layout.addWidget(QLabel("mm"))
        
        # 添加高度输入
        size_layout.addWidget(QLabel("高度:"))
        self.height_edit = QLineEdit("2.0247")
        self.height_edit.setFixedWidth(60)
        size_layout.addWidget(self.height_edit)
        size_layout.addWidget(QLabel("mm"))
        
        objective_layout.addLayout(size_layout)
        
        # 连接物镜倍数按钮组信号
        self.mag_button_group.buttonClicked.connect(self.on_magnification_changed)
        
        left_layout.addWidget(objective_group)
        
        # 相机基础参数组
        basic_group = QGroupBox("相机基础参数")
        basic_layout = QVBoxLayout(basic_group)
        basic_layout.setSpacing(8)
        
        # 功能复选框
        functions_layout = QHBoxLayout()
        self.isp_check = QCheckBox("打开ISP")
        self.auto_expo_check = QCheckBox("自动曝光")
        self.enhance_check = QCheckBox("增强")
        self.auto_wb_check = QCheckBox("自动白平衡")
        self.isp_check.setChecked(True)  # 默认勾选
        self.enhance_check.setChecked(True)  # 默认勾选
        functions_layout.addWidget(self.auto_expo_check)
        functions_layout.addWidget(self.isp_check)
        functions_layout.addWidget(self.enhance_check)
        functions_layout.addWidget(self.auto_wb_check)
        
        # 不在这里添加默认参数按钮，移动到曝光时间控制行
        
        basic_layout.addLayout(functions_layout)
        
        # 添加自动曝光状态变化的连接函数，控制亮度和曝光时间滑块的可用性
        def update_exposure_controls():
            auto_expo = self.auto_expo_check.isChecked()
            # 曝光时间控件只在自动曝光未勾选时可用
            self.ms_spinbox.setEnabled(not auto_expo)
            
            # 确保如果相机已连接，将自动曝光状态应用到相机
            if self.camera_open and self.tucam:
                try:
                    self.tucam.set_auto_exposure(1 if auto_expo else 0)
                    print(f"自动曝光已设置为: {'开启' if auto_expo else '关闭'}")
                    # 如果开启了自动曝光，在短暂延迟后获取并更新当前曝光值
                    if auto_expo:
                        QTimer.singleShot(200, self.update_exposure_from_camera)
                except Exception as e:
                    print(f"设置自动曝光状态时出错: {e}")
        
        self.auto_expo_check.stateChanged.connect(update_exposure_controls)
        
        # 分辨率选择
        resolution_layout = QHBoxLayout()
        resolution_label = QLabel("分辨率:")
        resolution_label.setFixedWidth(50)
        self.resolution_combo = QComboBox()
        self.resolution_combo.addItems(["2448x2048", "1224x1024"])
        self.resolution_combo.setCurrentIndex(0)  # 默认选择2448x2048
        
        # Add bit depth controls
        bit_depth_label = QLabel("位深:")
        bit_depth_label.setFixedWidth(40)
        self.bit_depth_combo = QComboBox()
        self.bit_depth_combo.addItems(["8bit", "16bit"])
        self.bit_depth_combo.setCurrentIndex(0)  # 默认选择8bit
        
        resolution_layout.addWidget(resolution_label)
        resolution_layout.addWidget(self.resolution_combo)
        resolution_layout.addWidget(bit_depth_label)
        resolution_layout.addWidget(self.bit_depth_combo)
        basic_layout.addLayout(resolution_layout)
        
        # 添加曝光时间SpinBox
        exposure_layout = QHBoxLayout()
        exposure_label = QLabel("曝光时间:")
        exposure_label.setFixedWidth(60)
        
        # 替换为双精度SpinBox
        self.ms_spinbox = QDoubleSpinBox()
        self.ms_spinbox.setRange(0, 1500)
        self.ms_spinbox.setValue(2.5)
        self.ms_spinbox.setSingleStep(0.5)
        self.ms_spinbox.setFixedWidth(80)  # 增加宽度
        self.ms_spinbox.setFixedHeight(30)  # 增加高度
        
        # 毫秒标签
        ms_label = QLabel("ms")
        
        # 添加默认参数按钮到曝光时间控制行
        default_param_btn = QPushButton("默认参数")
        default_param_btn.setFixedWidth(80)
        
        # 添加到布局
        exposure_layout.addWidget(exposure_label)
        exposure_layout.addWidget(self.ms_spinbox)
        exposure_layout.addWidget(ms_label)
        exposure_layout.addWidget(default_param_btn)
        exposure_layout.addStretch()
        
        basic_layout.addLayout(exposure_layout)
        
        # 增益滑块
        gain_layout, gain_slider, gain_value_label = self.create_slider_layout("增益:", 0, 480, 0, "0")
        basic_layout.addLayout(gain_layout)
        self.gain_slider = gain_slider  # 保存引用
        
        # 色调滑块
        hue_layout, hue_slider, hue_value_label = self.create_slider_layout("色调:", 0, 360, 180, "180")
        basic_layout.addLayout(hue_layout)
        self.hue_slider = hue_slider  # 保存引用
        
        # 饱和度滑块
        saturation_layout, saturation_slider, saturation_value_label = self.create_slider_layout("饱和度:", 0, 255, 64, "64")
        basic_layout.addLayout(saturation_layout)
        self.saturation_slider = saturation_slider  # 保存引用
        
        # 锐化滑块
        sharpen_layout, sharpen_slider, sharpen_value_label = self.create_slider_layout("锐化:", 0, 160, 25, "25")
        basic_layout.addLayout(sharpen_layout)
        self.sharpen_slider = sharpen_slider  # 保存引用
        
        # 亮度滑块
        light_layout, light_slider, light_value_label = self.create_slider_layout("亮度:", 0, 255, 64, "64")
        basic_layout.addLayout(light_layout)
        self.light_slider = light_slider  # 保存引用
        
        # 对比度滑块
        contrast_layout, contrast_slider, contrast_value_label = self.create_slider_layout("对比度:", 0, 63, 33, "33")
        basic_layout.addLayout(contrast_layout)
        self.contrast_slider = contrast_slider  # 保存引用
        
        # 伽马滑块（原偏色滑块）
        gamma_layout, gamma_slider, gamma_value_label = self.create_slider_layout("伽马:", 1, 255, 210, "210")
        basic_layout.addLayout(gamma_layout)
        self.gamma_slider = gamma_slider  # 保存引用
        
        # 添加三个复选框到基础参数组：自动白平衡、左右翻转和上下翻转
        flip_wb_layout = QHBoxLayout()
        self.horizontal_flip_check = QCheckBox("左右翻转")
        self.vertical_flip_check = QCheckBox("上下翻转")
        
        # 连接翻转复选框事件
        self.horizontal_flip_check.stateChanged.connect(self.on_horizontal_flip_changed)
        self.vertical_flip_check.stateChanged.connect(self.on_vertical_flip_changed)
        
        # 添加到同一行布局中 - 自动白平衡已经移到增强复选框后面
        flip_wb_layout.addWidget(self.horizontal_flip_check)
        flip_wb_layout.addWidget(self.vertical_flip_check)
        flip_wb_layout.addStretch()
        basic_layout.addLayout(flip_wb_layout)
        
        # 添加拍照和停止按钮到基础参数组
        buttons_layout = QHBoxLayout()
        
        # 从顶部移动按钮
        buttons_layout.addWidget(self.capture_btn)
        buttons_layout.addWidget(self.stop_btn)
        
        basic_layout.addLayout(buttons_layout)
        
        left_layout.addWidget(basic_group)
        
        # 电机控制
        wb_group = QGroupBox("电机控制")
        wb_layout = QVBoxLayout(wb_group)
        wb_layout.setSpacing(8)
        
        # 添加当前坐标显示标签
        coords_layout = QHBoxLayout()
        coords_label = QLabel("当前坐标：")
        coords_label.setFixedWidth(70)
        self.coords_value_label = QLabel("X: 0.00 mm, Y: 0.00 mm, Z: 0.00 µm")
        self.coords_value_label.setStyleSheet("font-weight: bold")
        coords_layout.addWidget(coords_label)
        coords_layout.addWidget(self.coords_value_label)
        coords_layout.addStretch()
        wb_layout.addLayout(coords_layout)
        
        # 串口连接区域改为状态显示和按钮
        serial_layout = QHBoxLayout()
        # 把串口标签和下拉框改为状态显示
        status_label = QLabel("状态:")
        status_label.setFixedWidth(50)
        self.connection_status = QLabel("未连接，请检查接口")
        self.connection_status.setStyleSheet("color: red;")
        # 重命名按钮
        self.enable_btn = QPushButton("电机使能")
        self.disable_btn = QPushButton("电机失能")
        self.enable_btn.setFixedWidth(80)
        self.disable_btn.setFixedWidth(80)
        
        # 连接按钮信号到槽函数
        self.enable_btn.clicked.connect(self.on_enable_clicked)
        self.disable_btn.clicked.connect(self.on_disable_clicked)
        
        serial_layout.addWidget(status_label)
        serial_layout.addWidget(self.connection_status)
        serial_layout.addWidget(self.enable_btn)
        serial_layout.addWidget(self.disable_btn)
        wb_layout.addLayout(serial_layout)
        
        # 电机控制按钮
        motor_layout = QHBoxLayout()
        home_btn = QPushButton("X-Y电机回零")
        move_btn = QPushButton("X-Y电机居中")
        init_btn = QPushButton("X-Y电机初始化")
        
        # 连接按钮信号
        home_btn.clicked.connect(self.on_motor_zero)
        move_btn.clicked.connect(self.on_motor_center)
        init_btn.clicked.connect(self.on_motor_init)
        
        motor_layout.addWidget(home_btn)
        motor_layout.addWidget(move_btn)
        motor_layout.addWidget(init_btn)
        wb_layout.addLayout(motor_layout)

        # 扫描速度和加速度
        speed_acc_layout = QHBoxLayout()
        speed_label = QLabel("电机速度:")
        speed_label.setFixedWidth(70)
        self.speed_edit = QLineEdit("3000")  # 默认为3000
        self.speed_edit.setFixedWidth(60)
        self.speed_edit.setValidator(QIntValidator(0, 5000))  # 限制最大值为5000
        
        acc_label = QLabel("电机加速度:")
        acc_label.setFixedWidth(85)
        self.acc_edit = QLineEdit("100")  # 默认为100
        self.acc_edit.setFixedWidth(60)
        self.acc_edit.setValidator(QIntValidator(50, 10000))  # 限制最小值为50
        
        # 当速度或加速度值改变时，应用到电机
        self.speed_edit.editingFinished.connect(self.apply_speed_settings)
        self.acc_edit.editingFinished.connect(self.apply_speed_settings)
        
        speed_acc_layout.addWidget(speed_label)
        speed_acc_layout.addWidget(self.speed_edit)
        speed_acc_layout.addWidget(acc_label)
        speed_acc_layout.addWidget(self.acc_edit)
        wb_layout.addLayout(speed_acc_layout)
        
        
        
        # 添加Z轴控制按钮
        z_motor_layout = QHBoxLayout()
        z_up_btn = QPushButton("Z轴上移")
        z_down_btn = QPushButton("Z轴下移")
        z_zero_btn = QPushButton("Z电机回零")
        
        # 连接Z轴按钮信号
        z_up_btn.clicked.connect(self.on_z_up_clicked)
        z_down_btn.clicked.connect(self.on_z_down_clicked)
        
        z_motor_layout.addWidget(z_zero_btn)
        z_motor_layout.addWidget(z_up_btn)
        z_motor_layout.addWidget(z_down_btn)
        
        wb_layout.addLayout(z_motor_layout)
        
        # 添加Z轴步距选择
        z_step_layout = QHBoxLayout()
        z_step_label = QLabel("Z轴步距(µm):")
        z_step_layout.addWidget(z_step_label)
        
        # 使用按钮组实现单选效果
        self.z_step_group = QButtonGroup(self)
        
        # 添加步距选项（微米）：0.1, 1, 10, 100, 200, 500
        self.z_step_buttons = []
        z_step_values = ["0.1", "1", "10", "100", "200", "500"]
        
        for value in z_step_values:
            btn = QRadioButton(value)
            z_step_layout.addWidget(btn)
            self.z_step_group.addButton(btn)
            self.z_step_buttons.append(btn)
            btn.clicked.connect(lambda checked, v=value: self.on_z_step_selected(v))
        
        # 默认选择10微米
        self.z_step_buttons[2].setChecked(True)
        self.current_z_step = 10.0  # 默认步距为10微米
        
        z_step_layout.addStretch()
        wb_layout.addLayout(z_step_layout)
        
        # 添加对焦控制
        focus_method_layout = QHBoxLayout()
        focus_method_label = QLabel("对焦方法:")
        focus_method_label.setFixedWidth(70)
        self.focus_method_combo = QComboBox()
        self.focus_method_combo.addItems([
            "laplacian", 
            "opencv_laplacian", 
            "opencv_canny", 
            "opencv_dof", 
            "tenengrad", 
            "sobel", 
            "shannon_entropy", 
            "smd",  # 方差梯度综合评价法
            "brenner"  # Brenner梯度法
        ])
        self.focus_method_combo.setCurrentText(self.focus_method)
        self.focus_method_combo.currentTextChanged.connect(self.on_focus_method_changed)
        
        # 添加对焦评分标签
        focus_score_label = QLabel("对焦评分:")
        focus_score_label.setFixedWidth(40)
        self.focus_score_value = QLabel("0.00")
        self.focus_score_value.setFixedWidth(60)
        self.focus_score_value.setAlignment(Qt.AlignRight | Qt.AlignVCenter)
        self.focus_score_value.setStyleSheet("font-weight: bold;")
        
        focus_method_layout.addWidget(focus_method_label)
        focus_method_layout.addWidget(self.focus_method_combo)
        focus_method_layout.addWidget(focus_score_label)
        focus_method_layout.addWidget(self.focus_score_value)
        focus_method_layout.addStretch()
        wb_layout.addLayout(focus_method_layout)
        
        # 添加对焦状态标签
        focus_status_layout = QHBoxLayout()
        self.focus_status = QLabel("对焦状态: 就绪")
        self.focus_status.setStyleSheet("color: green;")
        # 移除这两行：不再添加focus_status到focus_status_layout
        # focus_status_layout.addWidget(self.focus_status)
        # focus_status_layout.addStretch()
        wb_layout.addLayout(focus_status_layout)
        
        # 添加对焦按钮
        focus_buttons_layout = QHBoxLayout()
        self.coarse_focus_btn = QPushButton("粗对焦")
        self.medium_focus_btn = QPushButton("中对焦")
        self.fine_focus_btn = QPushButton("细对焦")
        self.auto_focus_btn = QPushButton("自动对焦")
        
        # 连接按钮信号
        self.coarse_focus_btn.clicked.connect(lambda: self.on_focus_clicked("coarse"))
        self.medium_focus_btn.clicked.connect(lambda: self.on_focus_clicked("medium"))
        self.fine_focus_btn.clicked.connect(lambda: self.on_focus_clicked("fine"))
        self.auto_focus_btn.clicked.connect(lambda: self.on_focus_clicked("auto"))
        
        focus_buttons_layout.addWidget(self.coarse_focus_btn)
        focus_buttons_layout.addWidget(self.medium_focus_btn)
        focus_buttons_layout.addWidget(self.fine_focus_btn)
        focus_buttons_layout.addWidget(self.auto_focus_btn)
        wb_layout.addLayout(focus_buttons_layout)
        
        left_layout.addWidget(wb_group)
        
        # 扫描参数组已被重命名为对焦与扫描
        scan_area_group = QGroupBox("对焦与扫描")
        scan_area_layout = QVBoxLayout(scan_area_group)
        #scan_area_layout.setSpacing(8)
        
        # 添加对焦按钮组
        focus_buttons_layout = QHBoxLayout()
        add_point_focus_btn = QPushButton("添加点对焦")
        nice_focus_btn = QPushButton("9点对焦")
        sixteen_focus_btn = QPushButton("16点对焦")

        # 设置按钮样式和大小
        for btn in [add_point_focus_btn, nice_focus_btn, sixteen_focus_btn]:
            btn.setFixedHeight(40)

        # 连接按钮信号
        add_point_focus_btn.clicked.connect(self.on_add_point_focus_clicked)
        nice_focus_btn.clicked.connect(self.on_nice_focus_clicked)
        sixteen_focus_btn.clicked.connect(self.on_sixteen_focus_clicked)

        # 添加到布局
        focus_buttons_layout.addWidget(add_point_focus_btn)
        focus_buttons_layout.addWidget(nice_focus_btn)
        focus_buttons_layout.addWidget(sixteen_focus_btn)
        scan_area_layout.addLayout(focus_buttons_layout)

        # 添加扫描按钮组
        big_buttons_layout = QHBoxLayout()
        grid_btn = QPushButton("扫描网格设置")
        self.scan_btn = QPushButton("执行扫描")  # 修改为类属性
        grid_btn.setFixedHeight(50)
        self.scan_btn.setFixedHeight(50)
        big_buttons_layout.addWidget(grid_btn)
        big_buttons_layout.addWidget(self.scan_btn)
        scan_area_layout.addLayout(big_buttons_layout)

        # 连接扫描网格设置按钮到stage窗口
        grid_btn.clicked.connect(self.open_stage_window)

        # 连接执行扫描按钮到扫描函数
        self.scan_btn.clicked.connect(self.on_scan_clicked)  # 修改为类属性

        left_layout.addWidget(scan_area_group)
        
        # 扫描文件保存组
        scan_file_group = QGroupBox("文件保存")
        scan_file_layout = QVBoxLayout(scan_file_group)
        scan_file_layout.setSpacing(8)
        
        # 文件格式
        format_layout = QHBoxLayout()
        format_label = QLabel("格式:")
        format_label.setFixedWidth(50)
        self.format_combo = QComboBox()  # 确保是实例变量
        self.format_combo.addItems(["JPG", "PNG", "TIF", "BMP", "RAW"])  # 添加支持的所有格式
        self.format_combo.currentIndexChanged.connect(self.on_format_changed)  # 连接格式变化事件
        format_layout.addWidget(format_label)
        format_layout.addWidget(self.format_combo)
        scan_file_layout.addLayout(format_layout)
        
        # 保存路径
        path_layout = QHBoxLayout()
        path_label = QLabel("路径:")
        path_label.setFixedWidth(50)
        self.path_edit = QLineEdit("D:\\images")  # 修改默认路径为D:\\images
        browse_btn = QPushButton("浏览...")
        browse_btn.setFixedWidth(60)
        
        # 浏览按钮点击事件处理函数
        def browse_folder():
            folder_path = QFileDialog.getExistingDirectory(self, "选择保存路径")
            if folder_path:  # 如果用户选择了文件夹而不是取消
                self.path_edit.setText(folder_path)
        
        browse_btn.clicked.connect(browse_folder)
        
        path_layout.addWidget(path_label)
        path_layout.addWidget(self.path_edit)
        path_layout.addWidget(browse_btn)
        scan_file_layout.addLayout(path_layout)
        
        # 文件名
        filename_layout = QHBoxLayout()
        filename_label = QLabel("文件名:")
        filename_label.setFixedWidth(50)
        self.filename_edit = QLineEdit("Image_")  # 添加self.引用
        filename_layout.addWidget(filename_label)
        filename_layout.addWidget(self.filename_edit)
        scan_file_layout.addLayout(filename_layout)
        
        # 自动编号
        self.auto_number_check = QCheckBox("自动编号")  # 添加self.引用
        self.auto_number_check.setChecked(False)
        scan_file_layout.addWidget(self.auto_number_check)
        
        left_layout.addWidget(scan_file_group)
        
        # 默认按钮点击处理 - 重置滑块为默认值
        def reset_to_default():
            hue_slider.setValue(180)  # 色调默认值
            saturation_slider.setValue(64)  # 饱和度默认值
            # brightness_slider.setValue(128)  # 光照默认值
            contrast_slider.setValue(33)  # 对比度默认值
            gamma_slider.setValue(210)  # 伽马默认值
            sharpen_slider.setValue(25)  # 锐化默认值
            light_slider.setValue(64)  # 亮度默认值
            gain_slider.setValue(0)  # 增益默认值
            
            # 曝光时间默认值
            self.ms_spinbox.setValue(2.5)  # 设置为默认值2.5ms
        
        # 连接默认参数按钮与重置函数
        default_param_btn.clicked.connect(reset_to_default)
        
        # 右侧面板布局 - 图像显示区和直方图
        right_layout = QVBoxLayout(right_panel)
        right_layout.setContentsMargins(0, 0, 0, 0)
        
        # 创建图像显示区
        self.image_display = QLabel()
        self.image_display.setAlignment(Qt.AlignCenter)
        self.image_display.setStyleSheet("background-color: #333333;")
        self.image_display.setMinimumSize(640, 480)
        
        # 创建信息显示区和直方图区域
        info_splitter = QSplitter(Qt.Vertical)
        info_splitter.setChildrenCollapsible(False)
        
        # 顶部图像显示区占80%
        image_container = QWidget()
        image_layout = QVBoxLayout(image_container)
        image_layout.setContentsMargins(0, 0, 0, 0)
        image_layout.addWidget(self.image_display)
        
        # 底部信息区域占20%
        info_container = QWidget()
        info_container.setMinimumHeight(150)
        info_container.setMaximumHeight(200)
        
        info_layout = QVBoxLayout(info_container)
        info_layout.setContentsMargins(5, 0, 5, 5)
        
        # 创建直方图选项卡
        histogram_tabs = QTabWidget()
        histogram_tabs.setTabPosition(QTabWidget.South)
        
        # 文件预览选项卡
        file_preview_tab = QWidget()
        file_layout = QHBoxLayout(file_preview_tab)
        file_layout.setContentsMargins(0, 0, 0, 0)
        histogram_tabs.addTab(file_preview_tab, "文件预览")
        
        # 官方图选项卡 - 包含直方图
        official_tab = QWidget()
        official_layout = QHBoxLayout(official_tab)
        official_layout.setContentsMargins(0, 0, 0, 0)
        
        # 直方图显示
        self.histogram = QLabel()
        self.histogram.setStyleSheet("background-color: white;")
        self.histogram.setMinimumHeight(100)
        
        # 直方图信息
        histogram_info = QWidget()
        histogram_info.setFixedWidth(120)
        histogram_info_layout = QVBoxLayout(histogram_info)
        histogram_info_layout.setContentsMargins(5, 5, 5, 5)
        
        # 颜色选择按钮
        histogram_info_layout.addWidget(QLabel("彩色"))
        
        color_btn_layout = QVBoxLayout()
        color_btn_layout.setSpacing(2)
        
        red_btn = QPushButton("红")
        red_btn.setStyleSheet("background-color: red; color: white;")
        red_btn.setFixedWidth(40)
        red_btn.setFixedHeight(20)
        
        green_btn = QPushButton("绿")
        green_btn.setStyleSheet("background-color: green; color: white;")
        green_btn.setFixedWidth(40)
        green_btn.setFixedHeight(20)
        
        blue_btn = QPushButton("蓝")
        blue_btn.setStyleSheet("background-color: blue; color: white;")
        blue_btn.setFixedWidth(40)
        blue_btn.setFixedHeight(20)
        
        color_btn_layout.addWidget(red_btn)
        color_btn_layout.addWidget(green_btn)
        color_btn_layout.addWidget(blue_btn)
        color_btn_layout.addStretch()
        
        histogram_info_layout.addLayout(color_btn_layout)
        
        official_layout.addWidget(self.histogram, stretch=1)
        official_layout.addWidget(histogram_info)
        
        histogram_tabs.addTab(official_tab, "官方图(实时)")
        
        # 相机日志选项卡
        log_tab = QWidget()
        log_layout = QVBoxLayout(log_tab)
        log_layout.setContentsMargins(0, 0, 0, 0)
        histogram_tabs.addTab(log_tab, "相机日志")
        
        # 校准表选项卡
        calibration_tab = QWidget()
        calibration_layout = QVBoxLayout(calibration_tab)
        calibration_layout.setContentsMargins(0, 0, 0, 0)
        histogram_tabs.addTab(calibration_tab, "校准表")
        
        info_layout.addWidget(histogram_tabs)
        
        # 状态栏
        status_bar = QWidget()
        status_bar.setFixedHeight(30)
        status_layout = QHBoxLayout(status_bar)
        status_layout.setContentsMargins(5, 0, 5, 0)
        
        auto_level_check = QCheckBox("自动灰度阶: 0")
        range_label = QLabel("范围:")
        bit_combo = QComboBox()
        bit_combo.addItems(["8bit", "16bit"])
        bit_combo.setFixedWidth(80)
        
        # 将固定的缩放比例标签更改为相机状态标签
        self.camera_status_label = QLabel("相机状态: 未连接")
        self.camera_status_label.setStyleSheet("color: orange;")
        
        # 添加分辨率和FPS标签
        self.camera_info_label = QLabel("分辨率: -- | FPS: --")
        self.camera_info_label.setStyleSheet("color: #3498db;")
        
        rgb_label = QLabel("RGB: (9885, 9360, 9167)")
        coords_label = QLabel("坐标: (631, 527)")
        
        status_layout.addWidget(auto_level_check)
        status_layout.addWidget(range_label)
        status_layout.addWidget(bit_combo)
        status_layout.addStretch()
        status_layout.addWidget(self.camera_status_label)
        status_layout.addWidget(self.camera_info_label)  # 添加新标签
        status_layout.addStretch()
        # 删除RGB和coords标签，添加focus_status
        # status_layout.addWidget(rgb_label)
        # status_layout.addWidget(coords_label)
        status_layout.addWidget(self.focus_status)
        
        info_layout.addWidget(status_bar)
        
        # 添加到分割器
        info_splitter.addWidget(image_container)
        info_splitter.addWidget(info_container)
        info_splitter.setSizes([600, 200])  # 设置垂直分割比例
        
        right_layout.addWidget(info_splitter)
        
        # 生成示例直方图
        self.create_sample_histogram()
        
        # 初始化时调用update_exposure_controls来设置初始滑块状态
        update_exposure_controls()
        
        # 连接分辨率下拉框到切换事件
        self._connect_resolution_combo()
        
        # 设置对焦方法提示信息
        self.focus_method_combo.setItemData(0, "拉普拉斯边缘检测算法 - 快速通用", Qt.ToolTipRole)
        self.focus_method_combo.setItemData(1, "OpenCV拉普拉斯算子 - 通常比PyTorch实现更快", Qt.ToolTipRole)
        self.focus_method_combo.setItemData(2, "OpenCV Canny边缘检测算法 - 对边缘结构敏感", Qt.ToolTipRole)
        self.focus_method_combo.setItemData(3, "景深和局部方差复合算法 - 对复杂结构有优势", Qt.ToolTipRole)
        self.focus_method_combo.setItemData(4, "Tenengrad梯度算法 - 适合纹理丰富的样本", Qt.ToolTipRole)
        self.focus_method_combo.setItemData(5, "Sobel边缘梯度算法 - 适合边缘明显的样本", Qt.ToolTipRole)
        self.focus_method_combo.setItemData(6, "Shannon熵算法 - 适合图像信息丰富的样本", Qt.ToolTipRole)
        self.focus_method_combo.setItemData(7, "方差梯度综合评价算法 - 综合多种评价指标", Qt.ToolTipRole)
        self.focus_method_combo.setItemData(8, "Brenner梯度算法 - 适合低对比度样本", Qt.ToolTipRole)
        
        # 设置对焦按钮提示信息
        self.coarse_focus_btn.setToolTip("粗对焦：步长100μm，用于快速接近焦点")
        self.medium_focus_btn.setToolTip("中对焦：步长10μm，用于中等精度对焦")
        self.fine_focus_btn.setToolTip("细对焦：步长1μm，用于高精度对焦")
        self.auto_focus_btn.setToolTip("自动对焦：自动完成从粗对焦到精细对焦的整个过程")
    
    def create_slider_layout(self, label_text, min_val, max_val, current_val, display_text=None):
        """创建统一样式的滑块布局"""
        layout = QHBoxLayout()
        layout.setContentsMargins(0, 0, 0, 0)
        
        # 标签
        label = QLabel(label_text)
        label.setFixedWidth(50)
        label.setAlignment(Qt.AlignLeft | Qt.AlignVCenter)
        
        # 根据滑块类型创建不同的滑块
        if label_text == "曝光时间:":
            # 创建水平布局用于毫秒SpinBox
            exposure_layout = QHBoxLayout()
            
            # 创建浮点数SpinBox
            ms_spinbox = QDoubleSpinBox()
            ms_spinbox.setRange(0, 1500)
            ms_spinbox.setValue(2.5)
            ms_spinbox.setSingleStep(0.5)
            ms_label = QLabel("ms")
            
            # 添加到布局
            exposure_layout.addWidget(ms_spinbox)
            exposure_layout.addWidget(ms_label)
            
            # 创建值显示标签
            value_label = QLabel(str(display_text) if display_text else str(current_val))
            value_label.setFixedWidth(50)
            value_label.setAlignment(Qt.AlignRight | Qt.AlignVCenter)
            
            # 当SpinBox值变更时更新图像
            def update_exposure_value():
                # 获取毫秒值
                ms_value = ms_spinbox.value()
                # 更新显示
                value_label.setText(f"{ms_value:.1f}")
            
            # 连接信号
            ms_spinbox.valueChanged.connect(update_exposure_value)
            
            # 添加组件到主布局
            layout.addWidget(label)
            layout.addLayout(exposure_layout)
            layout.addWidget(value_label)
            
            # 返回布局和SpinBox，以便后续使用
            return layout, ms_spinbox, value_label
        else:
            # 其他滑块使用普通QSlider
            slider = QSlider(Qt.Horizontal)
            slider.setRange(min_val, max_val)
            slider.setValue(current_val)
            slider.setSingleStep(1)
            
            # 连接滑块值变化信号到更新显示值的函数
            def update_value(value):
                if label_text == "色温:":
                    value_label.setText(f"{value}K")
                else:
                    value_label.setText(str(value))
            
            slider.valueChanged.connect(update_value)
        
        # 数值显示
        value_label = QLabel(display_text if display_text else str(current_val))
        value_label.setFixedWidth(40)
        value_label.setAlignment(Qt.AlignRight | Qt.AlignVCenter)
        
        layout.addWidget(label)
        layout.addWidget(slider)
        layout.addWidget(value_label)
        
        return layout, slider, value_label
        
    def create_sample_histogram(self):
        """创建示例直方图"""
        # 创建一个600x100的灰色图像，用于表示直方图
        hist_img = QPixmap(600, 100)
        hist_img.fill(QColor(240, 240, 240))
        
        # 将样本直方图设置到标签
        self.histogram.setPixmap(hist_img)

    def on_enable_clicked(self):
        """电机使能按钮点击处理"""
        if not self.motor_controller or not self.motor_controller.is_initialized:
            self.connection_status.setText("未连接，请检查端口")
            self.connection_status.setStyleSheet("color: red;")
            print("电机控制器未初始化，无法使能")
            return
            
        try:
            # 使能所有电机
            for motor_id in self.motor_controller.motor_sum:
                self.motor_controller.power_on(motor_id)
                
            motor_count = len(self.motor_controller.motor_sum)
            if motor_count == 2:
                self.connection_status.setText("X-Y两轴电机已连接")
            elif motor_count == 3:
                self.connection_status.setText("X-Y-Z三轴电机已连接")
            else:
                self.connection_status.setText("电机数量有误，请检查")
                
            self.connection_status.setStyleSheet("color: green;")
            print("电机已使能")
            
            # 更新按钮状态 - 使能按钮变红色
            self.enable_btn.setStyleSheet("QPushButton { background-color: #e74c3c; color: white; font-weight: bold; }")
            self.disable_btn.setStyleSheet("")  # 恢复系统默认样式
        except Exception as e:
            print(f"使能电机时出错: {e}")
            
    def on_disable_clicked(self):
        """电机失能按钮点击处理"""
        if not self.motor_controller or not self.motor_controller.is_initialized:
            self.connection_status.setText("未连接，请检查端口")
            self.connection_status.setStyleSheet("color: red;")
            print("电机控制器未初始化，无法失能")
            return
            
        try:
            # 失能所有电机
            for motor_id in self.motor_controller.motor_sum:
                self.motor_controller.power_off(motor_id)
                
            self.connection_status.setText("电机已失能")
            self.connection_status.setStyleSheet("color: orange;")
            print("电机已失能")
            
            # 更新按钮状态 - 使能按钮恢复默认，失能按钮变橙色
            self.enable_btn.setStyleSheet("")  # 恢复系统默认样式
            self.disable_btn.setStyleSheet("QPushButton { background-color: #f39c12; color: white; font-weight: bold; }")
        except Exception as e:
            print(f"失能电机时出错: {e}")
    
    def open_stage_window(self):
        """打开Stage窗口"""
        # 如果窗口已存在，直接显示
        if self.stage_window is not None:
            self.stage_window.show()
            self.stage_window.activateWindow()
            self.stage_window.raise_()
            return
            
        # 创建Stage窗口
        print("初始化Stage窗口")
        self.stage_window = Stage.MainWindow("COM4")
        
        # 设置扫描窗口参数，确保扫描能正常运行
        self.stage_window.grid_points = []  # 初始化网格点列表
        self.stage_window.current_scan_index = 0  # 初始化当前扫描索引
        
        # 初始化扫描定时器
        self.stage_window.scan_timer = QTimer(self.stage_window)
        self.stage_window.scan_timer.timeout.connect(self.stage_window.scan_next_position)
        
        # 设置网格尺寸
        self.stage_window.grid_width = 2.4202  # 默认FOV宽度 (mm)
        self.stage_window.grid_height = 2.0247  # 默认FOV高度 (mm)
        
        # 同步主窗口控制器
        if self.motor_controller and self.motor_controller.is_initialized:
            self.stage_window.set_motor_controller(self.motor_controller)
            
        # 设置主窗口引用，用于回调拍照功能
        self.stage_window.main_window = self
            
        # 同步初始参数
        self.sync_main_params_to_stage()
        
        # 连接参数变化信号
        self.connect_param_signals()
            
        # 显示窗口
        self.stage_window.show()
        
        # 覆盖Stage窗口的关闭事件
        original_close_event = self.stage_window.closeEvent
        
        # 定义新的关闭事件处理函数
        def new_close_event(event):
            # 在隐藏窗口前，更新主窗口中的参数值
            self.sync_stage_params_to_main()
            
            # 不销毁窗口，只是隐藏它
            self.stage_window.hide()
            self.on_stage_window_closed()
            event.ignore()  # 阻止窗口被销毁
            
        # 替换关闭事件
        self.stage_window.closeEvent = new_close_event
    
    def sync_main_params_to_stage(self):
        """将主窗口的参数同步到Stage窗口"""
        if not self.stage_window:
            return
            
        # 同步扫描速度
        speed_value = self.speed_edit.text()
        if hasattr(self.stage_window, 'scan_speed_edit'):
            self.stage_window.scan_speed_edit.setText(speed_value)
            
        # 同步扫描加速度
        acc_value = self.acc_edit.text()
        if hasattr(self.stage_window, 'scan_acc_edit'):
            self.stage_window.scan_acc_edit.setText(acc_value)
            
        # 应用参数
        if hasattr(self.stage_window, 'apply_scan_speed_settings'):
            self.stage_window.apply_scan_speed_settings()
            
    def sync_stage_params_to_main(self):
        """将Stage窗口的参数同步回主窗口"""
        if not self.stage_window:
            return
            
        # 同步扫描速度
        if hasattr(self.stage_window, 'scan_speed_edit'):
            speed_value = self.stage_window.scan_speed_edit.text()
            self.speed_edit.setText(speed_value)
            
        # 同步扫描加速度
        if hasattr(self.stage_window, 'scan_acc_edit'):
            acc_value = self.stage_window.scan_acc_edit.text()
            self.acc_edit.setText(acc_value)
            
        # 应用参数到电机控制器
        self.apply_speed_settings()
        
    def connect_param_signals(self):
        """连接参数变化信号"""
        if not self.stage_window:
            return
            
        # 主窗口参数变化时更新Stage窗口
        self.speed_edit.editingFinished.connect(lambda: self.update_stage_param('speed'))
        self.acc_edit.editingFinished.connect(lambda: self.update_stage_param('acc'))
        
        # 注释掉这一行，因为主窗口没有overlap_edit
        # self.overlap_edit.editingFinished.connect(lambda: self.update_stage_param('overlap'))
        
        # 检查path_combo是否存在
        if hasattr(self, 'path_combo'):
            self.path_combo.currentIndexChanged.connect(lambda: self.update_stage_param('path'))
        
        # Stage窗口参数变化时更新主窗口
        if hasattr(self.stage_window, 'scan_speed_edit'):
            self.stage_window.scan_speed_edit.editingFinished.connect(lambda: self.update_main_param('speed'))
        if hasattr(self.stage_window, 'scan_acc_edit'):
            self.stage_window.scan_acc_edit.editingFinished.connect(lambda: self.update_main_param('acc'))
        
        # 处理stage window的overlap_edit - 直接设置grid_overlap属性
        if hasattr(self.stage_window, 'overlap_edit'):
            # 当stage window的overlap_edit改变时更新grid_overlap
            self.stage_window.overlap_edit.editingFinished.connect(
                lambda: setattr(self.stage_window, 'grid_overlap', 
                    float(self.stage_window.overlap_edit.text()) / 100.0
                ) if hasattr(self.stage_window, 'grid_overlap') else None
            )
        
        if hasattr(self.stage_window, 'scan_pattern_combo'):
            self.stage_window.scan_pattern_combo.currentIndexChanged.connect(lambda: self.update_main_param('path'))
            
    def update_stage_param(self, param_type):
        """更新Stage窗口中的参数"""
        if not self.stage_window or self.stage_window.isHidden():
            return
            
        if param_type == 'speed' and hasattr(self.stage_window, 'scan_speed_edit'):
            self.stage_window.scan_speed_edit.setText(self.speed_edit.text())
            self.stage_window.apply_scan_speed_settings()
        elif param_type == 'acc' and hasattr(self.stage_window, 'scan_acc_edit'):
            self.stage_window.scan_acc_edit.setText(self.acc_edit.text())
            self.stage_window.apply_scan_speed_settings()
        elif param_type == 'path' and hasattr(self.stage_window, 'scan_pattern_combo') and hasattr(self, 'path_combo'):
            self.stage_window.scan_pattern_combo.setCurrentIndex(self.path_combo.currentIndex())
            
    def update_main_param(self, param_type):
        """更新主窗口中的参数"""
        if not self.stage_window:
            return
            
        if param_type == 'speed' and hasattr(self.stage_window, 'scan_speed_edit'):
            self.speed_edit.setText(self.stage_window.scan_speed_edit.text())
            self.apply_speed_settings()
        elif param_type == 'acc' and hasattr(self.stage_window, 'scan_acc_edit'):
            self.acc_edit.setText(self.stage_window.scan_acc_edit.text())
            self.apply_speed_settings()
        # 注释掉overlap相关的代码
        # elif param_type == 'overlap' and hasattr(self.stage_window, 'overlap_edit'):
        #     # 主窗口没有overlap_edit，所以不需要处理
        elif param_type == 'path' and hasattr(self.stage_window, 'scan_pattern_combo') and hasattr(self, 'path_combo'):
            self.path_combo.setCurrentIndex(self.stage_window.scan_pattern_combo.currentIndex())
            
    def on_stage_window_closed(self):
        """处理电机控制窗口关闭事件"""
        # 不销毁窗口，只是记录状态
        print("Stage窗口已隐藏")
            
    def auto_connect_motors(self):
        """自动连接电机并检测电机数量"""
        try:
            # 尝试使用COM4端口连接电机控制器 - 使用Stage模块的电机控制器
            com_port = "COM4"
            self.motor_controller = Stage.MotorController(com_port)
            
            # 检查电机控制器是否初始化成功
            if self.motor_controller.is_initialized:
                self.motor_connected = True
                # 获取电机数量
                motor_count = len(self.motor_controller.motor_sum)
                
                # 根据电机数量更新状态
                if motor_count == 2:
                    self.connection_status.setText("X-Y两轴电机已连接")
                    self.connection_status.setStyleSheet("color: green;")
                elif motor_count == 3:
                    self.connection_status.setText("X-Y-Z三轴电机已连接")
                    self.connection_status.setStyleSheet("color: green;")
                else:
                    self.connection_status.setText("电机数量有误，请检查")
                    self.connection_status.setStyleSheet("color: orange;")
                
                # 自动使能所有电机
                for motor_id in self.motor_controller.motor_sum:
                    self.motor_controller.power_on(motor_id)
                
                # 更新按钮状态 - 使能按钮变红色
                self.enable_btn.setStyleSheet("QPushButton { background-color: #e74c3c; color: white; font-weight: bold; }")
                self.disable_btn.setStyleSheet("")  # 恢复系统默认样式
                
                # 应用默认的速度和加速度设置
                try:
                    # 使用Config类的默认参数 
                    speed = int(self.speed_edit.text()) or Config.DEFAULT_SPEED
                    acc = int(self.acc_edit.text()) or Config.DEFAULT_ACC_TIME
                    for motor_id in self.motor_controller.motor_sum:
                        self.motor_controller.set_speed(motor_id, speed, acc)
                    print(f"应用默认速度和加速度设置，速度: {speed}, 加速度: {acc}")
                except Exception as e:
                    print(f"应用默认速度和加速度设置时出错: {e}")
                
                # 获取初始坐标信息
                self.update_motor_position()
                
                print(f"已连接电机，共{motor_count}个")
            else:
                self.motor_connected = False
                self.connection_status.setText("未连接，请检查端口")
                self.connection_status.setStyleSheet("color: red;")
                print("电机控制器初始化失败")
                
                # 更新按钮状态为默认
                self.enable_btn.setStyleSheet("")  # 恢复系统默认样式
                self.disable_btn.setStyleSheet("")  # 恢复系统默认样式
                
        except Exception as e:
            self.motor_connected = False
            self.connection_status.setText("未连接，请检查端口")
            self.connection_status.setStyleSheet("color: red;")
            print(f"连接电机时出错: {e}")
            
            # 更新按钮状态为默认
            self.enable_btn.setStyleSheet("")  # 恢复系统默认样式
            self.disable_btn.setStyleSheet("")  # 恢复系统默认样式
            
            import traceback
            traceback.print_exc()
    
    def update_motor_position(self):
        """更新电机位置信息到坐标显示标签"""
        if not self.motor_connected or not self.motor_controller:
            return
        
        try:
            # 获取X、Y、Z电机位置
            # 根据电机数量确定哪些坐标可用
            motor_count = len(self.motor_controller.motor_sum)
            
            if motor_count >= 2:  # 至少有X、Y电机
                # 获取X、Y电机位置（脉冲值）
                x_pulse = self.motor_controller.get_position(Axis.X)
                y_pulse = self.motor_controller.get_position(Axis.Y)
                
                # 转换为毫米 - 使用Config类转换函数
                x_pos = Config.pulse_to_physical(x_pulse, Axis.X)
                y_pos = Config.pulse_to_physical(y_pulse, Axis.Y)
                
                # 更新类属性
                self.current_x_mm = x_pos
                self.current_y_mm = y_pos
            
            if motor_count >= 3:  # 有Z电机
                # 获取Z电机位置（脉冲值）
                z_pulse = self.motor_controller.get_position(Axis.Z)
                
                # 转换为微米 - 使用Config类转换函数
                z_pos = Config.pulse_to_physical(z_pulse, Axis.Z)
                
                # 更新类属性
                self.current_z_um = z_pos
            
            # 更新坐标显示
            self.coords_value_label.setText(
                f"X: {self.current_x_mm:.3f} mm, Y: {self.current_y_mm:.3f} mm, Z: {self.current_z_um:.2f} µm"
            )
            
        except Exception as e:
            print(f"更新电机位置信息时出错: {e}")
    
    def sync_coordinates_to_stage(self):
        """同步当前坐标到Stage窗口 - 统一的坐标同步函数"""
        if not hasattr(self, 'stage_window') or self.stage_window is None:
            return
            
        try:
            # 更新Stage窗口中的当前位置数据
            if hasattr(self.stage_window, 'plotter_widget'):
                # 直接调用plotter_widget的更新方法，传入脉冲值
                x_pulse = self.motor_controller.get_position(Axis.X)
                y_pulse = self.motor_controller.get_position(Axis.Y)
                z_pulse = self.motor_controller.get_position(Axis.Z)
                
                self.stage_window.plotter_widget.update_actual_position(x_pulse, y_pulse, z_pulse)
            
            # Stage窗口的坐标属性也会在update_actual_position内部更新，但我们在这里也更新一下以确保一致性
            self.stage_window.current_x_mm = self.current_x_mm
            self.stage_window.current_y_mm = self.current_y_mm
            self.stage_window.current_z_micron = self.current_z_um
            
        except Exception as e:
            print(f"同步坐标到Stage窗口时出错: {e}")
    
    def update_and_sync_coordinates(self):
        """更新主界面坐标并同步到Stage窗口 - 一站式坐标更新函数"""
        # 更新主界面坐标
        self.update_motor_position()
        # 同步到Stage窗口
        self.sync_coordinates_to_stage()
    
    def move_z_axis(self, direction: int, step_um: float):
        """Z轴移动辅助函数
        
        Args:
            direction: 方向，1表示下移(增加脉冲)，-1表示上移(减少脉冲)
            step_um: 步长(微米)
        """
        if not self.motor_connected or not self.motor_controller:
            self.connection_status.setText("未连接，请检查端口")
            self.connection_status.setStyleSheet("color: red;")
            print("电机控制器未初始化，无法移动Z轴")
            return
        
        try:
            # 检查是否有Z轴电机
            motor_count = len(self.motor_controller.motor_sum)
            if motor_count < 3:
                print("没有找到Z轴电机")
                return
            
            # 计算脉冲数 - 使用Config类转换函数
            step_pulse = Config.physical_to_pulse(step_um, Axis.Z)
            
            # 获取当前位置并计算新位置
            current_position = self.motor_controller.get_position(Axis.Z)
            new_position = current_position + direction * step_pulse
            
            # 检查限位
            if direction < 0:  # 上移检查下限位
                min_pulse = Config.SOFT_LIMIT_PULSE[2][0]
                if new_position < min_pulse:
                    new_position = min_pulse
                    lower_limit_um = Config.pulse_to_physical(min_pulse, Axis.Z)
                    print(f"警告：Z轴已达到下限位 ({lower_limit_um:.1f}µm)")
            else:  # 下移检查上限位
                max_pulse = Config.SOFT_LIMIT_PULSE[2][1]
                if new_position > max_pulse:
                    new_position = max_pulse
                    print(f"警告：Z轴已达到上限位 ({Config.MAX_Z_MICRON:.1f}µm)")
            
            # 移动Z轴
            self.motor_controller.move_to_target_position(Axis.Z, new_position)
            print(f"Z轴{'下移' if direction > 0 else '上移'}{step_um:.1f}µm")
            
            # 稍后更新坐标显示并同步到Stage窗口
            QTimer.singleShot(100, self.update_and_sync_coordinates)
            
        except Exception as e:
            print(f"Z轴{'下移' if direction > 0 else '上移'}时出错: {e}")

    def on_z_up_clicked(self):
        """Z轴上移按钮点击处理函数"""
        self.move_z_axis(-1, self.current_z_step)
    
    def on_z_down_clicked(self):
        """Z轴下移按钮点击处理函数"""
        self.move_z_axis(1, self.current_z_step)
    
    def on_z_zero_clicked(self):
        """Z电机回零按钮点击处理函数"""
        if not self.motor_connected or not self.motor_controller:
            self.connection_status.setText("未连接，请检查端口")
            self.connection_status.setStyleSheet("color: red;")
            print("电机控制器未初始化，无法回零Z轴")
            return
        
        try:
            # 检查是否有Z轴电机
            motor_count = len(self.motor_controller.motor_sum)
            if motor_count < 3:
                print("没有找到Z轴电机")
                return
            # 调用back_zero方法让Z轴回零
            self.motor_controller.back_zero(Axis.Z)
            
        except Exception as e:
            print(f"Z轴回中时出错: {e}")
    
    def on_motor_zero(self):
        """电机回零按钮点击处理"""
        if not self.motor_controller or not self.motor_controller.is_initialized:
            self.connection_status.setText("未连接，请检查端口")
            self.connection_status.setStyleSheet("color: red;")
            print("电机控制器未初始化，无法回零")
            return

            # 执行电机回零操作
        self.motor_controller.xy_back_zero()
        self.connection_status.setText("电机已回零")
        self.connection_status.setStyleSheet("color: green;")
        print("电机已回零")
        self.apply_speed_settings()

       

    def on_motor_center(self):
        """电机居中按钮点击处理"""
        if not self.motor_controller or not self.motor_controller.is_initialized:
            self.connection_status.setText("未连接，请检查端口")
            self.connection_status.setStyleSheet("color: red;")
            print("电机控制器未初始化，无法居中")
            return
            
        try:
            # 执行电机居中操作
            self.motor_controller.xy_center()
            self.connection_status.setText("电机已居中")
            self.connection_status.setStyleSheet("color: green;")
            print("电机已居中")
        except Exception as e:
            print(f"电机居中时出错: {e}")

    def on_motor_init(self):
        pass

    def apply_speed_settings(self):
        """应用扫描速度和加速度设置"""
        # 获取速度和加速度值
        speed = int(self.speed_edit.text())
        acc = int(self.acc_edit.text())
            
        # 应用到电机控制器
        if self.motor_controller and self.motor_controller.is_initialized:
            # 应用到所有电机
            for motor_id in self.motor_controller.motor_sum:
                self.motor_controller.set_speed(motor_id, speed, acc)
                
            self.connection_status.setText("速度和加速度已应用")
            self.connection_status.setStyleSheet("color: green;")
            print(f"速度和加速度已应用，速度: {speed}, 加速度: {acc}")
        else:
            self.connection_status.setText("电机未连接，无法应用设置")
            self.connection_status.setStyleSheet("color: red;")
            print("电机未连接，无法应用速度和加速度设置")
        

    def closeEvent(self, event):
        """关闭窗口时的处理"""
        try:
            # 保存当前状态
            self.save_state()
            
            # 停止位置更新定时器
            if hasattr(self, 'update_position_timer') and self.update_position_timer:
                self.update_position_timer.stop()
            
            # 停止相机线程
            if hasattr(self, 'camera_thread') and self.camera_thread:
                print("正在停止相机线程...")
                self.camera_thread.stop()
                print("相机线程已停止")
            
            # 兼容性处理：停止旧的定时器
            if hasattr(self, 'camera_timer') and self.camera_timer:
                self.camera_timer.stop()
            
            if hasattr(self, 'fps_timer') and self.fps_timer:
                self.fps_timer.stop()
                
            # 先关闭阶段控制窗口的线程（如果存在）
            if self.stage_window is not None:
                try:
                    # 检查是否有工作线程正在运行
                    if hasattr(self.stage_window, 'worker_thread') and self.stage_window.worker_thread.isRunning():
                        print("正在停止Stage窗口工作线程...")
                        self.stage_window.worker_thread.quit()
                        if not self.stage_window.worker_thread.wait(3000):  # 等待最多3秒
                            print("警告：Stage窗口工作线程未能正常退出，将强制终止")
                            self.stage_window.worker_thread.terminate()
                        else:
                            print("Stage窗口工作线程已成功停止")
                    
                    # 现在可以安全地关闭窗口
                    self.stage_window.close()
                    self.stage_window = None
                    print("Stage窗口已关闭")
                except Exception as stage_e:
                    print(f"关闭Stage窗口时出错: {stage_e}")
            
            # 释放自动对焦资源
            if hasattr(self, 'autofocus') and self.autofocus:
                try:
                    # 清除缓存和引用
                    if hasattr(self.autofocus, 'clear_cache'):
                        self.autofocus.clear_cache()
                    # 设置为None以帮助垃圾回收
                    self.autofocus = None
                    print("自动对焦资源已释放")
                except Exception as af_e:
                    print(f"释放自动对焦资源时出错: {af_e}")
            
            # 如果有相机连接，关闭相机
            if hasattr(self, 'tucam') and self.tucam:
                
                # 停止视频捕获
                self.tucam.StopCapture()
                self.tucam.CloseCamera()
                self.tucam.UnInitApi()
                self.camera_open = False
                
            
            # 如果有电机连接，停止所有电机
            if hasattr(self, 'motor_controller') and self.motor_controller and self.motor_controller.is_initialized:
                # 失能电机再停止连接
                for motor_id in self.motor_controller.motor_sum:
                    self.motor_controller.power_off(motor_id)
                self.motor_controller.stop()
                
        except Exception as e:
            print(f"关闭窗口时出错: {e}")
            import traceback
            traceback.print_exc()
        
        # 确保所有资源都被释放
        QApplication.processEvents()
        
        # 接受关闭事件
        event.accept()

    def init_camera(self):
        """初始化相机并启动视频流"""
        self.camera_status_label.setText("相机状态: 正在连接...")
        self.camera_status_label.setStyleSheet("color: orange;")
            
        self.tucam = Camera.Tucam()
            
        # 打开相机
        if self.tucam.OpenCamera(0):   
            # 设置相机参数
            self.tucam.set_isp(1 if self.isp_check.isChecked() else 0)
            self.tucam.set_enhance(1 if self.enhance_check.isChecked() else 0)
            
            # 设置翻转状态 - 默认开启水平和左右翻转
            self.tucam.set_horizontal_flip(1)
            self.horizontal_flip_check.setChecked(True)
            self.tucam.set_vertical_flip(1)
            self.vertical_flip_check.setChecked(True)
                
            # 设置其他相机参数
            self.tucam.set_global_gain(self.gain_slider.value())
            self.tucam.set_hue(self.hue_slider.value())
            self.tucam.set_brightness(self.light_slider.value())
            self.tucam.set_gamma(self.gamma_slider.value())
            self.tucam.set_saturation(self.saturation_slider.value())
            self.tucam.set_contrast(self.contrast_slider.value())
            self.tucam.set_sharpness(self.sharpen_slider.value())
                
            # 设置自动白平衡状态
            auto_wb = self.auto_wb_check.isChecked()
            self.tucam.set_atwbalance(1 if auto_wb else 0)
                
            # 设置自动曝光状态 - 默认为手动曝光模式
            auto_expo = self.auto_expo_check.isChecked()
            self.tucam.set_auto_exposure(1 if auto_expo else 0)
                
            # 如果处于手动曝光模式，设置初始曝光时间
            if not auto_expo:
                try:
                    initial_exposure = self.ms_spinbox.value()
                    self.tucam.set_exposure_time(initial_exposure)
                    print(f"初始曝光时间设置为: {initial_exposure}ms")
                except Exception as e:
                    print(f"设置初始曝光时间时出错: {e}")
                
            # 更新状态标签
            self.camera_status_label.setText("相机状态: 已连接")
            self.camera_status_label.setStyleSheet("color: green;")
            
            # 设置初始分辨率信息
            current_resolution = self.resolution_combo.currentText()
            self.camera_info_label.setText(f"分辨率: {current_resolution} | FPS: --")
            
            # 启动相机捕获
            self.tucam.StartCapture()
            # 连接相机控制
            self.connect_camera_controls()
            
            # 确保UI界面与初始相机设置同步
            # 根据自动曝光状态启用/禁用曝光时间控制
            self.ms_spinbox.setEnabled(not auto_expo)
            
            # 如果自动曝光已开启，从相机获取并更新当前曝光时间
            if auto_expo:
                QTimer.singleShot(20, self.update_exposure_from_camera)
            
            # 初始化自动对焦模块（延迟初始化到实际需要时）
            try:
                # 我们在这里仅创建实例但不执行具体对焦操作
                self.autofocus = PyTorchAutoFocus(use_gpu=True)
                self.autofocus.set_focus_method(self.focus_method)
                print("自动对焦模块已初始化")
            except Exception as e:
                print(f"自动对焦模块初始化失败: {e}")
                self.autofocus = None
            
            # 创建并启动相机线程
            if self.camera_thread:
                self.camera_thread.stop()
                
            self.camera_thread = CameraThread(self.tucam)
            
            # 连接信号槽
            self.camera_thread.frame_ready.connect(self.on_frame_ready)
            self.camera_thread.fps_updated.connect(self.on_fps_updated)
            self.camera_thread.photo_captured.connect(self.on_photo_captured)
            
            # 启动线程
            self.camera_thread.start()
            
            self.camera_open = True
            
    def on_frame_ready(self, frame):
        """处理从相机线程接收到的帧"""
        if frame is not None:
            # 更新到图像标签
            self.tucam.display_frame(self.image_display, frame)
            
            # 只有在非扫描状态下才执行对焦评分计算
            if not self.is_scanning and hasattr(self, 'autofocus') and self.autofocus is not None:
                try:
                    # 计算并更新对焦评分 - 每10帧更新一次
                    self.frame_count += 1
                    if self.frame_count % 10 == 0:
                        score = self.autofocus.calculate_focus_score(frame)
                        # 将评分值放大100倍以使其更易于阅读
                        self.focus_score_value.setText(f"{score*1000:.2f}")
                except Exception as e:
                    pass  # 忽略评分计算错误
    
    def on_fps_updated(self, fps):
        """更新FPS显示"""
        # 获取当前分辨率文本
        current_resolution = self.resolution_combo.currentText()
        
        # 更新信息标签
        self.camera_info_label.setText(f"分辨率: {current_resolution} | FPS: {fps:.1f}")
    
    def on_photo_captured(self, save_path, success):
        """处理照片拍摄信号"""
        if success:
            # 提取文件名用于显示
            filename = os.path.basename(save_path)
            self.statusBar().showMessage(f"照片已保存: {filename}", 1000)
            print(f"照片已保存至: {save_path}")
        
    def connect_camera_controls(self):
        """连接相机控制UI元素与它们的处理函数"""
        # 连接功能复选框
        self.isp_check.stateChanged.connect(self.on_isp_changed)
        self.enhance_check.stateChanged.connect(self.on_enhance_changed)
        self.auto_expo_check.stateChanged.connect(self.on_auto_exposure_changed)
        self.auto_wb_check.stateChanged.connect(self.on_auto_wb_changed)
        
        # 连接翻转复选框
        self.horizontal_flip_check.stateChanged.connect(self.on_horizontal_flip_changed)
        self.vertical_flip_check.stateChanged.connect(self.on_vertical_flip_changed)
        
        # 连接曝光时间控件
        self.ms_spinbox.valueChanged.connect(self.on_exposure_changed)
        # 连接全局增益滑块
        self.gain_slider.valueChanged.connect(self.on_gain_changed)
        # 连接色调滑块
        self.hue_slider.valueChanged.connect(self.on_hue_changed)
        # 连接亮度滑块
        self.light_slider.valueChanged.connect(self.on_light_changed)
        # 连接伽马滑块
        self.gamma_slider.valueChanged.connect(self.on_gamma_changed)
        # 连接饱和度滑块
        self.saturation_slider.valueChanged.connect(self.on_saturation_changed)
        # 连接对比度滑块
        self.contrast_slider.valueChanged.connect(self.on_contrast_changed)
        # 连接锐化滑块
        self.sharpen_slider.valueChanged.connect(self.on_sharpen_changed)
        # Connect bit depth combo box
        self.bit_depth_combo.currentIndexChanged.connect(self.on_bit_depth_changed)
    
    def _connect_resolution_combo(self):
        """连接分辨率下拉框到切换事件"""
        if hasattr(self, 'resolution_combo'):
            self.resolution_combo.currentIndexChanged.connect(self.on_resolution_changed)

    def on_resolution_changed(self, index):
        """分辨率下拉框选择改变处理函数"""
        if not self.camera_open or not self.tucam:
            return
            
        # 获取分辨率索引值
        resolution_value = index
        
        # 设置状态栏消息
        self.statusBar().showMessage("正在切换分辨率...", 1000)
        
        # 停止相机线程
        if self.camera_thread:
            self.camera_thread.stop()
        
        try:
            # 正确的顺序: 先停止捕获，然后设置分辨率，再开始捕获
            self.tucam.StopCapture()
            self.tucam.set_resolution(resolution_value)
            self.tucam.StartCapture()
            
            # 重新启动相机线程
            self.camera_thread = CameraThread(self.tucam)
            self.camera_thread.frame_ready.connect(self.on_frame_ready)
            self.camera_thread.fps_updated.connect(self.on_fps_updated)
            self.camera_thread.photo_captured.connect(self.on_photo_captured)
            self.camera_thread.start()
            
            # 显示成功消息
            current_resolution = self.resolution_combo.currentText()
            self.statusBar().showMessage(f"分辨率已切换: {current_resolution}", 3000)
            
        except Exception as e:
            print(f"切换分辨率时出错: {e}")
            self.statusBar().showMessage(f"分辨率切换失败: {str(e)}", 3000)
            
            # 出错时也需要尝试重新启动相机
            try:
                self.tucam.StartCapture()
                # 重新启动相机线程
                self.camera_thread = CameraThread(self.tucam)
                self.camera_thread.frame_ready.connect(self.on_frame_ready)
                self.camera_thread.fps_updated.connect(self.on_fps_updated)
                self.camera_thread.photo_captured.connect(self.on_photo_captured)
                self.camera_thread.start()
            except:
                pass

    def on_bit_depth_changed(self, index):
        """位深度下拉框选择改变处理函数"""
        if not self.camera_open or not self.tucam:
            return
            
        # 获取位深度值 (0 for 8bit, 1 for 16bit)
        bit_depth_value = 8 if index == 0 else 16
        
        # 设置状态栏消息
        self.statusBar().showMessage(f"正在切换位深度到 {bit_depth_value}bit...", 1000)
        
        # 停止相机线程
        if self.camera_thread:
            self.camera_thread.stop()
        
        try:
            # 停止捕获
            self.tucam.StopCapture()
            # 设置新的位深度
            self.tucam.set_bit_depth(bit_depth_value)
            # 重新开始捕获
            self.tucam.StartCapture()
            
            # 重新启动相机线程
            self.camera_thread = CameraThread(self.tucam)
            self.camera_thread.frame_ready.connect(self.on_frame_ready)
            self.camera_thread.fps_updated.connect(self.on_fps_updated)
            self.camera_thread.photo_captured.connect(self.on_photo_captured)
            self.camera_thread.start()
            
            # 显示成功消息
            bit_depth_text = self.bit_depth_combo.currentText()
            self.statusBar().showMessage(f"位深度已切换: {bit_depth_text}", 3000)
            
        except Exception as e:
            print(f"切换位深度时出错: {e}")
            self.statusBar().showMessage(f"位深度切换失败: {str(e)}", 3000)
            
            # 出错时也需要尝试重新启动相机
            try:
                self.tucam.StartCapture()
                # 重新启动相机线程
                self.camera_thread = CameraThread(self.tucam)
                self.camera_thread.frame_ready.connect(self.on_frame_ready)
                self.camera_thread.fps_updated.connect(self.on_fps_updated)
                self.camera_thread.photo_captured.connect(self.on_photo_captured)
                self.camera_thread.start()
            except:
                pass

    def update_after_resolution_change(self):
        """分辨率切换后更新图像和状态"""
        # 获取一帧图像
        image = self.tucam.get_frame()
        if image is not None:
            # 缩放图像适应显示区域并保持宽高比
            self.tucam.display_frame(self.image_display, image)
            
            # 获取当前分辨率文本
            current_resolution = self.resolution_combo.currentText()
            
            # 更新信息标签
            fps = self.tucam.get_fps()
            self.camera_info_label.setText(f"分辨率: {current_resolution} | FPS: {fps:.1f}")
            
            # 显示成功消息
            self.statusBar().showMessage(f"分辨率已切换: {current_resolution}", 3000)
        else:
            self.statusBar().showMessage("分辨率已切换，但图像获取失败", 3000)
        
        # 重新启动帧更新定时器
        self.camera_timer.start(0)
        
        # 延迟启动FPS更新定时器，给状态栏消息足够的显示时间
        QTimer.singleShot(1000, self.restart_fps_timer)

    def restart_fps_timer(self):
        """重新启动FPS更新定时器"""
        if hasattr(self, 'fps_timer') and not self.fps_timer.isActive():
            self.fps_timer.start(100)

    def on_isp_changed(self, state):
        """ISP复选框状态改变处理函数"""
        if not self.camera_open or not self.tucam:
            return
        
        self.tucam.set_isp(1 if state else 0)
        print(f"ISP已设置为: {'开启' if state else '关闭'}")
    
    def on_enhance_changed(self, state):
        """增强复选框状态改变处理函数"""
        if not self.camera_open or not self.tucam:
            return
        
        self.tucam.set_enhance(1 if state else 0)
        print(f"增强已设置为: {'开启' if state else '关闭'}")
    
    def on_auto_exposure_changed(self, state):
        """自动曝光复选框状态改变处理函数"""
        if not self.camera_open or not self.tucam:
            return
        
        try:
            # 设置相机自动曝光状态
            self.tucam.set_auto_exposure(1 if state else 0)
            print(f"自动曝光已设置为: {'开启' if state else '关闭'}")
            
            # 更新UI：启用/禁用曝光时间控件
            self.ms_spinbox.setEnabled(not state)
            
            # 如果自动曝光开启，延迟获取并更新曝光时间显示
            if state:
                QTimer.singleShot(200, self.update_exposure_from_camera)
        except Exception as e:
            print(f"切换自动曝光状态时出错: {e}")
            import traceback
            traceback.print_exc()
    
    def on_auto_wb_changed(self, state):
        """自动白平衡复选框状态改变处理函数"""
        if not self.camera_open or not self.tucam:
            return
        
        self.tucam.set_atwbalance(1 if state else 0)
        print(f"自动白平衡已设置为: {'开启' if state else '关闭'}")
            
    def update_exposure_from_camera(self):
        """从相机获取当前曝光时间并更新到ms_spinbox"""
        if not self.camera_open or not self.tucam:
            return
            
        try:
            # 获取当前相机曝光时间（毫秒）
            exposure_time = self.tucam.get_exposure_time()
            
            # 阻止信号触发以避免递归调用
            self.ms_spinbox.blockSignals(True)
            # 更新ms_spinbox的值
            self.ms_spinbox.setValue(exposure_time)
            self.ms_spinbox.blockSignals(False)
            
            print(f"从相机获取并更新曝光时间: {exposure_time}ms")
        except Exception as e:
            print(f"获取曝光时间出错: {e}")
    
    def on_exposure_changed(self, value):
        """曝光值改变处理函数"""
        if not self.camera_open or not self.tucam:
            return
        
        # 如果用户手动调整曝光值，则自动关闭自动曝光
        if self.auto_expo_check.isChecked():
            # 阻止信号触发以避免递归调用
            self.auto_expo_check.blockSignals(True)
            # 取消自动曝光选择
            self.auto_expo_check.setChecked(False)
            self.auto_expo_check.blockSignals(False)
            
            # 设置相机为手动曝光模式
            self.tucam.set_auto_exposure(0)
            print("手动调整曝光值，已关闭自动曝光")
    
        total_ms = value
        self.tucam.set_exposure_time(total_ms)
        print(f"曝光时间已设置为: {total_ms}ms")

    def on_gain_changed(self, value):
        """增益滑块值改变处理函数"""
        if not self.camera_open or not self.tucam:
            return
        self.tucam.set_global_gain(value)
        print(f"全局增益已设置为: {value}")
        
    
    def on_hue_changed(self, value):
        """色调滑块值改变处理函数"""
        if not self.camera_open or not self.tucam:
            return
        self.tucam.set_hue(value)
        print(f"色调已设置为: {value}")
        
    
    def on_light_changed(self, value):
        """亮度滑块值改变处理函数"""
        if not self.camera_open or not self.tucam:
            return
        self.tucam.set_brightness(value)
        print(f"亮度已设置为: {value}")
    
    def on_gamma_changed(self, value):
        """伽马滑块值改变处理函数"""
        if not self.camera_open or not self.tucam:
            return
        
        self.tucam.set_gamma(value)
        print(f"伽马已设置为: {value}")
    
    def on_saturation_changed(self, value):
        """饱和度滑块值改变处理函数"""
        if not self.camera_open or not self.tucam:
            return
        
        self.tucam.set_saturation(value)
        print(f"饱和度已设置为: {value}")
    
    def on_contrast_changed(self, value):
        """对比度滑块值改变处理函数"""
        if not self.camera_open or not self.tucam:
            return
        
        self.tucam.set_contrast(value)
        print(f"对比度已设置为: {value}")
    
    def on_sharpen_changed(self, value):
        """锐化滑块值改变处理函数"""
        if not self.camera_open or not self.tucam:
            return
        
        self.tucam.set_sharpness(value)
        print(f"锐化已设置为: {value}")
    
    def on_bit_changed(self):
        """位深度切换后更新图像和状态"""
        # 获取一帧图像
        image = self.tucam.get_frame()
        if image is not None:
            # 使用Camera.py中的display_frame方法显示图像
            self.tucam.display_frame(self.image_display, image)
            
            # 获取当前位深度值和分辨率
            bit_depth_text = self.bit_depth_combo.currentText()
            current_resolution = self.resolution_combo.currentText()
            
            # 更新信息标签
            fps = self.tucam.get_fps()
            self.camera_info_label.setText(f"分辨率: {current_resolution} | FPS: {fps:.1f} | {bit_depth_text}")
            
            # 显示成功消息
            self.statusBar().showMessage(f"位深度已切换: {bit_depth_text}", 3000)
        
        # 重新启动相机定时器
        self.camera_timer.start(0)
        
        # 延迟启动FPS更新定时器
        QTimer.singleShot(1000, self.restart_fps_timer)

    def on_capture_clicked(self):
        """拍照按钮点击事件处理"""
        if not self.camera_open or not self.tucam:
            QMessageBox.warning(self, "警告", "相机未连接，无法拍照")
            return
        
        try:
            # 获取保存路径和文件名
            save_dir = self.path_edit.text()
            filename = self.filename_edit.text()
            format_ext = self.format_combo.currentText().lower()
            
            # 检查目录是否存在，不存在则创建
            if not os.path.exists(save_dir):
                os.makedirs(save_dir)
                print(f"创建目录: {save_dir}")
            
            # 自动编号处理
            if self.auto_number_check.isChecked():
                # 查找目录中现有的文件，确定下一个编号
                existing_files = [f for f in os.listdir(save_dir) if f.startswith(filename) and f.endswith(f".{format_ext}")]
                
                # 提取现有文件的编号
                numbers = []
                for file in existing_files:
                    # 从文件名中提取数字部分
                    name_part = file[len(filename):-len(f".{format_ext}")]
                    if name_part.isdigit():
                        numbers.append(int(name_part))
                
                # 确定下一个编号
                next_number = 1
                if numbers:
                    next_number = max(numbers) + 1
                
                full_filename = f"{filename}{next_number:04d}.{format_ext}"
            else:
                # 不自动编号，但确保有扩展名
                if not filename.endswith(f".{format_ext}"):
                    full_filename = f"{filename}.{format_ext}"
                else:
                    full_filename = filename
            
            # 构建完整的文件路径
            full_path = os.path.join(save_dir, full_filename)
            
            # 使用相机线程拍照
            if self.camera_thread:
                # 通过线程拍照，结果会通过信号返回
                self.camera_thread.capture_photo(full_path)
            else:
                # 如果没有相机线程，直接使用tucam拍照（兼容旧代码）
                result = self.tucam.SaveImage(full_path)
                if result:
                    self.statusBar().showMessage(f"图像已保存至: {full_path}", 3000)
                    print(f"图像已保存至: {full_path}")
                else:
                    self.statusBar().showMessage("图像保存失败", 3000)
                    print("图像保存失败")
        
        except Exception as e:
            QMessageBox.critical(self, "错误", f"保存图像时出错: {str(e)}")
            print(f"保存图像时出错: {e}")
            import traceback
            traceback.print_exc()

    def on_stop_start_clicked(self):
        """停止/开始按钮点击事件处理"""
        if not self.camera_open or not self.tucam:
            QMessageBox.warning(self, "警告", "相机未连接")
            return
        
        try:
            # 检查按钮当前文本，确定当前状态
            if self.stop_btn.text() == "停止":
                # 当前是播放状态，需要停止
                # 停止相机线程
                if self.camera_thread:
                    self.camera_thread.stop()
                
                self.tucam.StopCapture()
                
                # 更新按钮文本和图标
                self.stop_btn.setText("开始")
                self.stop_btn.setIcon(QIcon.fromTheme("media-playback-start"))
                
                # 更新状态
                self.camera_status_label.setText("相机状态: 已暂停")
                self.camera_status_label.setStyleSheet("color: orange;")
                
                self.statusBar().showMessage("相机捕获已停止", 2000)
            else:
                # 当前是停止状态，需要开始
                self.tucam.StartCapture()
                
                # 重新启动相机线程
                self.camera_thread = CameraThread(self.tucam)
                self.camera_thread.frame_ready.connect(self.on_frame_ready)
                self.camera_thread.fps_updated.connect(self.on_fps_updated)
                self.camera_thread.photo_captured.connect(self.on_photo_captured)
                self.camera_thread.start()
                
                # 更新按钮文本和图标
                self.stop_btn.setText("停止")
                self.stop_btn.setIcon(QIcon.fromTheme("media-playback-pause"))
                
                # 更新状态
                self.camera_status_label.setText("相机状态: 已连接")
                self.camera_status_label.setStyleSheet("color: green;")
                
                self.statusBar().showMessage("相机捕获已开始", 2000)
        
        except Exception as e:
            QMessageBox.critical(self, "错误", f"切换相机状态时出错: {str(e)}")
            print(f"切换相机状态时出错: {e}")
            import traceback
            traceback.print_exc()
        
    def on_format_changed(self, index):
        """处理保存格式变化"""
        if not self.camera_open or not self.tucam:
            return
        # 格式映射表
        format_map = {
            'jpg': TUIMG_FORMATS.TUFMT_JPG.value,
            'png': TUIMG_FORMATS.TUFMT_PNG.value,
            'tif': TUIMG_FORMATS.TUFMT_TIF.value,
            'bmp': TUIMG_FORMATS.TUFMT_BMP.value,
            'raw': TUIMG_FORMATS.TUFMT_RAW.value}
            
        # 获取当前选择的格式
        current_format = self.format_combo.currentText().lower()
            
        # 更新相机的保存格式
        if current_format in format_map:
            self.tucam.m_fs.nSaveFmt = format_map[current_format]
            print(f"保存格式已设置为: {current_format}")
        else:
            print(f"不支持的格式: {current_format}")

    

    

    def on_sharpen_changed(self, value):
        """锐化滑块值改变处理函数"""
        if not self.camera_open or not self.tucam:
            return
        
        try:
            self.tucam.set_sharpness(value)
            print(f"锐化已设置为: {value}")
        except Exception as e:
            print(f"设置锐化时出错: {e}")
    
    def on_horizontal_flip_changed(self, state):
        """水平翻转复选框状态改变处理函数"""
        if not self.camera_open or not self.tucam:
            return
        
        self.tucam.set_horizontal_flip(1 if state else 0)
        print(f"水平翻转已设置为: {'开启' if state else '关闭'}")

    def on_vertical_flip_changed(self, state):
        """垂直翻转复选框状态改变处理函数"""
        if not self.camera_open or not self.tucam:
            return
        
        self.tucam.set_vertical_flip(1 if state else 0)
        print(f"垂直翻转已设置为: {'开启' if state else '关闭'}")

    def update_frame(self):
        """更新图像帧"""
        if not self.camera_open or not self.tucam:
            return
            
        # 获取帧
        frame = self.tucam.get_frame()
        
        if frame:
            # 更新到图像标签
            self.tucam.display_frame(self.image_display, frame)
            
            # 只有在非扫描状态下才执行对焦评分计算
            if not self.is_scanning and hasattr(self, 'frame_count'):
                self.frame_count += 1
                if self.frame_count % 10 == 0 and hasattr(self, 'autofocus') and self.autofocus is not None:
                    try:
                        # 计算并更新对焦评分
                        score = self.autofocus.calculate_focus_score(frame)
                        # 将评分值放大100倍以使其更易于阅读
                        self.focus_score_value.setText(f"{score*1000:.2f}")
                    except Exception as e:
                        pass  # 忽略评分计算错误
    
    def update_fps_display(self):
        """更新FPS显示"""
        if not self.camera_open or not self.tucam:
            return
            
        # 获取当前帧率
        fps = self.tucam.get_fps()
        
        # 获取当前分辨率文本
        current_resolution = self.resolution_combo.currentText()
        
        # 更新信息标签
        self.camera_info_label.setText(f"分辨率: {current_resolution} | FPS: {fps:.1f}")
        

    def on_magnification_changed(self, button):
        """物镜倍数按钮点击处理函数"""
        try:
            if not button:
                return
                
            # 获取选中的倍数
            magnification_text = button.text()
            magnification = int(magnification_text.replace('x', ''))
            
            # 基准尺寸（5x物镜下的尺寸）- 使用Config类的默认值
            base_width = Config.DEFAULT_GRID_WIDTH
            base_height = Config.DEFAULT_GRID_HEIGHT
            
            # 根据倍数计算实际尺寸
            if magnification == 5:  # 5x是基准
                width = base_width
                height = base_height
            else:
                # 倍数越大，视野越小
                scale_factor = 5.0 / magnification
                width = base_width * scale_factor
                height = base_height * scale_factor
            
            # 更新尺寸输入框
            self.width_edit.setText(f"{width:.4f}")
            self.height_edit.setText(f"{height:.4f}")
            
            # 如果stage窗口已经打开，更新网格尺寸
            if self.stage_window is not None and not self.stage_window.isHidden():
                if hasattr(self.stage_window, 'grid_width'):
                    self.stage_window.grid_width = width
                    self.stage_window.grid_height = height
                    print(f"更新stage窗口网格尺寸为: {width:.4f}mm x {height:.4f}mm")
                    
                    # 如果有现有网格点，询问用户是否更新网格
                    if hasattr(self.stage_window, 'grid_points') and self.stage_window.grid_points:
                        # 在实际应用中，可能需要弹出对话框询问用户
                        # 这里我们自动清空现有网格，以便用户重新生成
                        self.stage_window.plotter_widget.clear_grid()
                        print("已清除现有网格点，请重新创建网格")
            
            print(f"物镜倍数已设置为: {magnification_text}, 图片尺寸: {width:.4f}mm x {height:.4f}mm")
        except Exception as e:
            print(f"设置物镜倍数时出错: {e}")
            import traceback
            traceback.print_exc()
    
    def on_z_step_selected(self, value):
        """Z轴步距选择按钮点击处理函数"""
        self.current_z_step = float(value)
        print(f"Z轴步距已设置为: {self.current_z_step}µm")

    def on_focus_method_changed(self, text):
        """对焦方法下拉框选择改变处理函数"""
        self.focus_method = text
        print(f"对焦方法已设置为: {self.focus_method}")

    def on_focus_clicked(self, focus_level):
        """对焦按钮点击处理函数"""
        if not self.camera_open or not self.tucam:
            self.focus_status.setText("对焦状态: 相机未连接")
            self.focus_status.setStyleSheet("color: red;")
            return False
            
        if not self.motor_controller or not self.motor_controller.is_initialized:
            self.focus_status.setText("对焦状态: 电机未连接")
            self.focus_status.setStyleSheet("color: red;")
            return False
        
        # 记录当前选中的按钮
        self.current_focus_btn = focus_level
        
        # 禁用所有焦点按钮，防止重复点击
        self.coarse_focus_btn.setEnabled(False)
        self.medium_focus_btn.setEnabled(False)
        self.fine_focus_btn.setEnabled(False)
        self.auto_focus_btn.setEnabled(False)
        
        # 设置当前按钮样式，指示正在进行的对焦类型
        focus_button_map = {
            "coarse": self.coarse_focus_btn,
            "medium": self.medium_focus_btn,
            "fine": self.fine_focus_btn,
            "auto": self.auto_focus_btn
        }
        
        focus_button_map[focus_level].setStyleSheet("background-color: #e93d3d; color: white;")
        
        try:
            # 初始化自动对焦对象（如果尚未初始化）
            if self.autofocus is None:
                self.autofocus = PyTorchAutoFocus(use_gpu=True)
                self.autofocus.set_focus_method(self.focus_method)
                self.focus_status.setText("对焦状态: 自动对焦模块已初始化")
            else:
                # 更新对焦方法
                self.autofocus.set_focus_method(self.focus_method)
            
            # 如果是自动对焦，调用完整对焦流程并返回
            if focus_level == "auto":
                # 恢复按钮状态，因为_perform_full_auto_focus会再次禁用
                self.reset_focus_buttons()
                # 自动对焦会从粗到细自动完成整个过程
                return self._perform_full_auto_focus()
            
            # 根据对焦等级设置参数 - 优化步长和最大步数以提高速度
            focus_params = {
                # 粗对焦：增大步长，减少步数，提高初始搜索速度
                "coarse": {"step_size": 150, "min_step": 20, "max_steps": 6, "name": "粗对焦"},
                # 中对焦：适当增大步长，减少步数，加快中等精度搜索
                "medium": {"step_size": 30, "min_step": 3, "max_steps": 5, "name": "中对焦"},
                # 细对焦：保持较小步长，但减少步数，减少不必要的小步调整
                "fine": {"step_size": 2, "min_step": 0.5, "max_steps": 5, "name": "细对焦"}
            }
            
            params = focus_params[focus_level]
            self.focus_status.setText(f"对焦状态: {params['name']}中...")
            
            # 优化进度回调函数，减少UI更新频率
            update_counter = 0
            def update_progress(current_z, best_z, progress_pct):
                nonlocal update_counter
                update_counter += 1   
                QApplication.processEvents()
                
            
            # 执行爬坡对焦算法 - 使用Config类简化坐标转换
            best_z, score = self.autofocus.focus_hill_climbing(
                get_current_z=lambda: Config.pulse_to_physical(self.motor_controller.get_position(Axis.Z), Axis.Z),
                move_to_z=lambda z: (
                    self.motor_controller.move_to_target_position(Axis.Z, Config.physical_to_pulse(z, Axis.Z)),
                    self.motor_controller.wait_target_position_reached(Axis.Z)
                ),
                get_image=lambda: self.tucam.get_frame(),
                step_size=params["step_size"],
                min_step=params["min_step"],
                max_steps=params["max_steps"],
                progress_callback=update_progress
            )
            
            # 移动到最佳对焦位置
            z_pulse = Config.physical_to_pulse(best_z, Axis.Z)
            self.motor_controller.move_to_target_position(Axis.Z, z_pulse)
            self.motor_controller.wait_target_position_reached(Axis.Z)
            
            # 更新主界面坐标并同步到Stage窗口
            self.update_and_sync_coordinates()
            
            # 更新状态和评分显示
            self.focus_status.setText(f"对焦状态: {params['name']}完成 | Z: {best_z:.2f}μm | 评分: {score*1000:.2f}")
            self.focus_status.setStyleSheet("color: green;")
            self.focus_score_value.setText(f"{score*1000:.2f}")
            
            print(f"{params['name']}完成，最佳焦平面位置: {best_z:.2f}µm, 分数: {score*1000:.2f}")
            
            return {"best_z": best_z, "score": score}
            
        except Exception as e:
            print(f"对焦操作时出错: {e}")
            traceback.print_exc()
            self.focus_status.setText(f"对焦状态: 出错 - {str(e)}")
            self.focus_status.setStyleSheet("color: red;")
            return False
        finally:
            # 恢复按钮状态
            self.reset_focus_buttons()
    
    def get_frame_as_qimage(self):
        """获取当前帧并转换为QImage供自动对焦使用
        
        Returns:
            QImage: 当前帧的QImage对象，失败时返回None
        """
        if not self.camera_open or not self.tucam:
            return None
            
        try:
            # 获取当前帧
            frame = self.tucam.get_frame()
            if frame is None:
                print("获取帧失败，无法执行对焦")
                return None
                
            return frame
        except Exception as e:
            print(f"获取QImage帧时出错: {e}")
            traceback.print_exc()
            return None

    def save_state(self):
        """保存当前状态"""
        try:
            # 如果stage窗口存在，调用其保存函数
            if self.stage_window is not None:
                # Stage窗口有自己的保存方法
                self.stage_window.save_state()
                print(f"已调用Stage窗口保存状态方法")
        except Exception as e:
            print(f"保存状态时出错: {e}")
            import traceback
            traceback.print_exc()

    def load_state(self):
        """从状态文件加载Stage窗口的状态"""
        # 不需要做任何事情，Stage窗口会在创建时自动加载其状态文件
        pass

    

    def on_add_point_focus_clicked(self):
        """处理添加点对焦按钮点击事件 - 遍历Stage表格中的所有点并执行自动对焦"""
        # 验证前置条件
        if not hasattr(self.stage_window, 'table_model') or self.stage_window.table_model.rowCount() == 0:
            QMessageBox.warning(self, "警告", "请先在Stage窗口添加至少一个点")
            return
            
        if not self.camera_open or not self.tucam:
            QMessageBox.warning(self, "警告", "相机未连接，无法执行对焦")
            return
            
        if not self.motor_controller or not self.motor_controller.is_initialized:
            QMessageBox.warning(self, "警告", "电机未连接，无法执行对焦")
            return
            
        total_points = self.stage_window.table_model.rowCount()
        
        # 禁用按钮，防止重复操作
        add_point_focus_btn = self.findChild(QPushButton, "添加点对焦")
        if add_point_focus_btn:
            add_point_focus_btn.setEnabled(False)
        
        # 记录原始对焦方法
        original_focus_method = self.focus_method
        
        try:
            # 遍历所有点并执行自动对焦
            for i in range(total_points):
                # 更新状态和选中当前点
                self.focus_status.setText(f"对焦状态: 正在处理点 {i+1}/{total_points}...")
                if hasattr(self.stage_window, 'table_view'):
                    self.stage_window.table_view.selectRow(i)
                QApplication.processEvents()
                
                # 移动到目标点
                print(f"开始移动到点 {i+1}")
                if hasattr(self.stage_window, 'on_move_to_clicked'):
                    self.stage_window.on_move_to_clicked()
                
                # Stage窗口的移动方法已包含等待逻辑，无需额外等待
                QApplication.processEvents()
                
                # 执行自动对焦
                self.focus_status.setText(f"对焦状态: 点 {i+1} 开始自动对焦...")
                QApplication.processEvents()
                print(f"点 {i+1} 开始自动对焦")
                
                # 调用统一的自动对焦方法
                self.on_focus_clicked("auto")
                
                # 更新表格中的坐标
                self.stage_window.table_model.setItem(i, 1, QStandardItem(f"{self.current_x_mm:.3f}"))
                self.stage_window.table_model.setItem(i, 2, QStandardItem(f"{self.current_y_mm:.3f}"))
                self.stage_window.table_model.setItem(i, 3, QStandardItem(f"{self.current_z_um:.3f}"))
                
                # 同步到绘图区
                if hasattr(self.stage_window, 'sync_table_to_plot'):
                    self.stage_window.sync_table_to_plot()
                
                # 更新Stage窗口状态
                point_num = self.stage_window.table_model.item(i, 0).text()
                if hasattr(self.stage_window, 'status_label'):
                    self.stage_window.status_label.setText(f"状态: 已更新点 #{point_num} 为 ({self.current_x_mm:.3f}, {self.current_y_mm:.3f}, {self.current_z_um:.3f})")
                
                print(f"完成点 {i+1} 对焦，坐标: X={self.current_x_mm:.3f}, Y={self.current_y_mm:.3f}, Z={self.current_z_um:.3f}")
                QApplication.processEvents()
                
            # 完成后创建网格
            if hasattr(self.stage_window, 'on_create_grid_clicked') and total_points > 0:
                self.focus_status.setText("对焦状态: 所有点对焦完成，正在创建网格...")
                QApplication.processEvents()
                self.stage_window.on_create_grid_clicked()
            
            # 更新完成状态
            self.focus_status.setText("对焦状态: 所有点对焦和网格创建完成")
            self.focus_status.setStyleSheet("color: green;")
            
        except Exception as e:
            QMessageBox.critical(self, "错误", f"执行点对焦时出错: {str(e)}")
            print(f"执行点对焦时出错: {e}")
            import traceback
            traceback.print_exc()
            
            self.focus_status.setText(f"对焦状态: 执行点对焦时出错")
            self.focus_status.setStyleSheet("color: red;")
            
        finally:
            # 恢复原始对焦方法
            self.focus_method = original_focus_method
            if self.autofocus:
                self.autofocus.set_focus_method(self.focus_method)
            
            # 重新启用界面按钮
            if add_point_focus_btn:
                add_point_focus_btn.setEnabled(True)


    def on_nice_focus_clicked(self):
        pass
    
    def on_sixteen_focus_clicked(self):
        pass

    def reset_focus_buttons(self):
        """重置所有对焦按钮的状态"""
        self.coarse_focus_btn.setEnabled(True)
        self.medium_focus_btn.setEnabled(True)
        self.fine_focus_btn.setEnabled(True)
        self.auto_focus_btn.setEnabled(True)
        
        # 清除高亮样式
        self.coarse_focus_btn.setStyleSheet("")
        self.medium_focus_btn.setStyleSheet("")
        self.fine_focus_btn.setStyleSheet("")
        self.auto_focus_btn.setStyleSheet("")
    
    def _perform_full_auto_focus(self):
        """执行完整的自动对焦流程（从粗到细）"""
        if not self.camera_open or not self.tucam or not self.motor_controller or not self.motor_controller.is_initialized:
            self.focus_status.setText("对焦状态: 相机或电机未就绪")
            self.focus_status.setStyleSheet("color: red;")
            return False
            
        # 禁用所有焦点按钮，防止重复点击
        self.coarse_focus_btn.setEnabled(False)
        self.medium_focus_btn.setEnabled(False)
        self.fine_focus_btn.setEnabled(False)
        self.auto_focus_btn.setEnabled(False)
        
        # 确保UI界面与初始相机设置同步
        QApplication.processEvents()
        
        # 高亮显示当前操作按钮
        self.auto_focus_btn.setStyleSheet("background-color: #e93d3d; color: white;")
        
        # 更新状态
        self.focus_status.setText("对焦状态: 多阶段自动对焦中...")
        QApplication.processEvents()  # 确保状态更新显示
        
        # 记录当前的对焦方法
        original_focus_method = self.focus_method
        # 记录开始时间，用于计算总耗时
        start_time = time.time()
        
        try:
            # 确保自动对焦对象已初始化
            if self.autofocus is None:
                self.autofocus = PyTorchAutoFocus(use_gpu=True)
                self.autofocus.set_focus_method(self.focus_method)
            
            # 用更快的参数执行一次对焦
            params = {"step_size": 80, "min_step": 0.2, "max_steps": 12, "name": "快速对焦"}
            
            # 更新状态
            self.focus_status.setText("对焦状态: 快速对焦中...")
            QApplication.processEvents()
            
            # 定义进度回调函数 - 使用每次回调都触发UI更新
            def update_progress(current_z, best_z, progress_pct):
                # 更新状态文本，显示当前进度
                self.focus_status.setText(f"对焦状态: 快速对焦中 Z={current_z:.2f}μm 进度:{progress_pct:.0f}%")
                # 允许UI刷新
                QApplication.processEvents()
                return True
            
            # 执行优化后的对焦算法 - 使用Config类简化坐标转换
            best_z, score = self.autofocus.focus_hill_climbing(
                # 使用Config类转换脉冲到微米
                get_current_z=lambda: Config.pulse_to_physical(self.motor_controller.get_position(Axis.Z), Axis.Z),
                move_to_z=lambda z: (
                    # 使用Config类转换微米到脉冲
                    self.motor_controller.move_to_target_position(Axis.Z, Config.physical_to_pulse(z, Axis.Z)), 
                    self.motor_controller.wait_target_position_reached(Axis.Z),
                    QApplication.processEvents()  # 确保UI响应
                ),
                get_image=lambda: self.tucam.get_frame(),
                step_size=params["step_size"],
                min_step=params["min_step"],
                max_steps=params["max_steps"],
                progress_callback=update_progress
            )
            
            # 计算总耗时
            total_time = time.time() - start_time
            
            # 移动到最佳对焦位置
            self.focus_status.setText(f"对焦状态: 移动到最佳焦点 Z={best_z:.2f}μm")
            QApplication.processEvents()
            
            # 使用Config类转换微米到脉冲
            z_pulse = Config.physical_to_pulse(best_z, Axis.Z)
            self.motor_controller.move_to_target_position(Axis.Z, z_pulse)
            self.motor_controller.wait_target_position_reached(Axis.Z)
            
            # 更新主界面坐标并同步到Stage窗口
            self.update_and_sync_coordinates()
            QApplication.processEvents()
            
            # 更新状态和评分显示
            self.focus_status.setText(f"对焦状态: 自动对焦完成 | Z: {best_z:.2f}μm | 评分: {score*1000:.2f} | 耗时: {total_time:.1f}秒")
            self.focus_status.setStyleSheet("color: green;")
            self.focus_score_value.setText(f"{score*1000:.2f}")
            QApplication.processEvents()
            
            print(f"自动对焦完成，最佳焦平面位置: {best_z:.2f}µm, 分数: {score*1000:.2f}, 耗时: {total_time:.1f}秒")
            
            # 返回对焦结果字典，包含最佳Z位置和对焦分数
            return {"best_z": best_z, "score": score}
            
        except Exception as e:
            print(f"自动对焦操作时出错: {e}")
            traceback.print_exc()
            self.focus_status.setText(f"对焦状态: 出错 - {str(e)}")
            self.focus_status.setStyleSheet("color: red;")
            QApplication.processEvents()
            return False
            
        finally:
            # 恢复按钮状态
            self.reset_focus_buttons()
            QApplication.processEvents()
            
            # 恢复原来的对焦方法
            self.focus_method = original_focus_method
            if self.autofocus:
                self.autofocus.set_focus_method(self.focus_method)

    def _generate_position_files(self, grid_points):
        """生成位置文件，用于后续拼接"""
        # 获取当前分辨率
        resolution = self.resolution_combo.currentText()
        width, height = map(float, resolution.split('x'))
            
        # 获取物镜倍数的图片尺寸参数 
        image_width = float(self.width_edit.text())  # mm
        image_height = float(self.height_edit.text())  # mm
            
        # 获取第一个点的坐标作为原点
        if len(grid_points) == 0:
            print("没有网格点，无法生成位置文件")
            return
            
        # 处理2D或3D点
        if len(grid_points[0]) == 3:
            x0, y0, z0 = grid_points[0]
        else:
            x0, y0 = grid_points[0]
            z0 = 0
            
        # 计算所有转换后的实际坐标和分辨率坐标
        transformed_real_coords = []
        transformed_res_coords = []
        
        for point in grid_points:
            # 提取坐标
            if len(point) == 3:
                x, y, z = point
            else:
                x, y = point
                z = 0
                
            # 计算相对于原点的偏移，反转X方向
            transformed_x = (x - x0) * -1
            transformed_y = y - y0
            transformed_z = z - z0
            
            # 保存实际物理坐标
            transformed_real_coords.append((transformed_x, transformed_y, transformed_z))
            
            # 计算分辨率坐标
            res_x = transformed_x / image_width * width
            res_y = transformed_y / image_height * height
            transformed_res_coords.append((res_x, res_y, transformed_z))
            
        # 生成物理位置配置文件
        self._write_position_file(
            os.path.join(self.save_dir, "TileConfiguration_read_position.txt"),
            transformed_real_coords,
            "实际"
        )
            
        # 生成分辨率位置配置文件
        self._write_position_file(
            os.path.join(self.save_dir, "TileConfiguration.txt"),
            transformed_res_coords,
            "分辨率"
        )
            
    def _write_position_file(self, filepath, coordinates, coord_type):
        """写入位置文件"""
        try:
            with open(filepath, 'w') as f:
                f.write("# Define the number of dimensions we are working on\n")
                f.write("dim = 2\n\n")
                f.write("# Define the image coordinates\n")
                    
                for i, coord in enumerate(coordinates):
                    # 只使用坐标的前两个值（x,y）
                    x, y = coord[0], coord[1]
                    f.write(f"s_{i+1:04d}.jpg ; ; ({x:.3f},{y:.3f})\n")
                    
            print(f"已生成{coord_type}位置文件: {filepath}")
        except Exception as e:
            print(f"生成{coord_type}位置文件时出错: {e}")
            traceback.print_exc()

    def init_scan_counter(self):
        """初始化扫描计数器和保存目录"""
        # 初始化图片计数器
        self.image_counter = 1
        # 确保目录存在
        save_dir = os.path.join(self.path_edit.text().strip(), self.filename_edit.text().strip())
        self.save_dir = save_dir
        os.makedirs(save_dir, exist_ok=True)
        print(f"图片将保存到目录: {save_dir}")
    
    def finish_scan(self, stage_window):
        """完成扫描并清理资源"""
        # 扫描完成，所有图像已保存
        self.scan_btn.setText("执行扫描")
        self.connection_status.setText("扫描已完成")
        self.connection_status.setStyleSheet("color: green;")
        
        # 停止监视定时器
        if hasattr(self, 'scan_monitor_timer') and self.scan_monitor_timer.isActive():
            self.scan_monitor_timer.stop()
        
        # 恢复对焦评分计算
        self.is_scanning = False
        if hasattr(self, 'focus_score_value'):
            self.focus_score_value.setText("0.00")  # 重置评分显示
        print("扫描完成，对焦评分计算已恢复")
        
        # 计算总耗时
        if hasattr(stage_window, 'scan_start_time') and stage_window.scan_start_time is not None:
            total_time = time.time() - stage_window.scan_start_time
            minutes = int(total_time // 60)
            seconds = int(total_time % 60)
            print(f"扫描完成，共扫描 {len(stage_window.grid_points)} 个点，总耗时: {minutes}分{seconds}秒")
        else:
            print(f"扫描完成，共扫描 {len(stage_window.grid_points)} 个点")

        # 生成位置文件
        # 获取当前分辨率
        resolution = self.resolution_combo.currentText()
        width, height = map(float, resolution.split('x'))
            
        # 获取物镜倍数的图片尺寸参数
        image_width = float(self.width_edit.text())  # mm
        image_height = float(self.height_edit.text())  # mm
            
            # 获取第一个点的坐标作为原点
        if len(stage_window.grid_points[0]) == 3:
            x0, y0, z0 = stage_window.grid_points[0]
        else:
            x0, y0 = stage_window.grid_points[0]
            z0 = 0
            
        # 计算所有转换后的实际坐标
        transformed_real_coords = []
        for point in stage_window.grid_points:
            if len(point) == 3:
                x, y, z = point
            else:
                x, y = point
                z = 0
            transformed_x = (x - x0) * -1
            transformed_y = y - y0
            transformed_z = z - z0
            transformed_real_coords.append((transformed_x, transformed_y, transformed_z))
            
            # 计算所有转换后的分辨率坐标
        transformed_res_coords = []
        for real_x, real_y, real_z in transformed_real_coords:
            res_x = real_x / image_width * width
            res_y = real_y / image_height * height
            transformed_res_coords.append((res_x, res_y, real_z))
            
            # 生成TileConfiguration_read_position.txt
        real_file = os.path.join(self.save_dir, "TileConfiguration_read_position.txt")
        with open(real_file, 'w') as f:
            f.write("# Define the number of dimensions we are working on\n")
            f.write("dim = 2\n\n")
            f.write("# Define the image coordinates\n")
                
            for i, (x, y, z) in enumerate(transformed_real_coords):
                f.write(f"s_{i+1:04d}.jpg ; ; ({x:.3f},{y:.3f},{z:.3f})\n")
        print(f"已生成实际位置文件: {real_file}")
            
        # 生成TileConfiguration.txt
        resolution_file = os.path.join(self.save_dir, "TileConfiguration.txt")
        with open(resolution_file, 'w') as f:
            f.write("# Define the number of dimensions we are working on\n")
            f.write("dim = 2\n\n")  # 2D坐标
            f.write("# Define the image coordinates\n")
                
            for i, (x, y, z) in enumerate(transformed_res_coords):
                f.write(f"s_{i+1:04d}.jpg ; ; ({x:.3f},{y:.3f})\n")
        print(f"已生成分辨率位置文件: {resolution_file}")
       

    def on_scan_clicked(self):
        """执行扫描功能"""
        # 检查Stage窗口是否已打开且有网格点
        if not hasattr(self, 'stage_window') or self.stage_window is None:
            QMessageBox.warning(self, "警告", "请先打开扫描网格设置窗口并创建网格")
            return
            
        if not hasattr(self.stage_window, 'grid_points') or not self.stage_window.grid_points:
            QMessageBox.warning(self, "警告", "请先在Stage窗口创建扫描网格")
            return
            
        # 检查相机连接状态
        if not self.camera_open or not self.tucam:
            QMessageBox.warning(self, "警告", "相机未连接，无法执行扫描拍照")
            return
            
        # 初始化扫描计数器和保存目录
        self.init_scan_counter()
        # 设置拍照回调函数
        self.scan_capture_slot = self.capture_at_position
        # 禁用对焦评分计算（避免扫描时计算影响性能）
        self.is_scanning = True
        # 调用Stage窗口的扫描功能
        self.stage_window.on_scan_clicked()
        # 更新按钮状态
        self.scan_btn.setText("扫描中...")
        self.connection_status.setText("正在执行扫描...")
        self.connection_status.setStyleSheet("color: orange;")

    def capture_at_position(self):
        """在当前位置拍照"""
        # 构建文件名
        filename = f"s_{self.image_counter:04d}"
        save_path = os.path.join(self.save_dir, filename)
        # 拍照保存 - 使用lambda函数延迟执行
        QTimer.singleShot(30, lambda: self.tucam.SaveImage(save_path))
        #print(f"图片已保存: {filename}")
        self.image_counter += 1
        
    def capture_and_move_next(self):
        """当电机移动到位后，拍照并准备移动到下一个位置"""
        # 先拍照
        self.capture_at_position()
        # 然后继续扫描流程
        if hasattr(self, 'stage_window') and self.stage_window:
            # 调用Stage窗口的下一步扫描
            self.stage_window.scan_next_position()

# 主程序入口
if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = MicroscopeControlUI()
    window.show()
    sys.exit(app.exec())
