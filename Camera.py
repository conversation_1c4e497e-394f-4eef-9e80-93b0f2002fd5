#!/usr/bin/env python
# coding: utf-8
'''
Created on 2024-01-04
@author:fdy
'''
import os
from datetime import datetime
import ctypes
from ctypes import *
from TUCam import *
from enum import Enum
import time
import sys
import numpy as np
import threading
from PySide6.QtWidgets import QA<PERSON>lication, QMainWindow, QPushButton, QVBoxLayout, QHBoxLayout, QWidget, QLabel, QGroupBox, QComboBox, QSlider, QSizePolicy, QDoubleSpinBox
from PySide6.QtGui import QImage, QPixmap, QTransform
from PySide6.QtCore import QTimer, Qt

class Tucam():
    def __init__(self):
        self.Path = './'
        self.TUCAMINIT = TUCAM_INIT(0, self.Path.encode('utf-8'))
        self.TUCAMOPEN = TUCAM_OPEN(0, 0)
        TUCAM_Api_Init(pointer(self.TUCAMINIT), 5000)
        #保存格式
        self.m_fs = TUCAM_FILE_SAVE()
        self.m_fs.nSaveFmt = TUIMG_FORMATS.TUFMT_JPG.value
        #帧结构
        self.m_frame = TUCAM_FRAME()
        self.m_frame.ucFormatGet = TUFRM_FORMATS.TUFRM_FMT_USUAl.value
        self.m_frame.uiRsdSize = 1
        
        
        print(self.TUCAMINIT.uiCamCount)
        print(self.TUCAMINIT.pstrConfigPath)
        print('Connect %d camera' %self.TUCAMINIT.uiCamCount)
        
        
    def OpenCamera(self, Idx):
        if Idx >= self.TUCAMINIT.uiCamCount:
            print('相机索引超出范围')
            return False

        self.TUCAMOPEN = TUCAM_OPEN(Idx, 0)
        result = TUCAM_Dev_Open(pointer(self.TUCAMOPEN))

        if result != TUCAMRET.TUCAMRET_SUCCESS:
            print(f'打开相机失败! 错误码: {result}')
            return False
        else:
            print('打开相机成功!')
            return True

    def CloseCamera(self):
        if 0 != self.TUCAMOPEN.hIdxTUCam:
            TUCAM_Dev_Close(self.TUCAMOPEN.hIdxTUCam)
        print('Close the camera success')
    
    def set_resolution(self, resolution=0):
        """设置相机分辨率"""
        TUCAM_Capa_SetValue(self.TUCAMOPEN.hIdxTUCam, TUCAM_IDCAPA.TUIDC_RESOLUTION.value, resolution)

    def UnInitApi(self):
        TUCAM_Api_Uninit()
        
    def StartCapture(self):
        self.m_frame.pBuffer = 0
        self.m_frame.uiRsdSize = 1
        # 分配新的缓冲区
        TUCAM_Buf_Alloc(self.TUCAMOPEN.hIdxTUCam, pointer(self.m_frame))
        TUCAM_Cap_Start(self.TUCAMOPEN.hIdxTUCam, TUCAM_CAPTURE_MODES.TUCCM_SEQUENCE.value)
        
        # 重置帧率计算
        self.frame_count = 0
        self.start_time = time.time()
        self.fps = 0.0
        
    def StopCapture(self):
        TUCAM_Buf_AbortWait(self.TUCAMOPEN.hIdxTUCam)
        TUCAM_Cap_Stop(self.TUCAMOPEN.hIdxTUCam)
        TUCAM_Buf_Release(self.TUCAMOPEN.hIdxTUCam)

    def get_frame(self):
        """获取当前帧并转换为QImage
        Returns:
            QImage: 成功时返回图像对象，失败时返回None
        """
        try:
            # 获取新帧
            result = TUCAM_Buf_WaitForFrame(self.TUCAMOPEN.hIdxTUCam, pointer(self.m_frame), 1000)   
            # 更新帧率计算
            self.frame_count += 1
            elapsed_time = time.time() - self.start_time
            if elapsed_time >= 0.3:
                self.fps = self.frame_count / elapsed_time
                self.frame_count = 0
                self.start_time = time.time()
            
            # 直接从帧缓冲区创建QImage
            buf = create_string_buffer(self.m_frame.uiImgSize)
            memmove(buf, c_void_p(self.m_frame.pBuffer + self.m_frame.usHeader), self.m_frame.uiImgSize)
            
            # 使用QTransform进行垂直翻转
            transform = QTransform()
            transform.scale(1, -1)  # 不水平翻转，只垂直翻转
            return QImage(buf,
                self.m_frame.usWidth,
                self.m_frame.usHeight,
                self.m_frame.usWidth * self.m_frame.ucChannels,
                QImage.Format_BGR888).transformed(transform)
                
        except Exception as e:
            print(f"获取帧时出错: {str(e)}")
            return None

    def display_frame(self, image_label,image=None):
        """更新帧图像
        Args:
            image_label: QLabel对象，用于显示图像
        Returns:
            QImage: 当前帧的图像对象或None
        """
        if image is not None:
            # 显示图像，使用更高质量的缩放算法
            scaled_pixmap = QPixmap.fromImage(image).scaled(
                image_label.size(), 
                Qt.KeepAspectRatio,
                Qt.SmoothTransformation
            )
            image_label.setPixmap(scaled_pixmap)     
    
    def get_fps(self):
        """获取当前帧率
        Returns:
            float: 当前帧率
        """
        return self.fps
        
    def SaveImage(self, path):
        """保存当前帧图像
        Args:
            path: 保存路径
        Returns:
            bool: 是否保存成功
        """
        self.m_fs.pFrame = pointer(self.m_frame)
        self.m_fs.pstrSavePath = path.encode('utf-8')
        TUCAM_File_SaveImage(self.TUCAMOPEN.hIdxTUCam, self.m_fs)
        
        

    def set_bit_depth(self, bit_depth=16):
        """设置位深度
        Args:
            bit_depth: 8 或 16
        """
        TUCAM_Capa_SetValue(self.TUCAMOPEN.hIdxTUCam, TUCAM_IDCAPA.TUIDC_BITOFDEPTH.value, bit_depth)
        
    def set_auto_exposure(self, auto_state):
        """设置自动曝光状态
        Args:
            auto_state: 1为开启自动曝光，0为关闭
        """
        TUCAM_Capa_SetValue(self.TUCAMOPEN.hIdxTUCam, TUCAM_IDCAPA.TUIDC_ATEXPOSURE.value, auto_state)
        
    def set_exposure_mode(self, exposure_mode=0):
        """设置自动曝光模式"""
        TUCAM_Capa_SetValue(self.TUCAMOPEN.hIdxTUCam, TUCAM_IDCAPA.TUIDC_ATEXPOSURE_MODE.value, exposure_mode)

    def set_atwbalance(self, auto_state):
        """设置自动白平衡状态"""
        TUCAM_Capa_SetValue(self.TUCAMOPEN.hIdxTUCam, TUCAM_IDCAPA.TUIDC_ATWBALANCE.value, auto_state)

    def set_isp(self, isp=1):
        """设置ISP(图像信号处理)"""                                                                                                                                                                                                                                              
        TUCAM_Capa_SetValue(self.TUCAMOPEN.hIdxTUCam, TUCAM_IDCAPA.TUIDC_ENABLEISP.value, isp)

    def set_enhance(self, enhance=1):
        """设置增强"""
        TUCAM_Capa_SetValue(self.TUCAMOPEN.hIdxTUCam, TUCAM_IDCAPA.TUIDC_ENHANCE.value, enhance)

    def set_horizontal_flip(self, enabled=1):
        """设置左右翻转"""
        TUCAM_Capa_SetValue(self.TUCAMOPEN.hIdxTUCam, TUCAM_IDCAPA.TUIDC_HORIZONTAL.value, enabled)
        
    def set_vertical_flip(self, enabled=1):
        """设置上下翻转"""
        TUCAM_Capa_SetValue(self.TUCAMOPEN.hIdxTUCam, TUCAM_IDCAPA.TUIDC_VERTICAL.value, enabled)

    def set_exposure_time(self, value=2):
        """设置曝光时间
        Args:
            value: 曝光时间值（毫秒）
        """
        # 添加安全范围检查，根据实际相机规格适当调整范围
        if  value > 1500:
            print(f"警告：曝光时间 {value}ms 可能超出相机支持范围")
            
        TUCAM_Prop_SetValue(self.TUCAMOPEN.hIdxTUCam, TUCAM_IDPROP.TUIDP_EXPOSURETM.value, value, 0)

    def set_global_gain(self, gain=0):
        """设置全局增益"""
        TUCAM_Prop_SetValue(self.TUCAMOPEN.hIdxTUCam, TUCAM_IDPROP.TUIDP_GLOBALGAIN.value, gain, 0)

    def set_gamma(self, gamma=210):
        """设置伽马值"""
        TUCAM_Prop_SetValue(self.TUCAMOPEN.hIdxTUCam, TUCAM_IDPROP.TUIDP_GAMMA.value, gamma, 0)

    def set_saturation(self, saturation=64):
        """设置饱和度"""
        TUCAM_Prop_SetValue(self.TUCAMOPEN.hIdxTUCam, TUCAM_IDPROP.TUIDP_SATURATION.value, saturation, 0)

    def set_contrast(self, contrast=33):
        """设置对比度"""
        TUCAM_Prop_SetValue(self.TUCAMOPEN.hIdxTUCam, TUCAM_IDPROP.TUIDP_CONTRAST.value, contrast, 0)

    def set_brightness(self, brightness=64):
        """设置亮度"""
        TUCAM_Prop_SetValue(self.TUCAMOPEN.hIdxTUCam, TUCAM_IDPROP.TUIDP_LIGHT.value, brightness, 0)

    def set_sharpness(self, sharpness=25):
        """设置锐度"""
        TUCAM_Prop_SetValue(self.TUCAMOPEN.hIdxTUCam, TUCAM_IDPROP.TUIDP_SHARPNESS.value, sharpness, 0)

    def set_color_temperature(self, value=3500):
        """设置色温"""
        TUCAM_Prop_SetValue(self.TUCAMOPEN.hIdxTUCam, TUCAM_IDPROP.TUIDP_CLRTEMPERATURE.value, value, 0)

    def get_exposure_time(self):
        """获取当前曝光时间
        Returns:
            float: 当前曝光时间值（毫秒）
        """
        
        ctVal = c_double(0)
        result = TUCAM_Prop_GetValue(self.TUCAMOPEN.hIdxTUCam, TUCAM_IDPROP.TUIDP_EXPOSURETM.value, byref(ctVal), 0)
        
        return ctVal.value  # 返回当前曝光时间值（毫秒）
            
        

    def set_hue(self, hue=180):
        """设置色调"""
        TUCAM_Prop_SetValue(self.TUCAMOPEN.hIdxTUCam, TUCAM_IDPROP.TUIDP_HUE.value, hue, 0)

    def SetAreaWhiteBalance(self, value):
        arearoi = TUCAM_CALC_ROI_ATTR()
        arearoi.bEnable  = value
        arearoi.idCalc   = TUCAM_IDCROI.TUIDCR_WBALANCE.value
        arearoi.nHOffset = 0
        arearoi.nVOffset = 0
        arearoi.nWidth   = 320
        arearoi.nHeight  = 240



class CameraUI(QMainWindow):
    def __init__(self, camera):
        super().__init__()
        self.camera = camera
        self.setWindowTitle("Camera Viewer")
        self.setGeometry(100, 100, 1024, 768)  # 增大窗口尺寸
        
        # Create central widget and layout
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)  # 减少边距以最大化图像显示区域
        
        # Create image label with increased size
        self.image_label = QLabel()
        self.image_label.setAlignment(Qt.AlignCenter)
        self.image_label.setMinimumSize(800, 600)  # 设置最小尺寸确保图像足够大
        self.image_label.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)  # 允许图像区域扩展填充可用空间
        
        # 创建状态栏并添加FPS标签
        #self.statusBar().showMessage("Ready")
        self.fps_label = QLabel("FPS: 0.0")
        self.statusBar().addPermanentWidget(self.fps_label)
        
        # Create button layout at the bottom
        button_layout = QHBoxLayout()
        button_layout.setContentsMargins(10, 5, 10, 5)  # 设置按钮区域边距
        
        # Create capture button
        self.capture_button = QPushButton("Capture Photo")
        self.capture_button.setFixedHeight(40)  # 增大按钮高度，更容易点击
        self.capture_button.setMinimumWidth(150)  # 设置最小宽度
        self.capture_button.clicked.connect(self.capture_photo)
        button_layout.addWidget(self.capture_button)
        
        # 添加分辨率选择下拉菜单
        resolution_label = QLabel("分辨率:")
        resolution_label.setFixedHeight(40)
        button_layout.addWidget(resolution_label)
        
        self.resolution_combo = QComboBox()
        self.resolution_combo.setFixedHeight(40)
        self.resolution_combo.setMinimumWidth(150)
        self.resolution_combo.addItem("2448x2048", 0)
        self.resolution_combo.addItem("1224x1024", 1)
        self.resolution_combo.currentIndexChanged.connect(self.on_resolution_changed)
        button_layout.addWidget(self.resolution_combo)
        
        # 添加曝光模式选择下拉菜单
        exposure_mode_label = QLabel("曝光模式:")
        exposure_mode_label.setFixedHeight(40)
        button_layout.addWidget(exposure_mode_label)
        
        self.exposure_mode_combo = QComboBox()
        self.exposure_mode_combo.setFixedHeight(40)
        self.exposure_mode_combo.setMinimumWidth(100)
        self.exposure_mode_combo.addItem("手动曝光", 0)
        self.exposure_mode_combo.addItem("自动曝光", 1)
        button_layout.addWidget(self.exposure_mode_combo)
        
        # 添加曝光时间控制
        exposure_label = QLabel("曝光时间(ms):")
        exposure_label.setFixedHeight(40)
        button_layout.addWidget(exposure_label)
        
        self.exposure_spinbox = QDoubleSpinBox()
        self.exposure_spinbox.setFixedHeight(40)
        self.exposure_spinbox.setMinimumWidth(100)
        self.exposure_spinbox.setRange(0, 1000)
        self.exposure_spinbox.setSingleStep(0.5)
        self.exposure_spinbox.setValue(2)  # 默认值设为2ms
        self.exposure_spinbox.valueChanged.connect(self.on_exposure_changed)
        button_layout.addWidget(self.exposure_spinbox)
        # 默认为手动曝光模式
        self.camera.set_auto_exposure(0)  # 0表示手动曝光
        # 连接曝光模式下拉菜单的信号
        self.exposure_mode_combo.currentIndexChanged.connect(self.on_exposure_mode_changed)
        
        # 添加增益滑块控制
        gain_label = QLabel("增益:")
        gain_label.setFixedHeight(40)
        button_layout.addWidget(gain_label)
        
        self.gain_slider = QSlider(Qt.Horizontal)
        self.gain_slider.setFixedHeight(40)
        self.gain_slider.setMinimumWidth(150)
        self.gain_slider.setRange(0, 100)  # 设置增益范围0-100
        self.gain_slider.setValue(0)  # 默认值设为0
        self.gain_slider.valueChanged.connect(self.on_gain_changed)
        button_layout.addWidget(self.gain_slider)
        
        self.gain_value_label = QLabel("0")  # 显示当前增益值
        self.gain_value_label.setFixedHeight(40)
        self.gain_value_label.setMinimumWidth(30)
        self.gain_value_label.setAlignment(Qt.AlignCenter)
        button_layout.addWidget(self.gain_value_label)
        
        # 添加间隔
        button_layout.addStretch()
        
        # 将布局添加到主布局
        main_layout.addWidget(self.image_label, 1)  # 图像区域占据大部分空间
        main_layout.addLayout(button_layout)
        
        # 使用常规QTimer直接更新帧
        self.frame_timer = QTimer()
        self.frame_timer.timeout.connect(self.update_frame)
        self.frame_timer.start(0)  # 设置为0让其尽可能快地更新
        
        # FPS更新定时器
        self.fps_update_timer = QTimer()
        self.fps_update_timer.timeout.connect(self.update_fps_display)
        self.fps_update_timer.start(300)  # 每0.3秒更新一次FPS显示
    
    def update_frame(self):
        """调用相机类的display_frame方法更新图像"""
        self.camera.display_frame(self.image_label,self.camera.get_frame())
    def update_fps_display(self):
        """更新FPS显示标签"""
        fps = self.camera.get_fps()
        self.fps_label.setText(f"FPS: {fps:.1f}")
        current_message = self.statusBar().currentMessage()
        if not current_message or current_message == "正在获取图像":
            self.statusBar().showMessage("")
    
    def on_resolution_changed(self, index):
        """响应分辨率选择变化"""
        try:
            # 停止所有定时器
            self.frame_timer.stop()
            self.fps_update_timer.stop()
            # 停止当前捕获
            self.camera.StopCapture()
            # 设置新分辨率
            self.camera.set_resolution(self.resolution_combo.currentIndex())
            self.camera.StartCapture()
            # 尝试获取新分辨率的图像
            self.camera.display_frame(self.image_label,self.camera.get_frame())
            # 显示实际分辨率文本而不是数据值
            self.statusBar().showMessage(f"分辨率已切换: {self.resolution_combo.currentText()}", 3000)
                
        except Exception as e:
            print(f"切换分辨率时出错: {e}")
            self.statusBar().showMessage(f"切换分辨率失败: {str(e)}", 3000)
            
        finally:
            # 无论成功与否，都重新启动定时器
            self.frame_timer.start(0)
            QTimer.singleShot(3000, self.restart_fps_timer)
    
    def restart_fps_timer(self):
        """重新启动FPS更新定时器"""
        if not self.fps_update_timer.isActive():
            self.fps_update_timer.start(500)
    
    def capture_photo(self,filename):
        # Create captures directory if it doesn't exist
        if not os.path.exists('captures'):
            os.makedirs('captures')
        # Generate filename with timestamp
        
        if self.camera.SaveImage(filename):
            print(f"Photo saved as {filename}")
            self.statusBar().showMessage(f"图像已保存: {filename}", 1000)
        else:
            print("Failed to save photo")
            self.statusBar().showMessage("保存图像失败", 1000)

    def on_exposure_changed(self, value):
        """响应曝光时间改变"""
        self.camera.set_exposure_time(value)
        self.statusBar().showMessage(f"曝光时间已设置为: {value}ms", 2000)
        

    def on_gain_changed(self, value):
        """响应增益滑块改变"""
        
        self.gain_value_label.setText(str(value))
        self.camera.set_global_gain(value)
        self.statusBar().showMessage(f"增益已设置为: {value}", 2000)
        

    def on_exposure_mode_changed(self, index):
        """响应曝光模式选择变化"""
        
        auto_mode = index == 1  # 1 表示自动曝光
        self.statusBar().showMessage(f"正在设置曝光模式: {'自动' if auto_mode else '手动'}...")
        
        # 设置自动曝光状态
        self.camera.set_auto_exposure(auto_mode)
        
        # 设置曝光时间输入框是否可编辑
        self.exposure_spinbox.setReadOnly(auto_mode)
        self.exposure_spinbox.setButtonSymbols(QDoubleSpinBox.NoButtons if auto_mode else QDoubleSpinBox.UpDownArrows)
        
        # 如果是自动模式，启动定时器来更新显示的曝光值
        if auto_mode:
            if not hasattr(self, 'exposure_update_timer'):
                self.exposure_update_timer = QTimer()
                self.exposure_update_timer.timeout.connect(self.update_exposure_display)
            self.exposure_update_timer.start(500)  # 每500ms更新一次
        else:
            # 手动模式，停止更新定时器
            if hasattr(self, 'exposure_update_timer') and self.exposure_update_timer.isActive():
                self.exposure_update_timer.stop()
        
        self.statusBar().showMessage(f"曝光模式已设置为: {'自动' if auto_mode else '手动'}", 2000)

    def update_exposure_display(self):
        """在自动曝光模式下更新显示的曝光时间值"""
        current_exposure = self.camera.get_exposure_time()
        if current_exposure is not None:
            # 更新spinbox的值，但不触发valueChanged信号
            self.exposure_spinbox.blockSignals(True)
            self.exposure_spinbox.setValue(current_exposure)
            self.exposure_spinbox.blockSignals(False)
        


if __name__ == "__main__":
    app = QApplication(sys.argv)
    
    # Initialize camera
    camera = Tucam()
    if not camera.OpenCamera(0):
        print("Failed to open camera")
        sys.exit(1)
    
    # 初始化相机设置
    camera.set_auto_exposure(0)  # 默认使用手动曝光
    camera.set_exposure_time(2)  # 默认曝光时间2ms
    camera.set_global_gain(0)    # 默认增益为0
    
    # Start camera capture
    camera.StartCapture()
    
    # Create and show UI
    window = CameraUI(camera)
    window.show()
    
    # Run application
    app.exec()
    
    # Cleanup
    camera.StopCapture()
    camera.CloseCamera()
    camera.UnInitApi()
