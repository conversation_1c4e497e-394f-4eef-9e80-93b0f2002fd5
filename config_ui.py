#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
配置管理界面
提供图形化的配置编辑和管理功能
"""

from typing import Dict, Any, Optional
from PySide6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QTabWidget, QWidget,
    QFormLayout, QSpinBox, QDoubleSpinBox, QLineEdit, QCheckBox,
    QComboBox, QPushButton, QGroupBox, QLabel, QMessageBox,
    QFileDialog, QTextEdit, QScrollArea
)
from PySide6.QtCore import Qt, Signal
from PySide6.QtGui import QFont

from config import get_config, save_config, ConfigManager
from logger import get_logger, LoggerMixin


class ConfigWidget(QWidget, LoggerMixin):
    """配置编辑控件基类"""
    
    value_changed = Signal()
    
    def __init__(self, title: str, parent=None):
        super().__init__(parent)
        self.title = title
        self.config = get_config()
        self.widgets: Dict[str, Any] = {}
        self.setup_ui()
    
    def setup_ui(self):
        """设置UI"""
        layout = QVBoxLayout(self)
        
        # 标题
        title_label = QLabel(self.title)
        title_label.setFont(QFont("Arial", 12, QFont.Bold))
        layout.addWidget(title_label)
        
        # 配置表单
        self.form_layout = QFormLayout()
        layout.addLayout(self.form_layout)
        
        # 子类实现具体配置项
        self.create_config_items()
        
        layout.addStretch()
    
    def create_config_items(self):
        """创建配置项（子类实现）"""
        pass
    
    def add_spinbox(self, label: str, key: str, value: int, 
                   min_val: int = 0, max_val: int = 999999):
        """添加整数输入框"""
        spinbox = QSpinBox()
        spinbox.setRange(min_val, max_val)
        spinbox.setValue(value)
        spinbox.valueChanged.connect(self.value_changed.emit)
        
        self.widgets[key] = spinbox
        self.form_layout.addRow(label, spinbox)
        return spinbox
    
    def add_double_spinbox(self, label: str, key: str, value: float,
                          min_val: float = 0.0, max_val: float = 999999.0,
                          decimals: int = 2):
        """添加浮点数输入框"""
        spinbox = QDoubleSpinBox()
        spinbox.setRange(min_val, max_val)
        spinbox.setDecimals(decimals)
        spinbox.setValue(value)
        spinbox.valueChanged.connect(self.value_changed.emit)
        
        self.widgets[key] = spinbox
        self.form_layout.addRow(label, spinbox)
        return spinbox
    
    def add_lineedit(self, label: str, key: str, value: str):
        """添加文本输入框"""
        lineedit = QLineEdit()
        lineedit.setText(value)
        lineedit.textChanged.connect(self.value_changed.emit)
        
        self.widgets[key] = lineedit
        self.form_layout.addRow(label, lineedit)
        return lineedit
    
    def add_checkbox(self, label: str, key: str, value: bool):
        """添加复选框"""
        checkbox = QCheckBox()
        checkbox.setChecked(value)
        checkbox.toggled.connect(self.value_changed.emit)
        
        self.widgets[key] = checkbox
        self.form_layout.addRow(label, checkbox)
        return checkbox
    
    def add_combobox(self, label: str, key: str, items: list, current: str):
        """添加下拉框"""
        combobox = QComboBox()
        combobox.addItems(items)
        if current in items:
            combobox.setCurrentText(current)
        combobox.currentTextChanged.connect(self.value_changed.emit)
        
        self.widgets[key] = combobox
        self.form_layout.addRow(label, combobox)
        return combobox
    
    def get_values(self) -> Dict[str, Any]:
        """获取所有配置值"""
        values = {}
        for key, widget in self.widgets.items():
            if isinstance(widget, QSpinBox):
                values[key] = widget.value()
            elif isinstance(widget, QDoubleSpinBox):
                values[key] = widget.value()
            elif isinstance(widget, QLineEdit):
                values[key] = widget.text()
            elif isinstance(widget, QCheckBox):
                values[key] = widget.isChecked()
            elif isinstance(widget, QComboBox):
                values[key] = widget.currentText()
        return values
    
    def set_values(self, values: Dict[str, Any]):
        """设置配置值"""
        for key, value in values.items():
            if key in self.widgets:
                widget = self.widgets[key]
                if isinstance(widget, QSpinBox):
                    widget.setValue(int(value))
                elif isinstance(widget, QDoubleSpinBox):
                    widget.setValue(float(value))
                elif isinstance(widget, QLineEdit):
                    widget.setText(str(value))
                elif isinstance(widget, QCheckBox):
                    widget.setChecked(bool(value))
                elif isinstance(widget, QComboBox):
                    widget.setCurrentText(str(value))


class MotorConfigWidget(ConfigWidget):
    """电机配置控件"""
    
    def __init__(self, parent=None):
        super().__init__("电机配置", parent)
    
    def create_config_items(self):
        motor_config = self.config.motor
        
        # 物理限制
        self.add_double_spinbox("X轴最大行程 (mm)", "max_x_mm", 
                               motor_config.max_x_mm, 0, 1000, 1)
        self.add_double_spinbox("Y轴最大行程 (mm)", "max_y_mm", 
                               motor_config.max_y_mm, 0, 1000, 1)
        self.add_double_spinbox("Z轴最大行程 (μm)", "max_z_micron", 
                               motor_config.max_z_micron, 0, 50000, 0)
        
        # 脉冲转换
        self.add_spinbox("每毫米脉冲数", "pulses_per_mm", 
                        motor_config.pulses_per_mm, 1000, 100000)
        self.add_double_spinbox("Z轴每微米脉冲数", "pulses_per_micron_z", 
                               motor_config.pulses_per_micron_z, 100, 1000, 2)
        
        # 中心位置
        self.add_double_spinbox("X轴中心位置 (mm)", "center_x_mm", 
                               motor_config.center_x_mm, 0, 100, 1)
        self.add_double_spinbox("Y轴中心位置 (mm)", "center_y_mm", 
                               motor_config.center_y_mm, 0, 100, 1)
        self.add_double_spinbox("Z轴中心位置 (μm)", "center_z_micron", 
                               motor_config.center_z_micron, 0, 20000, 0)
        
        # 运动参数
        self.add_spinbox("默认速度", "default_speed", 
                        motor_config.default_speed, 100, 10000)
        self.add_spinbox("默认加速时间 (ms)", "default_acc_time", 
                        motor_config.default_acc_time, 10, 1000)


class CameraConfigWidget(ConfigWidget):
    """相机配置控件"""
    
    def __init__(self, parent=None):
        super().__init__("相机配置", parent)
    
    def create_config_items(self):
        camera_config = self.config.camera
        
        self.add_spinbox("默认曝光时间 (ms)", "default_exposure_time", 
                        camera_config.default_exposure_time, 1, 1000)
        self.add_spinbox("默认增益", "default_gain", 
                        camera_config.default_gain, 0, 100)
        self.add_checkbox("自动曝光", "auto_exposure", 
                         camera_config.auto_exposure)
        self.add_spinbox("帧缓冲区大小", "frame_buffer_size", 
                        camera_config.frame_buffer_size, 1, 100)
        self.add_double_spinbox("FPS更新间隔 (s)", "fps_update_interval", 
                               camera_config.fps_update_interval, 0.1, 10.0, 1)


class AutoFocusConfigWidget(ConfigWidget):
    """自动对焦配置控件"""
    
    def __init__(self, parent=None):
        super().__init__("自动对焦配置", parent)
    
    def create_config_items(self):
        af_config = self.config.autofocus
        
        # 对焦方法
        methods = ["laplacian", "sobel", "tenengrad", "variance", "fft"]
        self.add_combobox("默认对焦方法", "default_method", 
                         methods, af_config.default_method)
        
        # 粗略扫描
        self.add_double_spinbox("粗略扫描范围 (μm)", "coarse_range_micron", 
                               af_config.coarse_range_micron, 100, 10000, 0)
        self.add_double_spinbox("粗略扫描步长 (μm)", "coarse_step_micron", 
                               af_config.coarse_step_micron, 1, 1000, 0)
        
        # 中等扫描
        self.add_double_spinbox("中等扫描范围 (μm)", "medium_range_micron", 
                               af_config.medium_range_micron, 10, 1000, 0)
        self.add_double_spinbox("中等扫描步长 (μm)", "medium_step_micron", 
                               af_config.medium_step_micron, 1, 100, 1)
        
        # 精细扫描
        self.add_double_spinbox("精细扫描范围 (μm)", "fine_range_micron", 
                               af_config.fine_range_micron, 1, 100, 1)
        self.add_double_spinbox("精细扫描步长 (μm)", "fine_step_micron", 
                               af_config.fine_step_micron, 0.1, 10, 2)
        
        # GPU设置
        self.add_checkbox("使用GPU加速", "use_gpu", af_config.use_gpu)
        
        # 爬山算法参数
        self.add_double_spinbox("爬山算法步长 (μm)", "hill_climbing_step_size", 
                               af_config.hill_climbing_step_size, 1, 200, 1)
        self.add_double_spinbox("爬山算法最小步长 (μm)", "hill_climbing_min_step", 
                               af_config.hill_climbing_min_step, 0.1, 10, 2)
        self.add_spinbox("爬山算法最大步数", "hill_climbing_max_steps", 
                        af_config.hill_climbing_max_steps, 10, 500)


class ScanConfigWidget(ConfigWidget):
    """扫描配置控件"""
    
    def __init__(self, parent=None):
        super().__init__("扫描配置", parent)
    
    def create_config_items(self):
        scan_config = self.config.scan
        
        self.add_double_spinbox("默认网格宽度 (mm)", "default_grid_width", 
                               scan_config.default_grid_width, 0.1, 10, 4)
        self.add_double_spinbox("默认网格高度 (mm)", "default_grid_height", 
                               scan_config.default_grid_height, 0.1, 10, 4)
        self.add_double_spinbox("默认重叠度", "default_overlap", 
                               scan_config.default_overlap, 0.0, 0.5, 2)
        self.add_lineedit("默认保存路径", "default_save_path", 
                         scan_config.default_save_path)
        
        formats = ["jpg", "png", "tiff", "bmp"]
        self.add_combobox("图像格式", "image_format", 
                         formats, scan_config.image_format)
        self.add_checkbox("生成拼接配置文件", "generate_tile_config", 
                         scan_config.generate_tile_config)


class SystemConfigWidget(ConfigWidget):
    """系统配置控件"""
    
    def __init__(self, parent=None):
        super().__init__("系统配置", parent)
    
    def create_config_items(self):
        config = self.config
        
        self.add_checkbox("调试模式", "debug_mode", config.debug_mode)
        
        log_levels = ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]
        self.add_combobox("日志级别", "log_level", log_levels, config.log_level)
        self.add_lineedit("日志文件", "log_file", config.log_file)
        
        self.add_lineedit("默认串口", "default_com_port", config.default_com_port)
        self.add_double_spinbox("串口超时 (s)", "com_timeout", 
                               config.com_timeout, 1.0, 30.0, 1)


class ConfigDialog(QDialog, LoggerMixin):
    """配置对话框"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("系统配置")
        self.setModal(True)
        self.resize(600, 500)
        
        self.config_widgets = {}
        self.setup_ui()
        self.load_config()
    
    def setup_ui(self):
        """设置UI"""
        layout = QVBoxLayout(self)
        
        # 选项卡
        self.tab_widget = QTabWidget()
        layout.addWidget(self.tab_widget)
        
        # 创建配置页面
        self.create_config_tabs()
        
        # 按钮
        button_layout = QHBoxLayout()
        
        self.load_btn = QPushButton("加载配置")
        self.save_btn = QPushButton("保存配置")
        self.export_btn = QPushButton("导出配置")
        self.import_btn = QPushButton("导入配置")
        self.reset_btn = QPushButton("重置默认")
        self.apply_btn = QPushButton("应用")
        self.cancel_btn = QPushButton("取消")
        
        button_layout.addWidget(self.load_btn)
        button_layout.addWidget(self.save_btn)
        button_layout.addWidget(self.export_btn)
        button_layout.addWidget(self.import_btn)
        button_layout.addWidget(self.reset_btn)
        button_layout.addStretch()
        button_layout.addWidget(self.apply_btn)
        button_layout.addWidget(self.cancel_btn)
        
        layout.addLayout(button_layout)
        
        # 连接信号
        self.load_btn.clicked.connect(self.load_config)
        self.save_btn.clicked.connect(self.save_config)
        self.export_btn.clicked.connect(self.export_config)
        self.import_btn.clicked.connect(self.import_config)
        self.reset_btn.clicked.connect(self.reset_config)
        self.apply_btn.clicked.connect(self.apply_config)
        self.cancel_btn.clicked.connect(self.reject)
    
    def create_config_tabs(self):
        """创建配置选项卡"""
        # 电机配置
        motor_widget = MotorConfigWidget()
        scroll_area = QScrollArea()
        scroll_area.setWidget(motor_widget)
        scroll_area.setWidgetResizable(True)
        self.tab_widget.addTab(scroll_area, "电机")
        self.config_widgets["motor"] = motor_widget
        
        # 相机配置
        camera_widget = CameraConfigWidget()
        scroll_area = QScrollArea()
        scroll_area.setWidget(camera_widget)
        scroll_area.setWidgetResizable(True)
        self.tab_widget.addTab(scroll_area, "相机")
        self.config_widgets["camera"] = camera_widget
        
        # 自动对焦配置
        autofocus_widget = AutoFocusConfigWidget()
        scroll_area = QScrollArea()
        scroll_area.setWidget(autofocus_widget)
        scroll_area.setWidgetResizable(True)
        self.tab_widget.addTab(scroll_area, "自动对焦")
        self.config_widgets["autofocus"] = autofocus_widget
        
        # 扫描配置
        scan_widget = ScanConfigWidget()
        scroll_area = QScrollArea()
        scroll_area.setWidget(scan_widget)
        scroll_area.setWidgetResizable(True)
        self.tab_widget.addTab(scroll_area, "扫描")
        self.config_widgets["scan"] = scan_widget
        
        # 系统配置
        system_widget = SystemConfigWidget()
        scroll_area = QScrollArea()
        scroll_area.setWidget(system_widget)
        scroll_area.setWidgetResizable(True)
        self.tab_widget.addTab(scroll_area, "系统")
        self.config_widgets["system"] = system_widget
    
    def load_config(self):
        """加载配置"""
        try:
            config = get_config()
            
            # 更新各个配置页面
            self.config_widgets["motor"].set_values(config.motor.__dict__)
            self.config_widgets["camera"].set_values(config.camera.__dict__)
            self.config_widgets["autofocus"].set_values(config.autofocus.__dict__)
            self.config_widgets["scan"].set_values(config.scan.__dict__)
            
            system_values = {
                "debug_mode": config.debug_mode,
                "log_level": config.log_level,
                "log_file": config.log_file,
                "default_com_port": config.default_com_port,
                "com_timeout": config.com_timeout
            }
            self.config_widgets["system"].set_values(system_values)
            
            self.logger.info("配置加载完成")
            
        except Exception as e:
            self.logger.error("加载配置失败", exception=e)
            QMessageBox.warning(self, "错误", f"加载配置失败: {str(e)}")
    
    def save_config(self):
        """保存配置"""
        try:
            if save_config():
                self.logger.info("配置保存成功")
                QMessageBox.information(self, "成功", "配置已保存")
            else:
                QMessageBox.warning(self, "错误", "保存配置失败")
        except Exception as e:
            self.logger.error("保存配置失败", exception=e)
            QMessageBox.warning(self, "错误", f"保存配置失败: {str(e)}")
    
    def export_config(self):
        """导出配置"""
        try:
            filename, _ = QFileDialog.getSaveFileName(
                self, "导出配置", "config_backup.json", "JSON文件 (*.json)")
            
            if filename:
                config_manager = ConfigManager()
                import shutil
                shutil.copy(config_manager.config_file, filename)
                QMessageBox.information(self, "成功", f"配置已导出到: {filename}")
                
        except Exception as e:
            self.logger.error("导出配置失败", exception=e)
            QMessageBox.warning(self, "错误", f"导出配置失败: {str(e)}")
    
    def import_config(self):
        """导入配置"""
        try:
            filename, _ = QFileDialog.getOpenFileName(
                self, "导入配置", "", "JSON文件 (*.json)")
            
            if filename:
                config_manager = ConfigManager()
                import shutil
                shutil.copy(filename, config_manager.config_file)
                config_manager.load_config()
                self.load_config()
                QMessageBox.information(self, "成功", "配置已导入")
                
        except Exception as e:
            self.logger.error("导入配置失败", exception=e)
            QMessageBox.warning(self, "错误", f"导入配置失败: {str(e)}")
    
    def reset_config(self):
        """重置为默认配置"""
        reply = QMessageBox.question(
            self, "确认", "确定要重置为默认配置吗？这将丢失当前所有设置。",
            QMessageBox.Yes | QMessageBox.No, QMessageBox.No)
        
        if reply == QMessageBox.Yes:
            try:
                config_manager = ConfigManager()
                config_manager._config = config_manager._create_default_config()
                config_manager.save_config()
                self.load_config()
                QMessageBox.information(self, "成功", "已重置为默认配置")
            except Exception as e:
                self.logger.error("重置配置失败", exception=e)
                QMessageBox.warning(self, "错误", f"重置配置失败: {str(e)}")
    
    def apply_config(self):
        """应用配置"""
        try:
            # 这里可以添加应用配置的逻辑
            # 例如重新初始化相关组件
            self.accept()
        except Exception as e:
            self.logger.error("应用配置失败", exception=e)
            QMessageBox.warning(self, "错误", f"应用配置失败: {str(e)}")


if __name__ == "__main__":
    from PySide6.QtWidgets import QApplication
    import sys
    
    app = QApplication(sys.argv)
    
    dialog = ConfigDialog()
    dialog.show()
    
    sys.exit(app.exec())
