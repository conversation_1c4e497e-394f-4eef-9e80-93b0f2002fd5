#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
统一日志系统
提供结构化的日志记录功能
"""

import logging
import sys
import os
from datetime import datetime
from typing import Optional
from pathlib import Path
import traceback


class ColoredFormatter(logging.Formatter):
    """彩色日志格式化器"""
    
    # ANSI颜色代码
    COLORS = {
        'DEBUG': '\033[36m',    # 青色
        'INFO': '\033[32m',     # 绿色
        'WARNING': '\033[33m',  # 黄色
        'ERROR': '\033[31m',    # 红色
        'CRITICAL': '\033[35m', # 紫色
        'RESET': '\033[0m'      # 重置
    }
    
    def format(self, record):
        # 添加颜色
        if record.levelname in self.COLORS:
            record.levelname = f"{self.COLORS[record.levelname]}{record.levelname}{self.COLORS['RESET']}"
        
        return super().format(record)


class MicroscopeLogger:
    """显微镜系统日志管理器"""
    
    def __init__(self, name: str = "microscope", log_file: Optional[str] = None, 
                 log_level: str = "INFO", enable_console: bool = True):
        """
        初始化日志管理器
        
        Args:
            name: 日志器名称
            log_file: 日志文件路径
            log_level: 日志级别
            enable_console: 是否启用控制台输出
        """
        self.logger = logging.getLogger(name)
        self.logger.setLevel(getattr(logging, log_level.upper()))
        
        # 清除现有处理器
        self.logger.handlers.clear()
        
        # 创建格式化器
        file_formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(filename)s:%(lineno)d - %(message)s'
        )
        
        console_formatter = ColoredFormatter(
            '%(asctime)s - %(levelname)s - %(filename)s:%(lineno)d - %(message)s',
            datefmt='%H:%M:%S'
        )
        
        # 文件处理器
        if log_file:
            # 确保日志目录存在
            log_path = Path(log_file)
            log_path.parent.mkdir(parents=True, exist_ok=True)
            
            file_handler = logging.FileHandler(log_file, encoding='utf-8')
            file_handler.setLevel(logging.DEBUG)
            file_handler.setFormatter(file_formatter)
            self.logger.addHandler(file_handler)
        
        # 控制台处理器
        if enable_console:
            console_handler = logging.StreamHandler(sys.stdout)
            console_handler.setLevel(getattr(logging, log_level.upper()))
            console_handler.setFormatter(console_formatter)
            self.logger.addHandler(console_handler)
    
    def debug(self, message: str, **kwargs):
        """调试日志"""
        self.logger.debug(message, **kwargs)
    
    def info(self, message: str, **kwargs):
        """信息日志"""
        self.logger.info(message, **kwargs)
    
    def warning(self, message: str, **kwargs):
        """警告日志"""
        self.logger.warning(message, **kwargs)
    
    def error(self, message: str, exception: Optional[Exception] = None, **kwargs):
        """错误日志"""
        if exception:
            self.logger.error(f"{message}: {str(exception)}", **kwargs)
            self.logger.debug(traceback.format_exc())
        else:
            self.logger.error(message, **kwargs)
    
    def critical(self, message: str, exception: Optional[Exception] = None, **kwargs):
        """严重错误日志"""
        if exception:
            self.logger.critical(f"{message}: {str(exception)}", **kwargs)
            self.logger.debug(traceback.format_exc())
        else:
            self.logger.critical(message, **kwargs)
    
    def log_function_call(self, func_name: str, args: tuple = (), kwargs: dict = None):
        """记录函数调用"""
        kwargs = kwargs or {}
        self.debug(f"调用函数 {func_name}, args={args}, kwargs={kwargs}")
    
    def log_performance(self, operation: str, duration: float):
        """记录性能信息"""
        self.info(f"性能统计 - {operation}: {duration:.3f}秒")
    
    def log_motor_operation(self, axis: str, operation: str, position: Optional[int] = None):
        """记录电机操作"""
        if position is not None:
            self.info(f"电机操作 - {axis}轴 {operation}, 位置: {position}")
        else:
            self.info(f"电机操作 - {axis}轴 {operation}")
    
    def log_camera_operation(self, operation: str, details: str = ""):
        """记录相机操作"""
        self.info(f"相机操作 - {operation} {details}")
    
    def log_autofocus_operation(self, operation: str, score: Optional[float] = None, 
                               position: Optional[float] = None):
        """记录自动对焦操作"""
        msg = f"自动对焦 - {operation}"
        if score is not None:
            msg += f", 评分: {score:.3f}"
        if position is not None:
            msg += f", 位置: {position:.1f}μm"
        self.info(msg)
    
    def log_scan_operation(self, operation: str, progress: Optional[int] = None, 
                          total: Optional[int] = None):
        """记录扫描操作"""
        msg = f"扫描操作 - {operation}"
        if progress is not None and total is not None:
            msg += f" ({progress}/{total})"
        self.info(msg)


class LoggerMixin:
    """日志混入类，为其他类提供日志功能"""
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self._logger = None
    
    @property
    def logger(self) -> MicroscopeLogger:
        """获取日志器"""
        if self._logger is None:
            class_name = self.__class__.__name__
            self._logger = get_logger(class_name.lower())
        return self._logger


# 全局日志管理器
_loggers = {}


def get_logger(name: str = "microscope", log_file: Optional[str] = None, 
               log_level: str = "INFO", enable_console: bool = True) -> MicroscopeLogger:
    """
    获取日志器实例
    
    Args:
        name: 日志器名称
        log_file: 日志文件路径
        log_level: 日志级别
        enable_console: 是否启用控制台输出
    
    Returns:
        MicroscopeLogger: 日志器实例
    """
    if name not in _loggers:
        _loggers[name] = MicroscopeLogger(name, log_file, log_level, enable_console)
    return _loggers[name]


def setup_logging(log_file: str = "microscope.log", log_level: str = "INFO", 
                  enable_console: bool = True):
    """
    设置全局日志配置
    
    Args:
        log_file: 日志文件路径
        log_level: 日志级别
        enable_console: 是否启用控制台输出
    """
    # 创建主日志器
    main_logger = get_logger("microscope", log_file, log_level, enable_console)
    
    # 创建各模块日志器
    get_logger("motor", log_file, log_level, enable_console)
    get_logger("camera", log_file, log_level, enable_console)
    get_logger("autofocus", log_file, log_level, enable_console)
    get_logger("scan", log_file, log_level, enable_console)
    get_logger("ui", log_file, log_level, enable_console)
    
    main_logger.info("日志系统初始化完成")
    return main_logger


def log_exception(logger_name: str = "microscope"):
    """装饰器：自动记录异常"""
    def decorator(func):
        def wrapper(*args, **kwargs):
            logger = get_logger(logger_name)
            try:
                logger.log_function_call(func.__name__, args, kwargs)
                return func(*args, **kwargs)
            except Exception as e:
                logger.error(f"函数 {func.__name__} 执行失败", exception=e)
                raise
        return wrapper
    return decorator


def log_performance(logger_name: str = "microscope"):
    """装饰器：自动记录性能"""
    def decorator(func):
        def wrapper(*args, **kwargs):
            logger = get_logger(logger_name)
            start_time = datetime.now()
            try:
                result = func(*args, **kwargs)
                duration = (datetime.now() - start_time).total_seconds()
                logger.log_performance(func.__name__, duration)
                return result
            except Exception as e:
                duration = (datetime.now() - start_time).total_seconds()
                logger.error(f"函数 {func.__name__} 执行失败 (耗时: {duration:.3f}秒)", exception=e)
                raise
        return wrapper
    return decorator


if __name__ == "__main__":
    # 测试日志系统
    setup_logging("test.log", "DEBUG")
    
    logger = get_logger("test")
    logger.debug("这是调试信息")
    logger.info("这是信息")
    logger.warning("这是警告")
    logger.error("这是错误")
    
    # 测试装饰器
    @log_exception("test")
    @log_performance("test")
    def test_function(x, y):
        return x + y
    
    result = test_function(1, 2)
    logger.info(f"测试结果: {result}")
