#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
优化的显微镜控制系统主应用程序
整合了所有优化的模块和组件
"""

import sys
import os
from typing import Optional

from PySide6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QTabWidget, QMenuBar, QStatusBar, QToolBar, QAction, QSplitter,
    QDockWidget, QTextEdit, QLabel
)
from PySide6.QtCore import Qt, QTimer, Signal
from PySide6.QtGui import QIcon, QKeySequence

# 导入优化的模块
from config import get_config, save_config
from logger import setup_logging, get_logger, LoggerMixin
from camera_controller import CameraController, OptimizedCameraThread
from motor_controller import OptimizedMotorController
from system_monitor import SystemMonitor, ComponentStatus
from ui_components import (
    StatusIndicator, NotificationManager, InfoPanel, 
    ThemeManager, UIUtils, ProgressDialog
)
from config_ui import ConfigDialog
from exceptions import MicroscopeException


class MicroscopeMainWindow(QMainWindow, LoggerMixin):
    """显微镜控制系统主窗口"""
    
    # 信号
    system_ready = Signal()
    system_error = Signal(str)
    
    def __init__(self):
        super().__init__()
        
        # 配置和日志
        self.config = get_config()
        setup_logging(self.config.log_file, self.config.log_level)
        
        # 组件
        self.camera_controller: Optional[CameraController] = None
        self.motor_controller: Optional[OptimizedMotorController] = None
        self.camera_thread: Optional[OptimizedCameraThread] = None
        self.system_monitor: Optional[SystemMonitor] = None
        
        # UI组件
        self.notification_manager: Optional[NotificationManager] = None
        self.status_indicators = {}
        self.info_panels = {}
        
        # 初始化UI
        self.setup_ui()
        self.setup_menu_bar()
        self.setup_tool_bar()
        self.setup_status_bar()
        self.setup_dock_widgets()
        
        # 初始化系统
        self.initialize_system()
        
        self.logger.info("显微镜控制系统启动")
    
    def setup_ui(self):
        """设置主界面"""
        self.setWindowTitle(self.config.ui.window_title)
        self.resize(*self.config.ui.default_window_size)
        
        # 应用主题
        ThemeManager.apply_light_theme(QApplication.instance())
        self.setStyleSheet(ThemeManager.get_custom_stylesheet())
        
        # 中央控件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 主布局
        main_layout = QVBoxLayout(central_widget)
        
        # 创建选项卡
        self.tab_widget = QTabWidget()
        main_layout.addWidget(self.tab_widget)
        
        # 相机控制选项卡
        self.camera_tab = self.create_camera_tab()
        self.tab_widget.addTab(self.camera_tab, "相机控制")
        
        # 电机控制选项卡
        self.motor_tab = self.create_motor_tab()
        self.tab_widget.addTab(self.motor_tab, "电机控制")
        
        # 扫描控制选项卡
        self.scan_tab = self.create_scan_tab()
        self.tab_widget.addTab(self.scan_tab, "扫描控制")
        
        # 自动对焦选项卡
        self.autofocus_tab = self.create_autofocus_tab()
        self.tab_widget.addTab(self.autofocus_tab, "自动对焦")
        
        # 通知管理器
        self.notification_manager = NotificationManager(self)
        
        # 居中显示
        UIUtils.center_window(self)
    
    def create_camera_tab(self) -> QWidget:
        """创建相机控制选项卡"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 相机状态面板
        camera_info = InfoPanel("相机状态")
        camera_info.add_info_item("status", "连接状态", "未连接")
        camera_info.add_info_item("fps", "帧率", "0.0 FPS")
        camera_info.add_info_item("resolution", "分辨率", "未知")
        camera_info.add_info_item("exposure", "曝光时间", "未设置")
        camera_info.add_info_item("gain", "增益", "未设置")
        
        self.info_panels["camera"] = camera_info
        layout.addWidget(camera_info)
        
        # 这里可以添加更多相机控制组件
        layout.addStretch()
        
        return widget
    
    def create_motor_tab(self) -> QWidget:
        """创建电机控制选项卡"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 电机状态面板
        motor_info = InfoPanel("电机状态")
        motor_info.add_info_item("status", "连接状态", "未连接")
        motor_info.add_info_item("x_pos", "X轴位置", "0.000 mm")
        motor_info.add_info_item("y_pos", "Y轴位置", "0.000 mm")
        motor_info.add_info_item("z_pos", "Z轴位置", "0.000 μm")
        
        self.info_panels["motor"] = motor_info
        layout.addWidget(motor_info)
        
        layout.addStretch()
        
        return widget
    
    def create_scan_tab(self) -> QWidget:
        """创建扫描控制选项卡"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 扫描状态面板
        scan_info = InfoPanel("扫描状态")
        scan_info.add_info_item("status", "扫描状态", "就绪")
        scan_info.add_info_item("progress", "进度", "0%")
        scan_info.add_info_item("current_pos", "当前位置", "0/0")
        scan_info.add_info_item("estimated_time", "预计时间", "未知")
        
        self.info_panels["scan"] = scan_info
        layout.addWidget(scan_info)
        
        layout.addStretch()
        
        return widget
    
    def create_autofocus_tab(self) -> QWidget:
        """创建自动对焦选项卡"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 自动对焦状态面板
        af_info = InfoPanel("自动对焦状态")
        af_info.add_info_item("status", "对焦状态", "就绪")
        af_info.add_info_item("method", "对焦方法", self.config.autofocus.default_method)
        af_info.add_info_item("score", "对焦评分", "0.000")
        af_info.add_info_item("best_pos", "最佳位置", "未知")
        
        self.info_panels["autofocus"] = af_info
        layout.addWidget(af_info)
        
        layout.addStretch()
        
        return widget
    
    def setup_menu_bar(self):
        """设置菜单栏"""
        menubar = self.menuBar()
        
        # 文件菜单
        file_menu = menubar.addMenu("文件")
        
        # 配置动作
        config_action = QAction("配置", self)
        config_action.setShortcut(QKeySequence.Preferences)
        config_action.triggered.connect(self.show_config_dialog)
        file_menu.addAction(config_action)
        
        file_menu.addSeparator()
        
        # 退出动作
        exit_action = QAction("退出", self)
        exit_action.setShortcut(QKeySequence.Quit)
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)
        
        # 工具菜单
        tools_menu = menubar.addMenu("工具")
        
        # 系统监控动作
        monitor_action = QAction("系统监控", self)
        monitor_action.triggered.connect(self.toggle_system_monitor)
        tools_menu.addAction(monitor_action)
        
        # 帮助菜单
        help_menu = menubar.addMenu("帮助")
        
        # 关于动作
        about_action = QAction("关于", self)
        about_action.triggered.connect(self.show_about_dialog)
        help_menu.addAction(about_action)
    
    def setup_tool_bar(self):
        """设置工具栏"""
        toolbar = self.addToolBar("主工具栏")
        
        # 连接相机动作
        connect_camera_action = QAction("连接相机", self)
        connect_camera_action.triggered.connect(self.connect_camera)
        toolbar.addAction(connect_camera_action)
        
        # 连接电机动作
        connect_motor_action = QAction("连接电机", self)
        connect_motor_action.triggered.connect(self.connect_motor)
        toolbar.addAction(connect_motor_action)
        
        toolbar.addSeparator()
        
        # 开始扫描动作
        start_scan_action = QAction("开始扫描", self)
        start_scan_action.triggered.connect(self.start_scan)
        toolbar.addAction(start_scan_action)
        
        # 自动对焦动作
        autofocus_action = QAction("自动对焦", self)
        autofocus_action.triggered.connect(self.start_autofocus)
        toolbar.addAction(autofocus_action)
    
    def setup_status_bar(self):
        """设置状态栏"""
        status_bar = self.statusBar()
        
        # 系统状态指示器
        status_layout = QHBoxLayout()
        
        # 相机状态
        camera_label = QLabel("相机:")
        camera_indicator = StatusIndicator()
        camera_indicator.set_status("unknown")
        self.status_indicators["camera"] = camera_indicator
        
        # 电机状态
        motor_label = QLabel("电机:")
        motor_indicator = StatusIndicator()
        motor_indicator.set_status("unknown")
        self.status_indicators["motor"] = motor_indicator
        
        # 系统状态
        system_label = QLabel("系统:")
        system_indicator = StatusIndicator()
        system_indicator.set_status("unknown")
        self.status_indicators["system"] = system_indicator
        
        # 添加到状态栏
        status_widget = QWidget()
        status_layout = QHBoxLayout(status_widget)
        status_layout.addWidget(camera_label)
        status_layout.addWidget(camera_indicator)
        status_layout.addWidget(motor_label)
        status_layout.addWidget(motor_indicator)
        status_layout.addWidget(system_label)
        status_layout.addWidget(system_indicator)
        status_layout.addStretch()
        
        status_bar.addPermanentWidget(status_widget)
    
    def setup_dock_widgets(self):
        """设置停靠窗口"""
        # 日志窗口
        log_dock = QDockWidget("系统日志", self)
        log_widget = QTextEdit()
        log_widget.setReadOnly(True)
        log_widget.setMaximumHeight(200)
        log_dock.setWidget(log_widget)
        
        self.addDockWidget(Qt.BottomDockWidgetArea, log_dock)
    
    def initialize_system(self):
        """初始化系统"""
        try:
            # 初始化系统监控
            self.system_monitor = SystemMonitor()
            self.system_monitor.start_monitoring()
            
            # 连接信号
            self.system_monitor.component_status_changed.connect(self.on_component_status_changed)
            self.system_monitor.alert_triggered.connect(self.on_system_alert)
            
            # 注册健康检查
            self.register_health_checks()
            
            # 更新系统状态
            self.status_indicators["system"].set_status("healthy")
            
            self.logger.info("系统初始化完成")
            self.system_ready.emit()
            
        except Exception as e:
            self.logger.error("系统初始化失败", exception=e)
            self.system_error.emit(str(e))
    
    def register_health_checks(self):
        """注册健康检查"""
        def camera_health_check():
            if self.camera_controller and self.camera_controller.is_connected:
                return ComponentStatus("camera", "healthy", 0, 0, 0, {})
            else:
                return ComponentStatus("camera", "error", 0, 1, 0, {"error": "未连接"})
        
        def motor_health_check():
            if self.motor_controller and self.motor_controller.is_connected:
                return ComponentStatus("motor", "healthy", 0, 0, 0, {})
            else:
                return ComponentStatus("motor", "error", 0, 1, 0, {"error": "未连接"})
        
        self.system_monitor.register_health_check("camera", camera_health_check)
        self.system_monitor.register_health_check("motor", motor_health_check)
    
    def connect_camera(self):
        """连接相机"""
        try:
            if not self.camera_controller:
                self.camera_controller = CameraController()
            
            if self.camera_controller.connect():
                self.status_indicators["camera"].set_status("healthy")
                self.info_panels["camera"].update_info_item("status", "已连接")
                self.notification_manager.show_notification("相机连接成功", "success")
                
                # 启动相机线程
                if not self.camera_thread:
                    self.camera_thread = OptimizedCameraThread(self.camera_controller)
                    self.camera_thread.start()
            
        except Exception as e:
            self.logger.error("连接相机失败", exception=e)
            self.status_indicators["camera"].set_status("error")
            self.notification_manager.show_notification(f"相机连接失败: {str(e)}", "error")
    
    def connect_motor(self):
        """连接电机"""
        try:
            if not self.motor_controller:
                self.motor_controller = OptimizedMotorController(self.config.default_com_port)
            
            if self.motor_controller.connect():
                self.status_indicators["motor"].set_status("healthy")
                self.info_panels["motor"].update_info_item("status", "已连接")
                self.notification_manager.show_notification("电机连接成功", "success")
            
        except Exception as e:
            self.logger.error("连接电机失败", exception=e)
            self.status_indicators["motor"].set_status("error")
            self.notification_manager.show_notification(f"电机连接失败: {str(e)}", "error")
    
    def start_scan(self):
        """开始扫描"""
        self.notification_manager.show_notification("扫描功能开发中", "info")
    
    def start_autofocus(self):
        """开始自动对焦"""
        self.notification_manager.show_notification("自动对焦功能开发中", "info")
    
    def show_config_dialog(self):
        """显示配置对话框"""
        dialog = ConfigDialog(self)
        if dialog.exec() == ConfigDialog.Accepted:
            self.notification_manager.show_notification("配置已更新", "success")
    
    def show_about_dialog(self):
        """显示关于对话框"""
        UIUtils.show_info_message(
            self, "关于", 
            "显微镜控制系统 v2.0\n\n"
            "一个现代化的显微镜控制和图像采集系统\n"
            "具有自动对焦、扫描拼接等功能"
        )
    
    def toggle_system_monitor(self):
        """切换系统监控显示"""
        self.notification_manager.show_notification("系统监控功能开发中", "info")
    
    def on_component_status_changed(self, component: str, status: str):
        """组件状态变更处理"""
        if component in self.status_indicators:
            self.status_indicators[component].set_status(status)
        
        self.logger.info(f"组件 {component} 状态变更为: {status}")
    
    def on_system_alert(self, level: str, component: str, message: str):
        """系统告警处理"""
        self.notification_manager.show_notification(
            f"{component}: {message}", level)
    
    def closeEvent(self, event):
        """关闭事件处理"""
        try:
            # 停止系统监控
            if self.system_monitor:
                self.system_monitor.stop_monitoring()
            
            # 停止相机线程
            if self.camera_thread:
                self.camera_thread.stop()
            
            # 断开连接
            if self.camera_controller:
                self.camera_controller.disconnect()
            
            if self.motor_controller:
                self.motor_controller.disconnect()
            
            # 保存配置
            save_config()
            
            self.logger.info("显微镜控制系统关闭")
            event.accept()
            
        except Exception as e:
            self.logger.error("关闭系统时发生错误", exception=e)
            event.accept()


def main():
    """主函数"""
    app = QApplication(sys.argv)
    app.setApplicationName("显微镜控制系统")
    app.setApplicationVersion("2.0")
    
    # 创建主窗口
    main_window = MicroscopeMainWindow()
    main_window.show()
    
    return app.exec()


if __name__ == "__main__":
    sys.exit(main())
