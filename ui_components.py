#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
UI组件库
提供通用的UI组件和工具函数
"""

from typing import Optional, Callable, Any, List
from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
    QProgressBar, QFrame, QSizePolicy, QGraphicsDropShadowEffect,
    QMessageBox, QDialog, QDialogButtonBox, QTextEdit, QScrollArea,
    QGroupBox, QGridLayout, QSpacerItem, QApplication, QStyle
)
from PySide6.QtCore import Qt, QTimer, Signal, QPropertyAnimation, QEasingCurve, QRect
from PySide6.QtGui import QFont, QColor, QPalette, QPixmap, QIcon, QPainter, QBrush

from config import get_config
from logger import get_logger, LoggerMixin


class StatusIndicator(QWidget):
    """状态指示器组件"""
    
    def __init__(self, size: int = 16, parent=None):
        super().__init__(parent)
        self.size = size
        self.status = "unknown"  # "healthy", "warning", "error", "unknown"
        self.setFixedSize(size, size)
        
        # 状态颜色映射
        self.colors = {
            "healthy": QColor(0, 200, 0),
            "warning": QColor(255, 165, 0),
            "error": QColor(255, 0, 0),
            "unknown": QColor(128, 128, 128)
        }
    
    def set_status(self, status: str):
        """设置状态"""
        if status in self.colors:
            self.status = status
            self.update()
    
    def paintEvent(self, event):
        """绘制状态指示器"""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)
        
        color = self.colors.get(self.status, self.colors["unknown"])
        painter.setBrush(QBrush(color))
        painter.setPen(Qt.NoPen)
        
        # 绘制圆形指示器
        margin = 2
        painter.drawEllipse(margin, margin, self.size - 2*margin, self.size - 2*margin)


class AnimatedButton(QPushButton):
    """带动画效果的按钮"""
    
    def __init__(self, text: str = "", parent=None):
        super().__init__(text, parent)
        self.animation = QPropertyAnimation(self, b"geometry")
        self.animation.setDuration(200)
        self.animation.setEasingCurve(QEasingCurve.OutCubic)
        
        # 添加阴影效果
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(10)
        shadow.setColor(QColor(0, 0, 0, 80))
        shadow.setOffset(2, 2)
        self.setGraphicsEffect(shadow)
    
    def enterEvent(self, event):
        """鼠标进入事件"""
        super().enterEvent(event)
        self._animate_scale(1.05)
    
    def leaveEvent(self, event):
        """鼠标离开事件"""
        super().leaveEvent(event)
        self._animate_scale(1.0)
    
    def _animate_scale(self, scale: float):
        """缩放动画"""
        current_rect = self.geometry()
        center = current_rect.center()
        
        new_width = int(current_rect.width() * scale)
        new_height = int(current_rect.height() * scale)
        
        new_rect = QRect(0, 0, new_width, new_height)
        new_rect.moveCenter(center)
        
        self.animation.setStartValue(current_rect)
        self.animation.setEndValue(new_rect)
        self.animation.start()


class ProgressDialog(QDialog, LoggerMixin):
    """进度对话框"""
    
    cancelled = Signal()
    
    def __init__(self, title: str = "处理中", message: str = "请稍候...", 
                 parent=None, cancellable: bool = True):
        super().__init__(parent)
        self.setWindowTitle(title)
        self.setModal(True)
        self.setFixedSize(400, 150)
        
        self.setup_ui(message, cancellable)
        
        # 居中显示
        if parent:
            self.move(parent.geometry().center() - self.rect().center())
    
    def setup_ui(self, message: str, cancellable: bool):
        """设置UI"""
        layout = QVBoxLayout(self)
        
        # 消息标签
        self.message_label = QLabel(message)
        self.message_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(self.message_label)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setValue(0)
        layout.addWidget(self.progress_bar)
        
        # 详细信息标签
        self.detail_label = QLabel("")
        self.detail_label.setAlignment(Qt.AlignCenter)
        self.detail_label.setStyleSheet("color: gray; font-size: 10px;")
        layout.addWidget(self.detail_label)
        
        # 按钮
        if cancellable:
            button_box = QDialogButtonBox(QDialogButtonBox.Cancel)
            button_box.rejected.connect(self._on_cancelled)
            layout.addWidget(button_box)
    
    def set_progress(self, value: int, message: str = "", detail: str = ""):
        """设置进度"""
        self.progress_bar.setValue(value)
        if message:
            self.message_label.setText(message)
        if detail:
            self.detail_label.setText(detail)
        
        QApplication.processEvents()
    
    def set_indeterminate(self, indeterminate: bool = True):
        """设置为不确定进度"""
        if indeterminate:
            self.progress_bar.setRange(0, 0)
        else:
            self.progress_bar.setRange(0, 100)
    
    def _on_cancelled(self):
        """取消按钮点击"""
        self.cancelled.emit()
        self.reject()


class InfoPanel(QFrame, LoggerMixin):
    """信息面板组件"""
    
    def __init__(self, title: str = "", parent=None):
        super().__init__(parent)
        self.title = title
        self.info_items = {}
        self.setup_ui()
    
    def setup_ui(self):
        """设置UI"""
        self.setFrameStyle(QFrame.StyledPanel)
        self.setLineWidth(1)
        
        layout = QVBoxLayout(self)
        
        # 标题
        if self.title:
            title_label = QLabel(self.title)
            title_label.setFont(QFont("Arial", 10, QFont.Bold))
            title_label.setStyleSheet("color: #333; padding: 5px;")
            layout.addWidget(title_label)
        
        # 信息网格
        self.grid_layout = QGridLayout()
        layout.addLayout(self.grid_layout)
        
        layout.addStretch()
    
    def add_info_item(self, key: str, label: str, value: str = ""):
        """添加信息项"""
        row = len(self.info_items)
        
        label_widget = QLabel(f"{label}:")
        label_widget.setStyleSheet("font-weight: bold; color: #555;")
        
        value_widget = QLabel(value)
        value_widget.setStyleSheet("color: #333;")
        
        self.grid_layout.addWidget(label_widget, row, 0)
        self.grid_layout.addWidget(value_widget, row, 1)
        
        self.info_items[key] = value_widget
    
    def update_info_item(self, key: str, value: str):
        """更新信息项"""
        if key in self.info_items:
            self.info_items[key].setText(value)
    
    def clear_info_items(self):
        """清空所有信息项"""
        for i in reversed(range(self.grid_layout.count())):
            self.grid_layout.itemAt(i).widget().setParent(None)
        self.info_items.clear()


class NotificationWidget(QFrame):
    """通知组件"""
    
    def __init__(self, message: str, notification_type: str = "info", 
                 duration: int = 3000, parent=None):
        super().__init__(parent)
        self.duration = duration
        self.setup_ui(message, notification_type)
        
        # 自动隐藏定时器
        if duration > 0:
            QTimer.singleShot(duration, self.hide_notification)
    
    def setup_ui(self, message: str, notification_type: str):
        """设置UI"""
        self.setFrameStyle(QFrame.StyledPanel)
        self.setLineWidth(2)
        
        # 根据类型设置样式
        styles = {
            "info": "background-color: #d1ecf1; border-color: #bee5eb; color: #0c5460;",
            "success": "background-color: #d4edda; border-color: #c3e6cb; color: #155724;",
            "warning": "background-color: #fff3cd; border-color: #ffeaa7; color: #856404;",
            "error": "background-color: #f8d7da; border-color: #f5c6cb; color: #721c24;"
        }
        
        style = styles.get(notification_type, styles["info"])
        self.setStyleSheet(f"QFrame {{ {style} border-radius: 5px; padding: 10px; }}")
        
        layout = QHBoxLayout(self)
        
        # 图标
        icon_label = QLabel()
        icon_style = QApplication.style()
        
        icons = {
            "info": icon_style.standardIcon(QStyle.SP_MessageBoxInformation),
            "success": icon_style.standardIcon(QStyle.SP_DialogApplyButton),
            "warning": icon_style.standardIcon(QStyle.SP_MessageBoxWarning),
            "error": icon_style.standardIcon(QStyle.SP_MessageBoxCritical)
        }
        
        icon = icons.get(notification_type, icons["info"])
        icon_label.setPixmap(icon.pixmap(16, 16))
        layout.addWidget(icon_label)
        
        # 消息
        message_label = QLabel(message)
        message_label.setWordWrap(True)
        layout.addWidget(message_label, 1)
        
        # 关闭按钮
        close_btn = QPushButton("×")
        close_btn.setFixedSize(20, 20)
        close_btn.setStyleSheet("border: none; font-weight: bold; font-size: 14px;")
        close_btn.clicked.connect(self.hide_notification)
        layout.addWidget(close_btn)
    
    def hide_notification(self):
        """隐藏通知"""
        self.hide()
        self.deleteLater()


class NotificationManager(QWidget, LoggerMixin):
    """通知管理器"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.notifications = []
        self.setup_ui()
    
    def setup_ui(self):
        """设置UI"""
        self.layout = QVBoxLayout(self)
        self.layout.setContentsMargins(10, 10, 10, 10)
        self.layout.setSpacing(5)
        self.layout.addStretch()
        
        # 设置为浮动在父窗口上方
        self.setWindowFlags(Qt.Tool | Qt.FramelessWindowHint)
        self.setAttribute(Qt.WA_TranslucentBackground)
    
    def show_notification(self, message: str, notification_type: str = "info", 
                         duration: int = 3000):
        """显示通知"""
        notification = NotificationWidget(message, notification_type, duration, self)
        
        # 添加到布局
        self.layout.insertWidget(self.layout.count() - 1, notification)
        self.notifications.append(notification)
        
        # 限制通知数量
        if len(self.notifications) > 5:
            old_notification = self.notifications.pop(0)
            old_notification.hide_notification()
        
        # 调整位置和大小
        self._update_position()
        
        self.logger.info(f"显示通知: {message} ({notification_type})")
    
    def _update_position(self):
        """更新位置"""
        if self.parent():
            parent_rect = self.parent().geometry()
            self.setGeometry(
                parent_rect.right() - 350,
                parent_rect.top() + 50,
                320,
                min(400, len(self.notifications) * 60 + 50)
            )
    
    def clear_all(self):
        """清空所有通知"""
        for notification in self.notifications:
            notification.hide_notification()
        self.notifications.clear()


class ThemeManager:
    """主题管理器"""
    
    @staticmethod
    def apply_dark_theme(app: QApplication):
        """应用暗色主题"""
        dark_palette = QPalette()
        
        # 设置颜色
        dark_palette.setColor(QPalette.Window, QColor(53, 53, 53))
        dark_palette.setColor(QPalette.WindowText, QColor(255, 255, 255))
        dark_palette.setColor(QPalette.Base, QColor(25, 25, 25))
        dark_palette.setColor(QPalette.AlternateBase, QColor(53, 53, 53))
        dark_palette.setColor(QPalette.ToolTipBase, QColor(0, 0, 0))
        dark_palette.setColor(QPalette.ToolTipText, QColor(255, 255, 255))
        dark_palette.setColor(QPalette.Text, QColor(255, 255, 255))
        dark_palette.setColor(QPalette.Button, QColor(53, 53, 53))
        dark_palette.setColor(QPalette.ButtonText, QColor(255, 255, 255))
        dark_palette.setColor(QPalette.BrightText, QColor(255, 0, 0))
        dark_palette.setColor(QPalette.Link, QColor(42, 130, 218))
        dark_palette.setColor(QPalette.Highlight, QColor(42, 130, 218))
        dark_palette.setColor(QPalette.HighlightedText, QColor(0, 0, 0))
        
        app.setPalette(dark_palette)
    
    @staticmethod
    def apply_light_theme(app: QApplication):
        """应用亮色主题"""
        app.setPalette(QApplication.style().standardPalette())
    
    @staticmethod
    def get_custom_stylesheet() -> str:
        """获取自定义样式表"""
        return """
        QMainWindow {
            background-color: #f0f0f0;
        }
        
        QTabWidget::pane {
            border: 1px solid #c0c0c0;
            background-color: white;
        }
        
        QTabBar::tab {
            background-color: #e0e0e0;
            padding: 8px 16px;
            margin-right: 2px;
            border-top-left-radius: 4px;
            border-top-right-radius: 4px;
        }
        
        QTabBar::tab:selected {
            background-color: white;
            border-bottom: 2px solid #007acc;
        }
        
        QPushButton {
            background-color: #007acc;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            font-weight: bold;
        }
        
        QPushButton:hover {
            background-color: #005a9e;
        }
        
        QPushButton:pressed {
            background-color: #004578;
        }
        
        QPushButton:disabled {
            background-color: #cccccc;
            color: #666666;
        }
        
        QGroupBox {
            font-weight: bold;
            border: 2px solid #cccccc;
            border-radius: 5px;
            margin-top: 1ex;
            padding-top: 10px;
        }
        
        QGroupBox::title {
            subcontrol-origin: margin;
            left: 10px;
            padding: 0 5px 0 5px;
        }
        
        QProgressBar {
            border: 2px solid #cccccc;
            border-radius: 5px;
            text-align: center;
        }
        
        QProgressBar::chunk {
            background-color: #007acc;
            border-radius: 3px;
        }
        """


class UIUtils:
    """UI工具类"""

    @staticmethod
    def center_window(window, parent=None):
        """居中显示窗口"""
        if parent:
            parent_rect = parent.geometry()
            window.move(parent_rect.center() - window.rect().center())
        else:
            screen = QApplication.primaryScreen().geometry()
            window.move(screen.center() - window.rect().center())

    @staticmethod
    def show_error_message(parent, title: str, message: str):
        """显示错误消息"""
        QMessageBox.critical(parent, title, message)

    @staticmethod
    def show_warning_message(parent, title: str, message: str):
        """显示警告消息"""
        QMessageBox.warning(parent, title, message)

    @staticmethod
    def show_info_message(parent, title: str, message: str):
        """显示信息消息"""
        QMessageBox.information(parent, title, message)

    @staticmethod
    def ask_confirmation(parent, title: str, message: str) -> bool:
        """询问确认"""
        reply = QMessageBox.question(
            parent, title, message,
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        return reply == QMessageBox.Yes

    @staticmethod
    def create_separator() -> QFrame:
        """创建分隔线"""
        separator = QFrame()
        separator.setFrameShape(QFrame.HLine)
        separator.setFrameShadow(QFrame.Sunken)
        return separator

    @staticmethod
    def create_spacer(width: int = 0, height: int = 0) -> QSpacerItem:
        """创建空白间隔"""
        return QSpacerItem(width, height, QSizePolicy.Expanding, QSizePolicy.Expanding)


if __name__ == "__main__":
    from PySide6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget
    import sys

    app = QApplication(sys.argv)

    # 应用自定义样式
    app.setStyleSheet(ThemeManager.get_custom_stylesheet())

    # 创建主窗口
    main_window = QMainWindow()
    main_window.setWindowTitle("UI组件测试")
    main_window.resize(800, 600)

    central_widget = QWidget()
    main_window.setCentralWidget(central_widget)

    layout = QVBoxLayout(central_widget)

    # 测试组件
    status_indicator = StatusIndicator()
    status_indicator.set_status("healthy")
    layout.addWidget(status_indicator)

    animated_btn = AnimatedButton("动画按钮")
    layout.addWidget(animated_btn)

    info_panel = InfoPanel("系统信息")
    info_panel.add_info_item("cpu", "CPU使用率", "25%")
    info_panel.add_info_item("memory", "内存使用率", "60%")
    layout.addWidget(info_panel)

    # 通知管理器
    notification_manager = NotificationManager(main_window)

    def show_test_notification():
        notification_manager.show_notification("这是一个测试通知", "info")

    test_btn = QPushButton("显示通知")
    test_btn.clicked.connect(show_test_notification)
    layout.addWidget(test_btn)

    # 居中显示
    UIUtils.center_window(main_window)
    main_window.show()

    sys.exit(app.exec())
