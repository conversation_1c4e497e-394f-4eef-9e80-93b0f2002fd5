#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
基于PyTorch实现的高效自动对焦模块
"""

import torch
import torch.nn.functional as F
import numpy as np
import time
from typing import Optional, List, Tuple, Callable
from PySide6.QtGui import QImage
import cv2  # 添加OpenCV库
# 导入EfficientNetV2相关模块
from torchvision.models import efficientnet_v2_s, EfficientNet_V2_S_Weights
from PIL import Image
from skimage.measure import shannon_entropy


class PyTorchAutoFocus:
    """利用PyTorch实现的高效自动对焦类"""
    
    def __init__(self, use_gpu: bool = True):
        """初始化PyTorch自动对焦
        
        Args:
            use_gpu: 是否使用GPU加速
        """
        # 检查CUDA是否可用
        self.cuda_available = torch.cuda.is_available() and use_gpu
        self.device = torch.device("cuda" if self.cuda_available else "cpu")
        
        # 对焦参数
        self.focus_method = "laplacian"  # 默认对焦算法
        
        # 对焦评分缓存 - 避免重复计算
        self.score_cache = {}
        
        # 初始化EfficientNet模型
        self.efficientnet_model = None
        try:
                # 使用预训练的EfficientNetV2-S模型
            weights = EfficientNet_V2_S_Weights.DEFAULT
            self.efficientnet_model = efficientnet_v2_s(weights=weights)
            self.efficientnet_model.eval()
            if self.cuda_available:
                self.efficientnet_model = self.efficientnet_model.to(self.device)
            self.preprocess = weights.transforms()
            print("EfficientNetV2-S模型加载成功")
        except Exception as e:
            print(f"EfficientNetV2-S模型加载失败: {e}")
            self.efficientnet_model = None
        
        # 初始化OpenCV级联分类器（用于辅助对焦）
        try:
            # 加载OpenCV预训练的Haar级联分类器（用于检测图像结构）
            self.face_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_frontalface_default.xml')
            self.edge_cascade = True  # 标记OpenCV边缘检测可用
        except Exception as e:
            print(f"OpenCV级联分类器加载失败: {e}")
            self.face_cascade = None
            self.edge_cascade = False
        
        # 检查是否初始化成功
        print(f"PyTorch自动对焦初始化，设备: {self.device}")
    
    def clear_cache(self):
        """清除对焦评分缓存"""
        self.score_cache = {}
    
    def qimage_to_tensor(self, qimg: QImage) -> torch.Tensor:
        """将QImage转换为PyTorch张量
        
        Args:
            qimg: QImage图像
            
        Returns:
            torch.Tensor: PyTorch格式图像张量
        """
        if qimg is None:
            return None
            
        # 获取图像尺寸
        width = qimg.width()
        height = qimg.height()
        
        # 转换逻辑 - 不使用sip.voidptr
        if qimg.format() == QImage.Format_RGB32 or qimg.format() == QImage.Format_ARGB32:
            # 对于32位格式
            bytes_per_line = qimg.bytesPerLine()
            ptr = qimg.constBits()
            
            buffer = memoryview(ptr).tobytes()
            # 重新构造为numpy数组
            arr = np.frombuffer(buffer, dtype=np.uint8).reshape(height, bytes_per_line // 4, 4)
            # 只保留前3个通道 (去掉Alpha通道)
            arr = arr[:, :width, :3].copy()
        elif qimg.format() == QImage.Format_RGB888:
            # 对于RGB888格式直接转换
            bytes_per_line = qimg.bytesPerLine()
            ptr = qimg.constBits()
            
            buffer = memoryview(ptr).tobytes()
            arr = np.frombuffer(buffer, dtype=np.uint8).reshape(height, bytes_per_line // 3, 3)
            arr = arr[:, :width, :].copy()
        elif qimg.format() == QImage.Format_Grayscale8:
            # 灰度图像
            bytes_per_line = qimg.bytesPerLine()
            ptr = qimg.constBits()
            
            buffer = memoryview(ptr).tobytes()
            arr = np.frombuffer(buffer, dtype=np.uint8).reshape(height, bytes_per_line)
            arr = arr[:, :width].copy()
            # 扩展到3D数组以保持一致性
            arr = np.expand_dims(arr, axis=2)
        else:
            # 其他格式：先转换为RGB888
            rgb_img = qimg.convertToFormat(QImage.Format_RGB888)
            bytes_per_line = rgb_img.bytesPerLine()
            ptr = rgb_img.constBits()
            
            buffer = memoryview(ptr).tobytes()
            arr = np.frombuffer(buffer, dtype=np.uint8).reshape(height, bytes_per_line // 3, 3)
            arr = arr[:, :width, :].copy()
            
        # 转换为PyTorch张量
        tensor = torch.from_numpy(arr).float()
        
        # 灰度化处理
        if tensor.shape[2] == 3:
            # 使用BGR转灰度公式: 0.299*R + 0.587*G + 0.114*B
            tensor = tensor[..., 0] * 0.299 + tensor[..., 1] * 0.587 + tensor[..., 2] * 0.114
        else:
            tensor = tensor.squeeze(2)  # 如果已经是灰度，去掉通道维度
            
        # 规范化 (0-255) -> (0-1)
        tensor = tensor / 255.0
        
        # 添加批次维度并移动到设备
        tensor = tensor.unsqueeze(0).unsqueeze(0).to(self.device)
        
        return tensor
    
    def calculate_focus_score(self, image: QImage, position_key: Optional[float] = None) -> float:
        """计算图像的对焦评分
        
        Args:
            image: QImage格式图像
            position_key: 可选的位置键，用于缓存
        
        Returns:
            float: 对焦评分，值越高表示对焦越好
        """
        # 检查缓存
        if position_key is not None and position_key in self.score_cache:
            return self.score_cache[position_key]
            
        if image is None:
            return 0.0
            
        # OpenCV方法需要转换为OpenCV格式
        if self.focus_method.startswith("opencv_"):
            cv_image = self._qimage_to_opencv(image)
            if cv_image is None:
                return 0.0
                
            if self.focus_method == "opencv_laplacian":
                # OpenCV拉普拉斯算子 - 通常比PyTorch实现快
                if len(cv_image.shape) > 2:
                    gray = cv2.cvtColor(cv_image, cv2.COLOR_BGR2GRAY)
                else:
                    gray = cv_image
                # 使用更大的核尺寸和更高的精度
                lap_var = cv2.Laplacian(gray, cv2.CV_64F, ksize=3)
                score = np.std(lap_var)
                
                # 缓存结果
                if position_key is not None:
                    self.score_cache[position_key] = score
                return score
                
            elif self.focus_method == "opencv_canny":
                # 基于Canny边缘检测的对焦评分 - 对边缘结构敏感
                if len(cv_image.shape) > 2:
                    gray = cv2.cvtColor(cv_image, cv2.COLOR_BGR2GRAY)
                else:
                    gray = cv_image
                # 自适应阈值
                median_val = np.median(gray)
                sigma = 0.33
                lower = int(max(0, (1.0 - sigma) * median_val))
                upper = int(min(255, (1.0 + sigma) * median_val))
                
                # 应用Canny边缘检测
                edges = cv2.Canny(gray, lower, upper)
                # 评分为检测到的边缘像素数量
                score = np.sum(edges > 0) / (edges.shape[0] * edges.shape[1])
                
                # 缓存结果
                if position_key is not None:
                    self.score_cache[position_key] = score
                return score
                
            elif self.focus_method == "opencv_dof":
                # 结合景深和局部方差的复合算法
                if len(cv_image.shape) > 2:
                    gray = cv2.cvtColor(cv_image, cv2.COLOR_BGR2GRAY)
                else:
                    gray = cv_image
                
                # 1. 计算全局方差作为清晰度基准
                global_var = np.var(gray)
                
                # 2. 计算局部区块方差
                block_size = min(32, min(gray.shape) // 4)  # 自适应区块大小
                if block_size < 8:  # 图像太小时的保护
                    block_size = 8
                
                local_vars = []
                for y in range(0, gray.shape[0] - block_size, block_size):
                    for x in range(0, gray.shape[1] - block_size, block_size):
                        block = gray[y:y+block_size, x:x+block_size]
                        local_vars.append(np.var(block))
                
                # 3. 取局部区块方差的前25%作为高清晰度区域
                if local_vars:
                    local_vars.sort(reverse=True)
                    top_k = max(3, len(local_vars) // 4)
                    top_vars = local_vars[:top_k]
                    local_var_score = np.mean(top_vars)
                else:
                    local_var_score = 0
                
                # 4. 拉普拉斯变换评估边缘清晰度
                lap_var = cv2.Laplacian(gray, cv2.CV_64F, ksize=3)
                lap_score = np.std(lap_var)
                
                # 5. 组合评分 (加权平均)
                # 根据经验，边缘评分权重稍高
                score = 0.4 * global_var + 0.3 * local_var_score + 0.3 * lap_score
                
                # 缓存结果
                if position_key is not None:
                    self.score_cache[position_key] = score
                return score
                
            elif self.focus_method == "opencv_phase_corr":
                # 基于相位相关的自动对焦算法
                # 相位相关方法能更好地检测微小的位移和模糊
                if len(cv_image.shape) > 2:
                    gray = cv2.cvtColor(cv_image, cv2.COLOR_BGR2GRAY)
                else:
                    gray = cv_image
                
                # 转换为32位浮点数
                gray_float = np.float32(gray)
                
                # 预处理 - 均值滤波降噪
                smoothed = cv2.GaussianBlur(gray_float, (5, 5), 0)
                
                # 计算图像的离散傅里叶变换
                dft = cv2.dft(smoothed, flags=cv2.DFT_COMPLEX_OUTPUT)
                dft_shift = np.fft.fftshift(dft)
                
                # 计算幅度谱
                magnitude_spectrum = cv2.magnitude(dft_shift[:,:,0], dft_shift[:,:,1])
                
                # 计算相位相关性得分
                # 1. 创建小高斯核
                rows, cols = gray.shape
                crow, ccol = rows//2, cols//2
                mask = np.zeros((rows, cols), np.float32)
                r = min(20, min(rows, cols)//10)  # 半径自适应
                center = [crow, ccol]
                x, y = np.ogrid[:rows, :cols]
                mask_area = (x - center[0])**2 + (y - center[1])**2 <= r*r
                mask[mask_area] = 1
                
                # 计算高频信息密度
                high_freq_score = np.sum(magnitude_spectrum * (1 - mask)) / np.sum(magnitude_spectrum)
                
                # 相位信息计算
                phase = np.arctan2(dft_shift[:,:,1], dft_shift[:,:,0])
                phase_coherence = np.std(phase)  # 相位一致性
                
                # 组合评分 - 相位信息和高频信息共同决定
                score = high_freq_score * 0.6 + phase_coherence * 0.4
                
                # 缓存结果
                if position_key is not None:
                    self.score_cache[position_key] = score
                return score
                
            elif self.focus_method == "opencv_fft_sharpness":
                # 基于傅里叶变换的锐度评估
                # 特别适合显微镜图像中的周期性结构
                if len(cv_image.shape) > 2:
                    gray = cv2.cvtColor(cv_image, cv2.COLOR_BGR2GRAY)
                else:
                    gray = cv_image
                
                # 应用汉宁窗减少边缘效应
                h, w = gray.shape
                hann_window = cv2.createHanningWindow((w, h), cv2.CV_32F)
                windowed = gray.astype(np.float32) * hann_window
                
                # 计算图像的FFT
                dft = cv2.dft(windowed, flags=cv2.DFT_COMPLEX_OUTPUT)
                dft_shift = np.fft.fftshift(dft)
                
                # 计算幅度谱
                magnitude = cv2.magnitude(dft_shift[:,:,0], dft_shift[:,:,1])
                # 对数变换以增强可视性
                magnitude = np.log(magnitude + 1)
                
                # 构建环形滤波器来分析不同频率带
                center_y, center_x = h//2, w//2
                y, x = np.ogrid[:h, :w]
                
                # 定义几个半径来划分不同频率带
                r_low = min(h, w) // 16  # 低频带半径
                r_mid = min(h, w) // 8   # 中频带半径
                r_high = min(h, w) // 4  # 高频带半径
                
                # 计算每个点到中心的距离
                dist_from_center = np.sqrt((x - center_x)**2 + (y - center_y)**2)
                
                # 创建不同频率带的掩码
                low_freq_mask = dist_from_center <= r_low
                mid_freq_mask = (dist_from_center > r_low) & (dist_from_center <= r_mid)
                high_freq_mask = (dist_from_center > r_mid) & (dist_from_center <= r_high)
                
                # 计算每个频率带的能量
                low_energy = np.mean(magnitude[low_freq_mask])
                mid_energy = np.mean(magnitude[mid_freq_mask])
                high_energy = np.mean(magnitude[high_freq_mask])
                
                # 清晰的图像通常中高频能量更高，低频能量适中
                # 组合评分，重点关注中高频与低频的比值
                score = (mid_energy + high_energy) / (low_energy + 1e-10)
                
                # 缓存结果
                if position_key is not None:
                    self.score_cache[position_key] = score
                return score
                
            elif self.focus_method == "opencv_smd":
                # SMD (Sum of Modified Differences) 算法
                # 这是专门为显微镜对焦设计的算法，对细节特别敏感
                if len(cv_image.shape) > 2:
                    gray = cv2.cvtColor(cv_image, cv2.COLOR_BGR2GRAY)
                else:
                    gray = cv_image
                
                # 转换为浮点数格式
                gray = gray.astype(np.float32)
                
                # 创建x和y方向的边缘检测核
                kernel_x = np.array([[-1, 0, 1], [-2, 0, 2], [-1, 0, 1]], dtype=np.float32)
                kernel_y = np.array([[-1, -2, -1], [0, 0, 0], [1, 2, 1]], dtype=np.float32)
                
                # 应用卷积操作
                grad_x = cv2.filter2D(gray, -1, kernel_x)
                grad_y = cv2.filter2D(gray, -1, kernel_y)
                
                # 计算修正差分和，使用方向导数修正以增强效果
                abs_grad_x = np.abs(grad_x)
                abs_grad_y = np.abs(grad_y)
                
                # 应用对比度增强
                abs_grad_x = np.power(abs_grad_x, 1.5)
                abs_grad_y = np.power(abs_grad_y, 1.5)
                
                # 计算SMD得分 - 考虑局部加权
                h, w = gray.shape
                weight_window = np.ones((h, w), dtype=np.float32)
                
                # 为图像中心区域赋予更高权重
                center_y, center_x = h//2, w//2
                y, x = np.ogrid[:h, :w]
                dist_from_center = np.sqrt((x - center_x)**2 + (y - center_y)**2)
                max_dist = np.sqrt(center_x**2 + center_y**2)
                
                # 创建径向权重衰减
                weight_window = 1.0 - 0.5 * (dist_from_center / max_dist)
                
                # 应用权重并计算最终得分
                weighted_sum = np.sum((abs_grad_x + abs_grad_y) * weight_window)
                normalization = np.sum(weight_window) * 2  # x和y方向
                score = weighted_sum / normalization
                
                # 缓存结果
                if position_key is not None:
                    self.score_cache[position_key] = score
                return score
                
            elif self.focus_method == "opencv_energy_gradient":
                # 基于能量梯度的对焦算法，对生物样本特别有效
                if len(cv_image.shape) > 2:
                    gray = cv2.cvtColor(cv_image, cv2.COLOR_BGR2GRAY)
                else:
                    gray = cv_image
                
                # 高斯模糊去噪，保留主要结构
                smoothed = cv2.GaussianBlur(gray, (3, 3), 0)
                
                # 提取多尺度梯度信息
                grad_x = cv2.Sobel(smoothed, cv2.CV_64F, 1, 0, ksize=3)
                grad_y = cv2.Sobel(smoothed, cv2.CV_64F, 0, 1, ksize=3)
                
                # 计算梯度能量
                gradient_magnitude = np.sqrt(grad_x**2 + grad_y**2)
                
                # 计算梯度能量的方差（清晰图像的梯度方差更大）
                energy_variance = np.var(gradient_magnitude)
                
                # 计算梯度熵（清晰图像的熵通常更高）
                # 归一化梯度幅度
                normalized = gradient_magnitude / (np.max(gradient_magnitude) + 1e-10)
                # 量化为8位以计算熵
                bins = 256
                hist, _ = np.histogram(normalized, bins, range=(0, 1))
                hist = hist / hist.sum()
                non_zero = hist > 0
                entropy = -np.sum(hist[non_zero] * np.log2(hist[non_zero]))
                
                # 提取高频结构的数量（EdgeBox算法简化版）
                # 对梯度幅度二值化
                thresh = np.mean(gradient_magnitude) * 1.5
                binary_edges = (gradient_magnitude > thresh).astype(np.uint8) * 255
                
                # 使用形态学操作清理噪声
                kernel = np.ones((3, 3), np.uint8)
                binary_edges = cv2.morphologyEx(binary_edges, cv2.MORPH_CLOSE, kernel)
                
                # 找到边缘的轮廓
                contours, _ = cv2.findContours(binary_edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
                
                # 计算结构复杂度分数
                structure_score = 0
                if len(contours) > 0:
                    # 计算轮廓的总周长和面积
                    total_perimeter = sum(cv2.arcLength(cnt, True) for cnt in contours)
                    total_area = sum(cv2.contourArea(cnt) for cnt in contours)
                    
                    # 计算周长/面积比率（越高表示结构越复杂）
                    if total_area > 0:
                        structure_score = total_perimeter / np.sqrt(total_area)
                        # 归一化
                        structure_score = np.log1p(structure_score) / 10.0
                
                # 组合三种评分，加权平均
                score = 0.4 * energy_variance + 0.3 * entropy + 0.3 * structure_score
                
                # 缓存结果
                if position_key is not None:
                    self.score_cache[position_key] = score
                return score
                
            # 新增的6种对焦方法
            elif self.focus_method == "opencv_local_variance":
                # 基于局部方差的对焦评价方法
                if len(cv_image.shape) > 2:
                    gray = cv2.cvtColor(cv_image, cv2.COLOR_BGR2GRAY)
                else:
                    gray = cv_image
                
                # 使用ksize=5的局部窗口计算局部方差
                ksize = 5
                mean = cv2.blur(gray, (ksize, ksize))
                mean_sq = cv2.blur(np.square(gray.astype(np.float32)), (ksize, ksize))
                variance = mean_sq - np.square(mean)
                
                # 返回局部方差的平均值作为对焦得分
                score = np.mean(variance)
                
                # 缓存结果
                if position_key is not None:
                    self.score_cache[position_key] = score
                return score
                
            elif self.focus_method == "opencv_entropy":
                # 基于熵的对焦评价方法
                if len(cv_image.shape) > 2:
                    gray = cv2.cvtColor(cv_image, cv2.COLOR_BGR2GRAY)
                else:
                    gray = cv_image
                
                # 使用skimage的shannon_entropy计算熵
                
                score = shannon_entropy(gray)
                
                # 缓存结果
                if position_key is not None:
                    self.score_cache[position_key] = score
                return score
                
            elif self.focus_method == "opencv_tenengrad":
                # Tenengrad (基于Sobel梯度的对焦评价方法)
                if len(cv_image.shape) > 2:
                    gray = cv2.cvtColor(cv_image, cv2.COLOR_BGR2GRAY)
                else:
                    gray = cv_image
                
                # 计算X和Y方向的Sobel梯度
                sobel_x = cv2.Sobel(gray, cv2.CV_64F, 1, 0, ksize=3)
                sobel_y = cv2.Sobel(gray, cv2.CV_64F, 0, 1, ksize=3)
                
                # 计算梯度幅度
                gradient_magnitude = np.sqrt(sobel_x**2 + sobel_y**2)
                
                # 计算梯度幅度的平均值作为对焦得分
                score = np.mean(gradient_magnitude)
                
                # 缓存结果
                if position_key is not None:
                    self.score_cache[position_key] = score
                return score
                
            elif self.focus_method == "opencv_brenner_gradient":
                # Brenner梯度对焦评价方法
                if len(cv_image.shape) > 2:
                    gray = cv2.cvtColor(cv_image, cv2.COLOR_BGR2GRAY)
                else:
                    gray = cv_image
                
                # 计算相隔2个像素的水平方向上的差异
                shifted = np.roll(gray, -2, axis=1)  # 水平方向移动2个像素
                diff = (gray.astype(np.float32) - shifted.astype(np.float32)) ** 2
                
                # 计算差异的总和作为对焦得分
                score = np.sum(diff)
                
                # 缓存结果
                if position_key is not None:
                    self.score_cache[position_key] = score
                return score
                
            elif self.focus_method == "opencv_sobel_variance":
                # Sobel梯度和方差组合的对焦评价方法
                if len(cv_image.shape) > 2:
                    gray = cv2.cvtColor(cv_image, cv2.COLOR_BGR2GRAY)
                else:
                    gray = cv_image
                
                # 计算Sobel梯度
                sobel_x = cv2.Sobel(gray, cv2.CV_64F, 1, 0, ksize=3)
                sobel_y = cv2.Sobel(gray, cv2.CV_64F, 0, 1, ksize=3)
                sobel_magnitude = np.sqrt(sobel_x**2 + sobel_y**2)
                
                # 计算全局方差
                variance = np.var(gray)
                
                # 组合Sobel梯度和方差作为对焦得分
                score = np.mean(sobel_magnitude) + variance
                
                # 缓存结果
                if position_key is not None:
                    self.score_cache[position_key] = score
                return score
                
            elif self.focus_method == "opencv_laplacian_var":
                # 基于拉普拉斯算子的对焦评价方法
                if len(cv_image.shape) > 2:
                    gray = cv2.cvtColor(cv_image, cv2.COLOR_BGR2GRAY)
                else:
                    gray = cv_image
                
                # 应用拉普拉斯算子
                laplacian = cv2.Laplacian(gray, cv2.CV_64F)
                
                # 计算拉普拉斯结果的方差作为对焦得分
                score = np.var(laplacian)
                
                # 缓存结果
                if position_key is not None:
                    self.score_cache[position_key] = score
                return score
        
        # EfficientNet深度学习方法
        elif self.focus_method == "efficientnet" and self.efficientnet_model:
            try:
                # 转换为PIL图像（torchvision预处理需要）
                cv_image = self._qimage_to_opencv(image)
                if cv_image is None:
                    return 0.0
                
                # 转换为RGB格式
                if len(cv_image.shape) > 2:
                    if cv_image.shape[2] == 3:
                        pil_img = cv2.cvtColor(cv_image, cv2.COLOR_BGR2RGB)
                    else:
                        pil_img = cv_image
                else:
                    # 单通道灰度图转为3通道
                    pil_img = cv2.cvtColor(cv_image, cv2.COLOR_GRAY2RGB)
                
                # 转换为PIL Image
                pil_img = Image.fromarray(pil_img)
                
                # 预处理图像
                input_tensor = self.preprocess(pil_img).unsqueeze(0).to(self.device)
                
                # 使用EfficientNet提取特征
                with torch.no_grad():
                    # 提取特征向量而不是分类结果
                    features = self.efficientnet_model.features(input_tensor)
                    
                # 使用特征图的方差和熵作为清晰度评分
                # 高清晰度图像通常有更大的特征方差和熵
                features_var = torch.var(features).item()
                
                # 计算特征图的熵
                norm_features = F.softmax(features.reshape(features.size(0), -1), dim=1)
                entropy = -torch.sum(norm_features * torch.log2(norm_features + 1e-10), dim=1).mean().item()
                
                # 合并评分
                score = features_var * 0.6 + entropy * 0.4
                
                # 缓存结果
                if position_key is not None:
                    self.score_cache[position_key] = score
                return score
            except Exception as e:
                print(f"EfficientNet对焦评分计算错误: {e}")
                # 回退到拉普拉斯算法
                self.focus_method = "laplacian"
        
        # 使用PyTorch张量计算的方法
        img_tensor = self.qimage_to_tensor(image)
        if img_tensor is None:
            return 0.0
            
        with torch.no_grad():  # 不需要梯度计算
            score = 0.0
            
            if self.focus_method == "laplacian":
                # 拉普拉斯算子卷积核
                laplacian_kernel = torch.tensor([
                    [0, 1, 0],
                    [1, -4, 1],
                    [0, 1, 0]
                ], dtype=torch.float32).unsqueeze(0).unsqueeze(0).to(self.device)
                
                # 使用PyTorch的卷积操作应用拉普拉斯算子
                laplacian = F.conv2d(img_tensor, laplacian_kernel, padding=1)
                score = torch.std(laplacian).item()
                
            elif self.focus_method == "sobel":
                # Sobel算子卷积核 (x方向和y方向)
                sobel_x = torch.tensor([
                    [-1, 0, 1],
                    [-2, 0, 2],
                    [-1, 0, 1]
                ], dtype=torch.float32).unsqueeze(0).unsqueeze(0).to(self.device)
                
                sobel_y = torch.tensor([
                    [-1, -2, -1],
                    [0, 0, 0],
                    [1, 2, 1]
                ], dtype=torch.float32).unsqueeze(0).unsqueeze(0).to(self.device)
                
                # 应用Sobel算子
                grad_x = F.conv2d(img_tensor, sobel_x, padding=1)
                grad_y = F.conv2d(img_tensor, sobel_y, padding=1)
                
                # 计算梯度幅度
                grad_magnitude = torch.sqrt(grad_x**2 + grad_y**2)
                score = torch.mean(grad_magnitude).item()
                
            elif self.focus_method == "tenengrad":
                # Sobel算子卷积核 (与上面相同)
                sobel_x = torch.tensor([
                    [-1, 0, 1],
                    [-2, 0, 2],
                    [-1, 0, 1]
                ], dtype=torch.float32).unsqueeze(0).unsqueeze(0).to(self.device)
                
                sobel_y = torch.tensor([
                    [-1, -2, -1],
                    [0, 0, 0],
                    [1, 2, 1]
                ], dtype=torch.float32).unsqueeze(0).unsqueeze(0).to(self.device)
                
                # 应用Sobel算子
                grad_x = F.conv2d(img_tensor, sobel_x, padding=1)
                grad_y = F.conv2d(img_tensor, sobel_y, padding=1)
                
                # 计算梯度平方和
                teng = grad_x**2 + grad_y**2
                score = torch.mean(teng).item()
                
            elif self.focus_method == "variance":
                # 使用PyTorch计算方差
                score = torch.var(img_tensor).item()
            
            elif self.focus_method == "fft":
                # 使用频域分析 - FFT方法
                # 获取频域表示
                fft = torch.fft.fft2(img_tensor.squeeze())
                fft_shifted = torch.fft.fftshift(fft)
                
                # 计算频谱幅度
                magnitude_spectrum = torch.log(torch.abs(fft_shifted) + 1)
                
                # 使用高频信息作为清晰度度量
                h, w = magnitude_spectrum.shape
                center_h, center_w = h // 2, w // 2
                
                # 创建高频掩码 - 忽略低频区域
                y, x = torch.meshgrid(torch.arange(h), torch.arange(w), indexing='ij')
                y = y.to(self.device)
                x = x.to(self.device)
                distance_from_center = torch.sqrt((x - center_w)**2 + (y - center_h)**2)
                
                # 只考虑中高频区域
                min_radius = min(h, w) // 8
                max_radius = min(h, w) // 2
                mask = (distance_from_center > min_radius) & (distance_from_center < max_radius)
                
                # 计算高频区域能量
                score = torch.mean(magnitude_spectrum[mask]).item()
                
            # 缓存结果
            if position_key is not None:
                self.score_cache[position_key] = score
                
            return score
            
    def set_focus_method(self, method: str):
        """设置对焦评估算法
        
        Args:
            method: 对焦算法，可选值: "laplacian", "sobel", "tenengrad", "variance", "fft",
                              "opencv_laplacian", "opencv_canny", "opencv_dof", "efficientnet",
                              "opencv_phase_corr", "opencv_fft_sharpness", "opencv_smd", "opencv_energy_gradient"
        """
        methods = ["laplacian", "sobel", "tenengrad", "variance", "fft", 
                   "opencv_laplacian", "opencv_canny", "opencv_dof", "efficientnet",
                   "opencv_phase_corr", "opencv_fft_sharpness", "opencv_smd", "opencv_energy_gradient"]
        if method in methods:
            # 验证特殊方法的可用性
            if method == "efficientnet" and not self.efficientnet_model:
                print("EfficientNet模型不可用，将使用默认laplacian方法")
                method = "laplacian"
            elif method.startswith("opencv_") and not self.edge_cascade:
                print("OpenCV级联分类器不可用，将使用默认laplacian方法")
                method = "laplacian"
            elif method == "opencv_entropy":
                print("将使用默认laplacian方法")
                method = "laplacian"
                
            # 设置新方法时清除缓存
            self.clear_cache()
            self.focus_method = method
            return True
        return False
        
    def find_best_focus(self, z_positions: List[float], 
                         get_image_at_z: Callable[[float], QImage],
                         progress_callback: Optional[Callable[[float, float, float], None]] = None) -> Tuple[float, float]:
        """查找最佳对焦位置
        
        Args:
            z_positions: Z轴位置列表 (微米)
            get_image_at_z: 获取指定Z位置图像的回调函数
            progress_callback: 进度回调函数
            
        Returns:
            Tuple[float, float]: (最佳Z位置, 对应的对焦评分)
        """
        # 清除缓存
        self.clear_cache()
        
        scores = []
        best_score = -float('inf')
        best_z = 0
        
        total_positions = len(z_positions)
        
        # 检查总位置数
        if total_positions == 0:
            return (0, 0)
            
        # 用于计时的变量
        start_time = time.time()
        images_processed = 0
                
        # 遍历每个Z位置并评估对焦情况
        for i, z in enumerate(z_positions):
            image = get_image_at_z(z)
            if image is None:
                scores.append(0)
                continue
                
            # 使用Z位置作为缓存键
            score = self.calculate_focus_score(image, z)
            scores.append(score)
            
            images_processed += 1
            
            # 更新最佳位置
            if score > best_score:
                best_score = score
                best_z = z
                
            # 计算进度并回调
            if progress_callback:
                progress = (i + 1) / total_positions * 100
                progress_callback(progress, z, score)
                
            # 性能统计
            if images_processed > 0 and (time.time() - start_time) > 2:
                elapsed = time.time() - start_time
                fps = images_processed / elapsed
                print(f"处理速度: {fps:.2f} 图像/秒")
                images_processed = 0
                start_time = time.time()
                
        return (best_z, best_score)
        
    def focus_hill_climbing(self, 
                           get_current_z: Callable[[], float],
                           move_to_z: Callable[[float], None],
                           get_image: Callable[[], QImage],
                           step_size: float = 10.0,
                           min_step: float = 0.5,
                           max_steps: int = 50,
                           progress_callback: Optional[Callable[[float, float, float], None]] = None) -> Tuple[float, float]:
        """使用爬山算法进行快速对焦
        
        Args:
            get_current_z: 获取当前Z位置的回调函数
            move_to_z: 移动到指定Z位置的回调函数
            get_image: 获取当前图像的回调函数
            step_size: 初始步长(微米)
            min_step: 最小步长(微米)
            max_steps: 最大步数
            progress_callback: 进度回调函数
            
        Returns:
            Tuple[float, float]: (最佳Z位置, 对应的对焦评分)
        """
        # 清除缓存
        self.clear_cache()
        
        # 获取当前位置和评分
        current_z = get_current_z()
        current_image = get_image()
        current_score = self.calculate_focus_score(current_image, current_z)
        
        best_z = current_z
        best_score = current_score
        
        # 初始方向 (上下各尝试一次，选择更好的方向)
        direction = 0  # 0=未定, 1=向上, -1=向下
        steps_taken = 0
        current_step_size = step_size
        
        while steps_taken < max_steps and current_step_size >= min_step:
            steps_taken += 1
            
            # 如果方向未确定，尝试两个方向
            if direction == 0:
                # 尝试向上
                test_z_up = current_z + current_step_size
                move_to_z(test_z_up)
                up_image = get_image()
                up_score = self.calculate_focus_score(up_image, test_z_up)
                
                # 尝试向下
                test_z_down = current_z - current_step_size
                move_to_z(test_z_down)
                down_image = get_image()
                down_score = self.calculate_focus_score(down_image, test_z_down)
                
                # 确定方向
                if up_score > current_score and up_score >= down_score:
                    direction = 1
                    current_z = test_z_up
                    current_score = up_score
                elif down_score > current_score and down_score > up_score:
                    direction = -1
                    current_z = test_z_down
                    current_score = down_score
                else:
                    # 如果两个方向都不好，减小步长
                    direction = 0
                    current_step_size /= 2
                    # 回到原位置
                    move_to_z(current_z)
            else:
                # 按照已确定的方向移动
                test_z = current_z + direction * current_step_size
                move_to_z(test_z)
                test_image = get_image()
                test_score = self.calculate_focus_score(test_image, test_z)
                
                # 如果评分提高，继续同方向
                if test_score > current_score:
                    current_z = test_z
                    current_score = test_score
                else:
                    # 方向反转
                    direction = -direction
                    # 步长减半
                    current_step_size /= 2
                    # 回到上次位置
                    move_to_z(current_z)
                    
            # 更新最佳位置
            if current_score > best_score:
                best_z = current_z
                best_score = current_score
                
            # 计算进度并回调
            if progress_callback:
                progress = (steps_taken / max_steps) * 100
                progress_callback(progress, current_z, current_score)
                
        # 确保返回最佳位置
        if best_score > 0:
            move_to_z(best_z)
            
        return (best_z, best_score) 

    def _qimage_to_opencv(self, qimg: QImage) -> np.ndarray:
        """将QImage转换为OpenCV图像格式
        
        Args:
            qimg: QImage图像
            
        Returns:
            numpy.ndarray: OpenCV格式图像
        """
        if qimg is None:
            return None
            
        # 获取图像尺寸
        width = qimg.width()
        height = qimg.height()
        
        # 不同的转换方法，避免使用过时的sip.voidptr API
        if qimg.format() == QImage.Format_RGB32 or qimg.format() == QImage.Format_ARGB32:
            # 对于32位格式，需要处理RGBA或BGRA格式
            bytes_per_line = qimg.bytesPerLine()
            ptr = qimg.constBits()
            
            # 创建numpy数组
            buffer = memoryview(ptr).tobytes()
            # 重新构造为numpy数组
            arr = np.frombuffer(buffer, dtype=np.uint8).reshape(height, bytes_per_line // 4, 4)
            # 只保留前3个通道 (去掉Alpha通道)
            return arr[:, :width, :3].copy()
            
        elif qimg.format() == QImage.Format_RGB888:
            # 对于RGB888格式直接转换
            bytes_per_line = qimg.bytesPerLine()
            ptr = qimg.constBits()
            
            buffer = memoryview(ptr).tobytes()
            arr = np.frombuffer(buffer, dtype=np.uint8).reshape(height, bytes_per_line // 3, 3)
            return arr[:, :width, :].copy()
            
        elif qimg.format() == QImage.Format_BGR888:
            # BGR格式，OpenCV默认格式
            bytes_per_line = qimg.bytesPerLine()
            ptr = qimg.constBits()
            
            buffer = memoryview(ptr).tobytes()
            arr = np.frombuffer(buffer, dtype=np.uint8).reshape(height, bytes_per_line // 3, 3)
            return arr[:, :width, :].copy()
            
        elif qimg.format() == QImage.Format_Grayscale8:
            # 灰度图像
            bytes_per_line = qimg.bytesPerLine()
            ptr = qimg.constBits()
            
            buffer = memoryview(ptr).tobytes()
            arr = np.frombuffer(buffer, dtype=np.uint8).reshape(height, bytes_per_line)
            return arr[:, :width].copy()
        else:
            # 其他格式：先转换为RGB888，再进行处理
            rgb_img = qimg.convertToFormat(QImage.Format_RGB888)
            bytes_per_line = rgb_img.bytesPerLine()
            ptr = rgb_img.constBits()
            
            buffer = memoryview(ptr).tobytes()
            arr = np.frombuffer(buffer, dtype=np.uint8).reshape(height, bytes_per_line // 3, 3)
            return arr[:, :width, :].copy()

class MultiModelAutoFocus:
    """组合多种对焦算法和模型的综合自动对焦类"""
    
    def __init__(self, use_gpu: bool = True):
        """初始化多模型自动对焦
        
        Args:
            use_gpu: 是否使用GPU加速
        """
        # 初始化PyTorch自动对焦引擎
        self.pytorch_focus = PyTorchAutoFocus(use_gpu=use_gpu)
        
        # 对焦速度与精度的权衡系数 (越大越精确但越慢)
        self.accuracy_factor = 0.7  # 默认偏向精度
        
        # 对焦算法特性映射 (速度、精度评分）
        self.method_profiles = {
            "laplacian": {"speed": 0.8, "accuracy": 0.7},
            "sobel": {"speed": 0.9, "accuracy": 0.6},
            "tenengrad": {"speed": 0.7, "accuracy": 0.7},
            "variance": {"speed": 0.95, "accuracy": 0.4},
            "fft": {"speed": 0.5, "accuracy": 0.8},
            "opencv_laplacian": {"speed": 0.9, "accuracy": 0.7},
            "opencv_canny": {"speed": 0.8, "accuracy": 0.7},
            "opencv_dof": {"speed": 0.7, "accuracy": 0.85},
            "efficientnet": {"speed": 0.3, "accuracy": 0.9},
            "opencv_phase_corr": {"speed": 0.7, "accuracy": 0.8},
            "opencv_fft_sharpness": {"speed": 0.6, "accuracy": 0.85},
            "opencv_smd": {"speed": 0.85, "accuracy": 0.8},
            "opencv_energy_gradient": {"speed": 0.6, "accuracy": 0.9},
            # 新增算法的速度和精度评分
            "opencv_local_variance": {"speed": 0.9, "accuracy": 0.75},
            "opencv_entropy": {"speed": 0.85, "accuracy": 0.7},
            "opencv_tenengrad": {"speed": 0.85, "accuracy": 0.8},
            "opencv_brenner_gradient": {"speed": 0.95, "accuracy": 0.7},
            "opencv_sobel_variance": {"speed": 0.85, "accuracy": 0.8},
            "opencv_laplacian_var": {"speed": 0.9, "accuracy": 0.75}
        }
        
        # 默认激活方法 - 增加新的OpenCV方法
        self.active_methods = ["laplacian", "opencv_dof", "opencv_smd", "opencv_tenengrad", "opencv_laplacian_var"]
    
    def set_accuracy_factor(self, factor: float):
        """设置精度因子 (0.0-1.0)，越高越精确但越慢
        
        Args:
            factor: 精度因子，范围0.0-1.0
        """
        self.accuracy_factor = max(0.0, min(1.0, factor))
        
    def set_active_methods(self, methods: List[str]):
        """设置要使用的活跃对焦方法
        
        Args:
            methods: 方法名列表
        """
        valid_methods = []
        for method in methods:
            if method in self.method_profiles:
                valid_methods.append(method)
        
        if valid_methods:
            self.active_methods = valid_methods
        else:
            print("没有有效的对焦方法，使用默认方法")
            self.active_methods = ["laplacian", "opencv_dof"]
            
    def select_best_method(self, speed_priority: bool = False):
        """根据当前环境和需求选择最佳对焦方法
        
        Args:
            speed_priority: 是否优先考虑速度而非精度
            
        Returns:
            str: 选定的对焦方法名称
        """
        # 计算每个方法的综合评分
        method_scores = {}
        
        # 如果优先速度，调整权重
        speed_weight = 0.7 if speed_priority else 0.3
        accuracy_weight = 1.0 - speed_weight
        
        for method in self.active_methods:
            if method in self.method_profiles:
                profile = self.method_profiles[method]
                # 综合评分计算
                score = (profile["speed"] * speed_weight + 
                         profile["accuracy"] * accuracy_weight)
                method_scores[method] = score
        
        # 选择评分最高的方法
        if method_scores:
            best_method = max(method_scores.items(), key=lambda x: x[1])[0]
            return best_method
        else:
            # 默认回退方法
            return "laplacian"
            
    def calculate_focus_score(self, image: QImage, method: Optional[str] = None) -> float:
        """计算图像的对焦评分
        
        Args:
            image: QImage格式图像
            method: 可选的指定方法，如果为None则使用智能选择的方法
            
        Returns:
            float: 对焦评分
        """
        if method is None:
            method = self.select_best_method()
            
        # 设置选择的方法
        if self.pytorch_focus.set_focus_method(method):
            # 使用选定的方法计算对焦评分
            return self.pytorch_focus.calculate_focus_score(image)
        else:
            # 如果方法不可用，使用默认方法
            self.pytorch_focus.set_focus_method("laplacian")
            return self.pytorch_focus.calculate_focus_score(image)
            
    def find_best_focus(self, z_positions: List[float], 
                         get_image_at_z: Callable[[float], QImage],
                         progress_callback: Optional[Callable[[float, float, float], None]] = None,
                         speed_priority: bool = False) -> Tuple[float, float]:
        """查找最佳对焦位置
        
        Args:
            z_positions: Z轴位置列表 (微米)
            get_image_at_z: 获取指定Z位置图像的回调函数
            progress_callback: 进度回调函数
            speed_priority: 是否优先考虑速度
            
        Returns:
            Tuple[float, float]: (最佳Z位置, 对应的对焦评分)
        """
        # 选择最佳方法
        best_method = self.select_best_method(speed_priority)
        self.pytorch_focus.set_focus_method(best_method)
        
        # 使用选定的方法寻找最佳对焦
        return self.pytorch_focus.find_best_focus(
            z_positions, get_image_at_z, progress_callback)
            
    def focus_hill_climbing(self, 
                           get_current_z: Callable[[], float],
                           move_to_z: Callable[[float], None],
                           get_image: Callable[[], QImage],
                           step_size: float = 10.0,
                           min_step: float = 0.5,
                           max_steps: int = 50,
                           progress_callback: Optional[Callable[[float, float, float], None]] = None,
                           speed_priority: bool = False) -> Tuple[float, float]:
        """使用爬山算法进行快速对焦
        
        Args:
            get_current_z: 获取当前Z位置的回调函数
            move_to_z: 移动到指定Z位置的回调函数
            get_image: 获取当前图像的回调函数
            step_size: 初始步长(微米)
            min_step: 最小步长(微米)
            max_steps: 最大步数
            progress_callback: 进度回调函数
            speed_priority: 是否优先考虑速度
            
        Returns:
            Tuple[float, float]: (最佳Z位置, 对应的对焦评分)
        """
        # 选择最佳方法
        best_method = self.select_best_method(speed_priority)
        self.pytorch_focus.set_focus_method(best_method)
        
        # 使用选定的方法进行爬山对焦
        return self.pytorch_focus.focus_hill_climbing(
            get_current_z, move_to_z, get_image, 
            step_size, min_step, max_steps, progress_callback)

