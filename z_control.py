#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Z轴控制界面 - 使用PySide6实现简单的Z轴电机控制
"""

import sys
import time
import numpy as np
import cv2
from typing import Optional, List, Tuple

from PySide6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QLabel, QPushButton, QDoubleSpinBox, QGroupBox, QGridLayout,
    QMessageBox, QProgressBar, QSlider, QComboBox, QTabWidget,
    QCheckBox, QRadioButton, QButtonGroup, QSplitter, QSizePolicy
)
from PySide6.QtCore import Qt, QThread, Signal, Slot, QTimer, QSize
from PySide6.QtGui import QImage, QPixmap


from DBDynamics import BeeS
from Camera import Tucam
import torch  # 添加PyTorch导入
from Autofocus import PyTorchAutoFocus, MultiModelAutoFocus  # 导入PyTorch自动对焦模块
MOTOR_LIBRARY_AVAILABLE = True


# 常量定义
MAX_Z_MICRON = 10000 # 最大Z轴行程 (μm)
PULSES_PER_MICRON_Z = 51200 / 100 # Z轴每微米对应的脉冲数，51200脉冲=100微米

# Z轴电机ID
Z_MOTOR_ID = 2

class ZMotorWorker(QThread):
    """Z轴电机工作线程"""
    positionUpdated = Signal(int)  # 位置更新信号（脉冲值）
    operationFinished = Signal(bool, str)  # 操作完成信号（成功与否，消息）
    progressUpdated = Signal(int)  # 进度更新信号
    
    def __init__(self, com_port: str, motor_id: int = Z_MOTOR_ID):
        super().__init__()
        self.com_port = com_port
        self.motor_id = motor_id
        self.motor = None
        self.target_position = 0
        self.operation = ""
        self.running = True
        self.simulation_mode = not MOTOR_LIBRARY_AVAILABLE
        self.current_position = 0  # 用于模拟模式
        
    def initialize(self):
        """初始化电机"""
        try:
            if self.simulation_mode:
                print(f"模拟模式: 初始化Z轴控制器 (端口: {self.com_port})")
                time.sleep(0.5)  # 模拟初始化延迟
                return True
                
            self.motor = BeeS(self.com_port)
            
            # 高级使能
            # 软件限位 无效-0 有效-1
            # 传感器限位 生效-0 无效-1
            # 报警 释放电机-0 自动恢复-1
            self.motor.setPowerOnPro(id=self.motor_id, limit_soft=0, limit_off=0, auto_recovery=0)
            
            # 设置为平滑位置模式
            self.motor.setPositionMode(self.motor_id)
            
            # 设置最大运行速度
            self.motor.setTargetVelocity(self.motor_id, 3000)
            
            # 设置加减速时间 单位ms
            self.motor.setAccTime(self.motor_id, 200)
            
            # 设置Z轴软限位
            z_max_pulse = int(MAX_Z_MICRON * PULSES_PER_MICRON_Z)
            self.motor.setLimitPositionN(self.motor_id, 0)  # 下限
            self.motor.setLimitPositionP(self.motor_id, z_max_pulse)  # 上限
            
            return True
        except Exception as e:
            print(f"初始化电机失败: {str(e)}")
            return False
    
    def run(self):
        """线程主方法"""
        if not self.initialize():
            self.operationFinished.emit(False, f"初始化电机失败: 无法连接到 {self.com_port}")
            return
            
        while self.running:
            if self.operation == "move_to":
                self._move_to_position()
                self.operation = ""
            elif self.operation == "step_up":
                self._step_move(True)
                self.operation = ""
            elif self.operation == "step_down":
                self._step_move(False)
                self.operation = ""
            elif self.operation == "home":
                self._home()
                self.operation = ""
            elif self.operation == "center":
                self._center()
                self.operation = ""
            else:
                # 更新当前位置
                try:
                    if self.simulation_mode:
                        position = self.current_position
                    else:
                        position = self.motor.getActualPosition(self.motor_id)
                        
                    if position is not None:
                        self.positionUpdated.emit(position)
                except Exception:
                    pass
                time.sleep(0.1)
    
    def stop(self):
        """停止线程"""
        self.running = False
        if not self.simulation_mode and self.motor:
            try:
                self.motor.stop()
            except Exception:
                pass
        self.wait()
    
    def move_to_position(self, position: int):
        """移动到指定位置"""
        self.target_position = position
        self.operation = "move_to"
    
    def step_move(self, up: bool, step_size: int):
        """相对移动"""
        self.target_position = step_size
        if up:
            self.operation = "step_up"
        else:
            self.operation = "step_down"
    
    def home(self):
        """回零"""
        self.operation = "home"
    
    def center(self):
        """移动到中心位置"""
        self.operation = "center"
    
    def _move_to_position(self):
        """移动到目标位置的内部实现"""
        try:
            if self.simulation_mode:
                current_pos = self.current_position
            else:
                current_pos = self.motor.getActualPosition(self.motor_id)
                
            if current_pos is None:
                self.operationFinished.emit(False, "无法获取当前位置")
                return
                
            if self.simulation_mode:
                # 模拟模式下的移动
                start_pos = current_pos
                total_distance = abs(self.target_position - start_pos)
                
                # 模拟移动过程
                if total_distance == 0:
                    self.progressUpdated.emit(100)
                    self.operationFinished.emit(True, "已在目标位置")
                    return
                
                direction = 1 if self.target_position > start_pos else -1
                steps = min(100, total_distance)  # 最多100步
                
                for i in range(1, steps + 1):
                    time.sleep(0.02)  # 模拟延迟
                    progress = (i / steps) * 100
                    self.progressUpdated.emit(int(progress))
                    
                    # 更新当前位置
                    moved = (i / steps) * total_distance
                    self.current_position = start_pos + (direction * moved)
                    self.positionUpdated.emit(int(self.current_position))
                
                # 设置最终位置
                self.current_position = self.target_position
                self.positionUpdated.emit(self.target_position)
                self.operationFinished.emit(True, "移动完成")
            else:
                # 实际硬件模式
                self.motor.setTargetPosition(self.motor_id, self.target_position)
                
                # 等待到位，同时更新进度
                start_pos = current_pos
                total_distance = abs(self.target_position - start_pos)
                if total_distance == 0:
                    self.progressUpdated.emit(100)
                    self.operationFinished.emit(True, "已在目标位置")
                    return
                    
                while True:
                    current_pos = self.motor.getActualPosition(self.motor_id)
                    if current_pos is None:
                        break
                        
                    self.positionUpdated.emit(current_pos)
                    
                    # 计算进度
                    if total_distance > 0:
                        progress = min(100, int(abs(current_pos - start_pos) / total_distance * 100))
                        self.progressUpdated.emit(progress)
                    
                    # 检查是否到位
                    if abs(current_pos - self.target_position) < 100:  # 允许100脉冲的误差
                        break
                        
                    time.sleep(0.1)
                    
                self.motor.waitTargetPositionReached(self.motor_id)
                self.progressUpdated.emit(100)
                self.operationFinished.emit(True, "移动完成")
        except Exception as e:
            self.operationFinished.emit(False, f"移动失败: {str(e)}")
    
    def _step_move(self, up: bool):
        """相对移动的内部实现"""
        try:
            if self.simulation_mode:
                current_pos = self.current_position
            else:
                current_pos = self.motor.getActualPosition(self.motor_id)
                
            if current_pos is None:
                self.operationFinished.emit(False, "无法获取当前位置")
                return
                
            step = self.target_position
            # 反转方向逻辑：up=True时应该减少脉冲值（载物台向上）
            if up:
                step = -step
            # 如果down (up=False)，则为正值，载物台向下移动
                
            target = current_pos + step
            
            # 检查目标位置是否在限位范围内
            z_max_pulse = int(MAX_Z_MICRON * PULSES_PER_MICRON_Z)
            if not (0 <= target <= z_max_pulse):
                self.operationFinished.emit(False, f"目标位置 {target} 超出范围 [0, {z_max_pulse}]")
                return
            
            if self.simulation_mode:
                # 模拟模式下的步进
                self.current_position = target
                self.positionUpdated.emit(target)
                
                # 延迟一小段时间模拟运动
                time.sleep(0.5)
                
                # 更新状态
                direction = "上" if up else "下"
                self.operationFinished.emit(True, f"向{direction}步进移动完成")
            else:
                # 实际硬件模式
                self.motor.setTargetPosition(self.motor_id, target)
                self.motor.waitTargetPositionReached(self.motor_id)
                
                position = self.motor.getActualPosition(self.motor_id)
                if position is not None:
                    self.positionUpdated.emit(position)
                    
                # 更新状态消息，显示正确的方向
                direction = "上" if up else "下"
                self.operationFinished.emit(True, f"向{direction}步进移动完成")
        except Exception as e:
            self.operationFinished.emit(False, f"步进移动失败: {str(e)}")
    
    def _home(self):
        """回零操作的内部实现"""
        try:
            if self.simulation_mode:
                # 模拟回零
                time.sleep(1)  # 模拟延迟
                self.current_position = 0
                self.positionUpdated.emit(0)
                self.operationFinished.emit(True, "回零完成")
            else:
                # 实际硬件回零
                self.motor.setTargetPosition(self.motor_id, 0)
                self.motor.waitTargetPositionReached(self.motor_id)
                self.positionUpdated.emit(0)
                self.operationFinished.emit(True, "回零完成")
        except Exception as e:
            self.operationFinished.emit(False, f"回零失败: {str(e)}")
            
    def _center(self):
        """移动到中心位置的内部实现"""
        try:
            # 计算中心位置
            center_micron = MAX_Z_MICRON / 2  
            center_pulse = int(center_micron * PULSES_PER_MICRON_Z)
            
            if self.simulation_mode:
                # 模拟居中
                self.current_position = center_pulse
                self.positionUpdated.emit(center_pulse)
                time.sleep(1)  # 模拟延迟
                self.operationFinished.emit(True, "已移动到中心位置")
            else:
                # 实际硬件居中
                self.motor.setTargetPosition(self.motor_id, center_pulse)
                self.motor.waitTargetPositionReached(self.motor_id)
                self.positionUpdated.emit(center_pulse)
                self.operationFinished.emit(True, "已移动到中心位置")
        except Exception as e:
            self.operationFinished.emit(False, f"移动到中心位置失败: {str(e)}")

class AutoFocus:
    """自动对焦类，使用Autofocus.py提供的高效算法实现自动对焦"""
    
    def __init__(self, z_motor: ZMotorWorker, camera: Tucam):
        """初始化自动对焦
        
        Args:
            z_motor: Z轴电机控制器
            camera: 相机对象
        """
        self.z_motor = z_motor
        self.camera = camera
        
        # 初始化多模型自动对焦引擎
        try:
            self.use_gpu = torch.cuda.is_available()
            # 使用MultiModelAutoFocus，它可以根据情况选择最佳的对焦方法
            self.focus_engine = MultiModelAutoFocus(use_gpu=self.use_gpu)
            print(f"自动对焦引擎初始化完成，GPU加速: {self.use_gpu}")
        except Exception as e:
            print(f"高级对焦引擎初始化错误: {str(e)}，将使用基础模式")
            self.use_gpu = False
            try:
                # 回退到纯PyTorch模式
                self.focus_engine = PyTorchAutoFocus(use_gpu=False)
                print("PyTorch自动对焦引擎已降级到CPU模式")
            except Exception as e2:
                print(f"PyTorch初始化也失败: {str(e2)}，对焦功能可能无法正常工作")
                self.focus_engine = None
        
        # 对焦参数
        self.focus_method = "laplacian"  # 默认对焦算法
        self.coarse_range_micron = 2000  # 粗略扫描范围(微米)
        self.coarse_step_micron = 200   # 粗略扫描步长(微米)
        self.medium_range_micron = 200  # 中等扫描范围(微米)
        self.medium_step_micron = 10    # 中等扫描步长(微米)
        self.fine_range_micron = 10     # 精细扫描范围(微米)
        self.fine_step_micron = 0.5     # 精细扫描步长(微米)
        
        # 对焦状态
        self.is_focusing = False
        self.abort_focusing = False
        self.focus_level = ""  # 当前对焦级别: "coarse", "medium", "fine"
        self.auto_next_level = False  # 是否自动进入下一级别对焦
        self.auto_goto_best = False   # 完成后是否自动移动到最佳位置
        self.moving_to_best = False   # 是否正在移动到最佳位置
        self.use_hill_climbing = False  # 是否使用爬山算法
        
        # 对焦结果
        self.focus_points = []  # 存储(z位置, 对焦分数)对
        self.best_z_micron = 0.0  # 最佳Z位置(微米)
        self.best_score = 0.0     # 最佳对焦分数
        
        # 注册电机完成操作回调
        self.z_motor.operationFinished.connect(self._on_motor_operation_finished)
        
        # 当前扫描位置索引
        self.current_scan_index = 0
        self.scan_positions = []
        
        # 对焦完成回调
        self.on_focus_completed = None
        self.on_focus_progress = None
    
    def set_focus_method(self, method: str):
        """设置对焦评估算法
        
        Args:
            method: 对焦算法，可用算法取决于初始化的对焦引擎
        """
        if self.focus_engine is None:
            print(f"对焦引擎未初始化，无法设置对焦方法")
            return False
            
        if isinstance(self.focus_engine, MultiModelAutoFocus):
            # 如果是多模型引擎，设置活跃方法列表
            self.focus_engine.set_active_methods([method])
            self.focus_method = method
            print(f"对焦算法已设置为: {method} (多模型模式)")
            return True
        elif isinstance(self.focus_engine, PyTorchAutoFocus):
            # 如果是PyTorch引擎，直接设置方法
            if self.focus_engine.set_focus_method(method):
                self.focus_method = method
                print(f"对焦算法已设置为: {method}")
                return True
        
        print(f"不支持的对焦算法: {method}，使用默认的拉普拉斯法")
        self.focus_method = "laplacian"
        if isinstance(self.focus_engine, PyTorchAutoFocus):
            self.focus_engine.set_focus_method("laplacian")
        return False
    
    def set_coarse_scan_params(self, range_micron: float, step_micron: float):
        """设置粗略扫描参数
        
        Args:
            range_micron: 扫描范围(微米)
            step_micron: 扫描步长(微米)
        """
        self.coarse_range_micron = max(100.0, min(range_micron, MAX_Z_MICRON))
        self.coarse_step_micron = max(1.0, min(step_micron, 500.0))
    
    def set_fine_scan_params(self, range_micron: float, step_micron: float):
        """设置精细扫描参数
        
        Args:
            range_micron: 扫描范围(微米)
            step_micron: 扫描步长(微米)
        """
        self.fine_range_micron = max(10.0, min(range_micron, 1000.0))
        self.fine_step_micron = max(0.5, min(step_micron, 50.0))
    
    def set_medium_scan_params(self, range_micron: float, step_micron: float):
        """设置中等扫描参数
        
        Args:
            range_micron: 扫描范围(微米)
            step_micron: 扫描步长(微米)
        """
        self.medium_range_micron = max(50.0, min(range_micron, 1000.0))
        self.medium_step_micron = max(1.0, min(step_micron, 50.0))
    
    def calculate_focus_score(self, image: QImage) -> float:
        """计算图像的对焦评分
        
        Args:
            image: QImage格式图像
        
        Returns:
            float: 对焦评分，值越高表示对焦越好
        """
        if image is None:
            return 0.0
            
        try:
            # 使用导入的对焦引擎计算对焦评分
            if self.focus_engine is not None:
                return self.focus_engine.calculate_focus_score(image)
            
            # 如果对焦引擎不可用，返回0
            return 0.0
            
        except Exception as e:
            print(f"对焦评分计算错误: {str(e)}")
            return 0.0
    
    def _qimage_to_opencv(self, qimg: QImage) -> np.ndarray:
        """将QImage转换为OpenCV图像格式 (用于PyTorch不可用时的回退)
        
        Args:
            qimg: QImage图像
            
        Returns:
            numpy.ndarray: OpenCV格式图像
        """
        if qimg is None:
            return None
            
        # 获取图像尺寸
        width = qimg.width()
        height = qimg.height()
        
        # 不同的转换方法，避免使用setsize
        if qimg.format() == QImage.Format_RGB32 or qimg.format() == QImage.Format_ARGB32:
            # 对于32位格式，需要处理RGBA或BGRA格式
            bytes_per_line = qimg.bytesPerLine()
            ptr = qimg.constBits()
            
            # 创建numpy数组，不使用setsize
            buffer = memoryview(ptr).tobytes()
            # 重新构造为numpy数组
            arr = np.frombuffer(buffer, dtype=np.uint8).reshape(height, bytes_per_line // 4, 4)
            # 只保留前3个通道 (去掉Alpha通道)
            return arr[:, :width, :3].copy()
            
        elif qimg.format() == QImage.Format_RGB888:
            # 对于RGB888格式直接转换
            bytes_per_line = qimg.bytesPerLine()
            ptr = qimg.constBits()
            
            buffer = memoryview(ptr).tobytes()
            arr = np.frombuffer(buffer, dtype=np.uint8).reshape(height, bytes_per_line // 3, 3)
            return arr[:, :width, :].copy()
            
        elif qimg.format() == QImage.Format_BGR888:
            # BGR格式，OpenCV默认格式
            bytes_per_line = qimg.bytesPerLine()
            ptr = qimg.constBits()
            
            buffer = memoryview(ptr).tobytes()
            arr = np.frombuffer(buffer, dtype=np.uint8).reshape(height, bytes_per_line // 3, 3)
            return arr[:, :width, :].copy()
            
        elif qimg.format() == QImage.Format_Grayscale8:
            # 灰度图像
            bytes_per_line = qimg.bytesPerLine()
            ptr = qimg.constBits()
            
            buffer = memoryview(ptr).tobytes()
            arr = np.frombuffer(buffer, dtype=np.uint8).reshape(height, bytes_per_line)
            return arr[:, :width].copy()
        else:
            # 其他格式：先转换为RGB888，再进行处理
            rgb_img = qimg.convertToFormat(QImage.Format_RGB888)
            bytes_per_line = rgb_img.bytesPerLine()
            ptr = rgb_img.constBits()
            
            buffer = memoryview(ptr).tobytes()
            arr = np.frombuffer(buffer, dtype=np.uint8).reshape(height, bytes_per_line // 3, 3)
            return arr[:, :width, :].copy()
    
    def _pulse_to_micron(self, pulse: int) -> float:
        """将电机脉冲值转换为微米
        
        Args:
            pulse: 脉冲值
            
        Returns:
            float: 微米值
        """
        return pulse / PULSES_PER_MICRON_Z
    
    def _micron_to_pulse(self, micron: float) -> int:
        """将微米转换为电机脉冲值
        
        Args:
            micron: 微米值
            
        Returns:
            int: 脉冲值
        """
        return int(micron * PULSES_PER_MICRON_Z)
    
    def get_current_z_micron(self) -> float:
        """获取当前Z轴位置(微米)
        
        Returns:
            float: Z轴位置(微米)
        """
        if self.z_motor.simulation_mode:
            pulse = self.z_motor.current_position
        else:
            try:
                pulse = self.z_motor.motor.getActualPosition(self.z_motor.motor_id)
            except:
                pulse = 0
                
        if pulse is None:
            return 0.0
            
        return self._pulse_to_micron(pulse)
    
    def start_coarse_focus(self, auto_continue=False, auto_goto_best=False, use_hill_climbing=False):
        """开始粗略对焦
        
        Args:
            auto_continue: 是否自动进入下一级别对焦
            auto_goto_best: 完成后是否自动移动到最佳位置
            use_hill_climbing: 是否使用爬山算法代替全范围扫描
        """
        if self.is_focusing:
            print("自动对焦已在进行中")
            return False
            
        if self.focus_engine is None:
            print("对焦引擎未初始化，无法进行自动对焦")
            return False
        
        self.is_focusing = True
        self.abort_focusing = False
        self.focus_points = []
        self.focus_level = "coarse"
        self.auto_next_level = auto_continue
        self.auto_goto_best = auto_goto_best
        self.use_hill_climbing = use_hill_climbing
        
        # 获取当前Z位置
        current_z_micron = self.get_current_z_micron()
        
        # 如果使用爬山算法，则直接启动爬山对焦
        if use_hill_climbing:
            print(f"使用爬山算法进行粗略对焦，当前位置: {current_z_micron:.2f} μm")
            self._start_hill_climbing(initial_step=self.coarse_step_micron)
            return True
        
        # 计算扫描范围
        half_range = self.coarse_range_micron / 2
        z_min = max(0, current_z_micron - half_range)
        z_max = min(MAX_Z_MICRON, current_z_micron + half_range)
        
        # 创建扫描位置列表
        try:
            self.scan_positions = np.arange(z_min, z_max + self.coarse_step_micron, 
                                            self.coarse_step_micron).tolist()
        except Exception as e:
            print(f"创建扫描位置列表失败: {str(e)}")
            self.is_focusing = False
            return False
            
        self.current_scan_index = 0
        
        # 如果没有有效的扫描位置，则退出
        if not self.scan_positions:
            self.is_focusing = False
            print("没有有效的扫描位置")
            return False
            
        print(f"开始粗略对焦，扫描范围: {z_min:.2f} - {z_max:.2f} μm, 步长: {self.coarse_step_micron:.2f} μm")
        print(f"扫描位置数量: {len(self.scan_positions)}")
        
        # 开始扫描第一个位置
        self._move_to_next_position()
        return True
    
    def start_medium_focus(self, auto_continue=False, auto_goto_best=False, use_hill_climbing=False):
        """开始中等精度对焦
        
        Args:
            auto_continue: 是否自动进入下一级别对焦
            auto_goto_best: 完成后是否自动移动到最佳位置
            use_hill_climbing: 是否使用爬山算法代替全范围扫描
        """
        if self.is_focusing:
            print("自动对焦已在进行中")
            return False
            
        if self.focus_engine is None:
            print("对焦引擎未初始化，无法进行自动对焦")
            return False
        
        self.is_focusing = True
        self.abort_focusing = False
        self.focus_points = []
        self.focus_level = "medium"
        self.auto_next_level = auto_continue
        self.auto_goto_best = auto_goto_best
        self.use_hill_climbing = use_hill_climbing
        
        # 确定扫描中心位置
        if self.best_z_micron > 0:
            # 使用之前找到的最佳位置作为中心
            center_z = self.best_z_micron
        else:
            # 使用当前位置作为中心
            center_z = self.get_current_z_micron()
            
        # 如果使用爬山算法，则直接启动爬山对焦
        if use_hill_climbing:
            print(f"使用爬山算法进行中等精度对焦，当前位置: {center_z:.2f} μm")
            # 首先移动到中心位置
            target_pulse = self._micron_to_pulse(center_z)
            self.z_motor.move_to_position(target_pulse)
            # 稍后在电机操作完成后启动爬山对焦
            self.pending_hill_climbing = True
            self.hill_climbing_step = self.medium_step_micron
            return True
        
        # 计算中等扫描范围
        half_range = self.medium_range_micron / 2
        z_min = max(0, center_z - half_range)
        z_max = min(MAX_Z_MICRON, center_z + half_range)
        
        # 创建扫描位置列表
        try:
            self.scan_positions = np.arange(z_min, z_max + self.medium_step_micron, 
                                            self.medium_step_micron).tolist()
        except Exception as e:
            print(f"创建扫描位置列表失败: {str(e)}")
            self.is_focusing = False
            return False
            
        self.current_scan_index = 0
        
        # 如果没有有效的扫描位置，则退出
        if not self.scan_positions:
            self.is_focusing = False
            print("没有有效的扫描位置")
            return False
            
        print(f"开始中等精度对焦，扫描范围: {z_min:.2f} - {z_max:.2f} μm, 步长: {self.medium_step_micron:.2f} μm")
        print(f"扫描位置数量: {len(self.scan_positions)}")
        
        # 开始扫描第一个位置
        self._move_to_next_position()
        return True
    
    def start_fine_focus(self, auto_goto_best=True, use_hill_climbing=False):
        """开始精细对焦
        
        Args:
            auto_goto_best: 完成后是否自动移动到最佳位置
            use_hill_climbing: 是否使用爬山算法代替全范围扫描
        """
        if self.is_focusing:
            print("自动对焦已在进行中")
            return False
            
        if self.focus_engine is None:
            print("对焦引擎未初始化，无法进行自动对焦")
            return False
        
        self.is_focusing = True
        self.abort_focusing = False
        self.focus_points = []
        self.focus_level = "fine"
        self.auto_next_level = False  # 精细对焦是最后一级
        self.auto_goto_best = auto_goto_best
        self.use_hill_climbing = use_hill_climbing
        
        # 确定扫描中心位置
        if self.best_z_micron > 0:
            # 使用之前找到的最佳位置作为中心
            center_z = self.best_z_micron
        else:
            # 使用当前位置作为中心
            center_z = self.get_current_z_micron()
            
        # 如果使用爬山算法，则直接启动爬山对焦
        if use_hill_climbing:
            print(f"使用爬山算法进行精细对焦，当前位置: {center_z:.2f} μm")
            # 首先移动到中心位置
            target_pulse = self._micron_to_pulse(center_z)
            self.z_motor.move_to_position(target_pulse)
            # 稍后在电机操作完成后启动爬山对焦
            self.pending_hill_climbing = True
            self.hill_climbing_step = self.fine_step_micron
            return True
        
        # 计算精细扫描范围
        half_range = self.fine_range_micron / 2
        z_min = max(0, center_z - half_range)
        z_max = min(MAX_Z_MICRON, center_z + half_range)
        
        # 创建扫描位置列表
        try:
            self.scan_positions = np.arange(z_min, z_max + self.fine_step_micron, 
                                            self.fine_step_micron).tolist()
        except Exception as e:
            print(f"创建扫描位置列表失败: {str(e)}")
            self.is_focusing = False
            return False
            
        self.current_scan_index = 0
        
        # 如果没有有效的扫描位置，则退出
        if not self.scan_positions:
            self.is_focusing = False
            print("没有有效的扫描位置")
            return False
            
        print(f"开始精细对焦，扫描范围: {z_min:.2f} - {z_max:.2f} μm, 步长: {self.fine_step_micron:.2f} μm")
        print(f"扫描位置数量: {len(self.scan_positions)}")
        
        # 开始扫描第一个位置
        self._move_to_next_position()
        return True
    
    def _start_hill_climbing(self, initial_step=10.0):
        """启动爬山算法对焦
        
        Args:
            initial_step: 初始步长(微米)
        """
        # 清除标记
        if hasattr(self, 'pending_hill_climbing'):
            self.pending_hill_climbing = False
        
        if self.focus_engine is None:
            print("对焦引擎未初始化，无法使用爬山算法")
            self._finish_focus_scan()
            return
        
        # 定义回调函数，用于处理爬山算法的进度更新
        def progress_callback(progress, z_position, score):
            if self.on_focus_progress:
                self.on_focus_progress(progress, z_position, score)
            
        # 定义获取当前Z位置、移动到指定Z位置和获取图像的回调函数
        def get_current_z():
            return self.get_current_z_micron()
            
        def move_to_z(z_micron):
            target_pulse = self._micron_to_pulse(z_micron)
            self.z_motor.move_to_position(target_pulse)
            # 注意：这里需要等待电机操作完成
            # 这是通过继续正常的hill climbing流程来处理的
            
        def get_image():
            return self.camera.get_frame()
            
        # 记录初始位置，以便在算法进行过程中进行进度更新
        self.hill_climbing_initial_z = self.get_current_z_micron()
        self.hill_climbing_state = {
            'in_progress': True,
            'current_step': 0,
            'max_steps': 20,
            'current_z': self.hill_climbing_initial_z,
            'best_z': self.hill_climbing_initial_z,
            'best_score': 0.0
        }
        
        # 启动异步爬山对焦
        # 注意：由于我们需要等待电机移动完成，我们不能使用focus_engine.focus_hill_climbing方法
        # 而是需要实现我们自己的爬山算法逻辑，该逻辑将与电机操作完成事件集成
        
        # 初始化爬山步骤
        self.hill_climbing_params = {
            'current_z': self.get_current_z_micron(),
            'current_score': 0.0,
            'best_z': self.get_current_z_micron(),
            'best_score': 0.0,
            'direction': 0,  # 0=未定, 1=向上, -1=向下
            'step_size': initial_step,
            'min_step': initial_step / 20.0,
            'max_steps': 20,
            'steps_taken': 0,
            'state': 'init',  # 状态: init, test_up, test_down, move, done
            'test_up_score': 0.0,
            'test_down_score': 0.0
        }
        
        # 首先获取当前位置的评分
        try:
            frame = self.camera.get_frame()
            if frame is not None:
                current_score = self.calculate_focus_score(frame)
                self.hill_climbing_params['current_score'] = current_score
                self.hill_climbing_params['best_score'] = current_score
                
                # 开始第一次移动 - 尝试向上
                self.hill_climbing_params['state'] = 'test_up'
                test_z_up = self.hill_climbing_params['current_z'] + self.hill_climbing_params['step_size']
                target_pulse = self._micron_to_pulse(test_z_up)
                self.z_motor.move_to_position(target_pulse)
                
                # 进度更新
                if self.on_focus_progress:
                    self.on_focus_progress(5, self.hill_climbing_params['current_z'], current_score)
                    
                print(f"爬山对焦: 初始评分 {current_score:.2f} @ {self.hill_climbing_params['current_z']:.2f}μm，尝试向上移动...")
            else:
                print("获取相机图像失败，无法进行爬山对焦")
                self._finish_focus_scan()
        except Exception as e:
            print(f"爬山对焦初始化错误: {str(e)}")
            self._finish_focus_scan()
    
    def _continue_hill_climbing(self):
        """继续执行爬山算法对焦的下一步"""
        if not hasattr(self, 'hill_climbing_params'):
            print("爬山对焦参数不存在")
            self._finish_focus_scan()
            return
            
        if self.focus_engine is None:
            print("对焦引擎未初始化，无法继续爬山对焦")
            self._finish_focus_scan()
            return
            
        params = self.hill_climbing_params
        
        try:
            # 获取当前图像
            frame = self.camera.get_frame()
            if frame is None:
                print("获取相机图像失败")
                self._finish_focus_scan()
                return
                
            current_z = self.get_current_z_micron()
            
            # 根据当前状态执行相应操作
            if params['state'] == 'test_up':
                # 刚完成向上测试，记录评分
                score = self.calculate_focus_score(frame)
                params['test_up_score'] = score
                
                # 进度更新
                if self.on_focus_progress:
                    self.on_focus_progress(10, current_z, score)
                    
                print(f"爬山对焦: 向上测试评分 {score:.2f} @ {current_z:.2f}μm")
                
                # 移动到测试向下位置
                params['state'] = 'test_down'
                test_z_down = params['current_z'] - params['step_size']
                target_pulse = self._micron_to_pulse(test_z_down)
                self.z_motor.move_to_position(target_pulse)
                
            elif params['state'] == 'test_down':
                # 刚完成向下测试，记录评分
                score = self.calculate_focus_score(frame)
                params['test_down_score'] = score
                
                # 进度更新
                if self.on_focus_progress:
                    self.on_focus_progress(15, current_z, score)
                
                print(f"爬山对焦: 向下测试评分 {score:.2f} @ {current_z:.2f}μm")
                
                # 确定移动方向
                current_score = params['current_score']
                up_score = params['test_up_score']
                down_score = params['test_down_score']
                
                if up_score > current_score and up_score >= down_score:
                    # 向上移动
                    params['direction'] = 1
                    params['state'] = 'move'
                    test_z_up = params['current_z'] + params['step_size']
                    params['current_z'] = test_z_up
                    params['current_score'] = up_score
                    
                    if up_score > params['best_score']:
                        params['best_score'] = up_score
                        params['best_z'] = test_z_up
                        
                    target_pulse = self._micron_to_pulse(test_z_up)
                    self.z_motor.move_to_position(target_pulse)
                    print(f"爬山对焦: 选择向上方向，移动到 {test_z_up:.2f}μm")
                    
                elif down_score > current_score and down_score > up_score:
                    # 向下移动
                    params['direction'] = -1
                    params['state'] = 'move'
                    test_z_down = params['current_z'] - params['step_size']
                    params['current_z'] = test_z_down
                    params['current_score'] = down_score
                    
                    if down_score > params['best_score']:
                        params['best_score'] = down_score
                        params['best_z'] = test_z_down
                        
                    # 已经在下测试位置，无需移动
                    print(f"爬山对焦: 选择向下方向，当前位置 {test_z_down:.2f}μm")
                    self._continue_hill_climbing()  # 直接继续下一步
                    return
                    
                else:
                    # 两个方向都不好，减小步长
                    params['direction'] = 0
                    params['step_size'] /= 2
                    params['state'] = 'move'
                    
                    # 回到原位置
                    target_pulse = self._micron_to_pulse(params['current_z'])
                    self.z_motor.move_to_position(target_pulse)
                    print(f"爬山对焦: 两个方向都不好，减小步长至 {params['step_size']:.2f}μm，回到 {params['current_z']:.2f}μm")
                
            elif params['state'] == 'move':
                # 刚完成按确定方向移动
                params['steps_taken'] += 1
                score = self.calculate_focus_score(frame)
                
                # 进度更新 - 基于步数计算进度
                progress = min(95, 15 + 80 * params['steps_taken'] / params['max_steps'])
                if self.on_focus_progress:
                    self.on_focus_progress(progress, current_z, score)
                
                print(f"爬山对焦: 步骤 {params['steps_taken']}/{params['max_steps']} 评分 {score:.2f} @ {current_z:.2f}μm")
                
                # 检查是否需要继续
                if params['steps_taken'] >= params['max_steps'] or params['step_size'] < params['min_step']:
                    # 对焦完成，移动到最佳位置
                    params['state'] = 'done'
                    if params['best_score'] > 0:
                        target_pulse = self._micron_to_pulse(params['best_z'])
                        self.z_motor.move_to_position(target_pulse)
                        print(f"爬山对焦: 完成，移动到最佳位置 {params['best_z']:.2f}μm，评分 {params['best_score']:.2f}")
                    else:
                        self._finish_focus_scan()
                    return
                
                # 与当前得分比较，决定下一步操作
                if score > params['current_score']:
                    # 评分提高，继续同方向
                    params['current_score'] = score
                    
                    if score > params['best_score']:
                        params['best_score'] = score
                        params['best_z'] = current_z
                        
                    # 继续同方向移动
                    next_z = current_z + params['direction'] * params['step_size']
                    params['current_z'] = current_z  # 更新当前位置
                    target_pulse = self._micron_to_pulse(next_z)
                    self.z_motor.move_to_position(target_pulse)
                    print(f"爬山对焦: 评分提高，继续向{'上' if params['direction'] > 0 else '下'}移动到 {next_z:.2f}μm")
                    
                else:
                    # 评分降低，反转方向并减小步长
                    params['direction'] = -params['direction']
                    params['step_size'] /= 2
                    
                    # 移动到上一个位置
                    params['current_z'] = current_z  # 更新当前位置
                    target_pulse = self._micron_to_pulse(params['current_z'])
                    self.z_motor.move_to_position(target_pulse)
                    print(f"爬山对焦: 评分降低，反转方向，减小步长至 {params['step_size']:.2f}μm")
                
            elif params['state'] == 'done':
                # 已经移动到最佳位置
                self.best_z_micron = params['best_z']
                self.best_score = params['best_score']
                self.focus_points.append((params['best_z'], params['best_score']))
                
                # 完成对焦
                print(f"爬山对焦完成: 最佳位置 {params['best_z']:.2f}μm，评分 {params['best_score']:.2f}")
                self._finish_focus_scan()
                
        except Exception as e:
            print(f"爬山对焦过程错误: {str(e)}")
            self._finish_focus_scan()
    
    def start_three_level_focus(self, use_hill_climbing=True):
        """启动三级对焦过程，从粗对焦开始，自动继续到中等对焦和精细对焦，最后移动到最佳位置
        
        Args:
            use_hill_climbing: 是否使用爬山算法加速对焦过程
        """
        return self.start_coarse_focus(auto_continue=True, auto_goto_best=True, use_hill_climbing=use_hill_climbing)
    
    def move_to_best_focus(self):
        """移动到最佳对焦位置"""
        if self.is_focusing:
            print("自动对焦已在进行中")
            return False
        
        if self.best_z_micron <= 0:
            print("尚未找到最佳对焦位置")
            return False
        
        self.is_focusing = True
        self.moving_to_best = True
        
        # 移动到最佳位置
        target_pulse = self._micron_to_pulse(self.best_z_micron)
        self.z_motor.move_to_position(target_pulse)
        return True
    
    def stop_focus(self):
        """停止当前正在进行的对焦过程"""
        if not self.is_focusing:
            return False
        
        self.abort_focusing = True
        self.is_focusing = False
        
        # 尝试停止电机
        if not self.z_motor.simulation_mode and self.z_motor.motor:
            try:
                self.z_motor.motor.stop()
            except Exception:
                pass
                
        return True
    
    def _move_to_next_position(self):
        """移动到下一个扫描位置"""
        # 检查是否完成扫描或已被中止
        if self.abort_focusing or self.current_scan_index >= len(self.scan_positions):
            self._finish_focus_scan()
            return
        
        # 检查索引是否有效
        if self.current_scan_index < 0 or self.current_scan_index >= len(self.scan_positions):
            print(f"无效的扫描索引: {self.current_scan_index}, 扫描位置数量: {len(self.scan_positions)}")
            self._finish_focus_scan()
            return
        
        # 获取下一个位置
        z_micron = self.scan_positions[self.current_scan_index]
        z_pulse = self._micron_to_pulse(z_micron)
        
        # 移动到该位置
        self.z_motor.move_to_position(z_pulse)
    
    def _on_motor_operation_finished(self, success: bool, message: str):
        """电机操作完成回调
        
        Args:
            success: 操作是否成功
            message: 操作消息
        """
        if not self.is_focusing:
            return
            
        if not success:
            print(f"电机操作失败: {message}")
            self.stop_focus()
            if self.on_focus_completed:
                self.on_focus_completed(False, 0, 0)
            return
            
        # 对焦被中止
        if self.abort_focusing:
            self._finish_focus_scan()
            return
            
        # 如果是移动到最佳位置完成
        if self.moving_to_best:
            self.moving_to_best = False
            self.is_focusing = False
            if self.on_focus_completed:
                self.on_focus_completed(True, self.best_z_micron, self.best_score)
            return
            
        # 如果需要启动爬山算法
        if hasattr(self, 'pending_hill_climbing') and self.pending_hill_climbing:
            self._start_hill_climbing(initial_step=self.hill_climbing_step)
            return
            
        # 如果正在进行爬山对焦，继续下一步
        if hasattr(self, 'hill_climbing_params') and self.is_focusing and self.use_hill_climbing:
            self._continue_hill_climbing()
            return
            
        # 电机移动完成后，评估当前位置的对焦情况
        self._evaluate_current_position()
    
    def _evaluate_current_position(self):
        """评估当前位置的对焦情况"""
        if not self.is_focusing or self.abort_focusing:
            self._finish_focus_scan()
            return
        
        # 检查索引是否有效
        if self.current_scan_index < 0 or self.current_scan_index >= len(self.scan_positions):
            print(f"无效的扫描索引: {self.current_scan_index}, 扫描位置数量: {len(self.scan_positions)}")
            self._finish_focus_scan()
            return
            
        # 获取图像
        frame = self.camera.get_frame()
        if frame is None:
            print("获取图像失败，跳过当前位置")
            self.current_scan_index += 1
            self._move_to_next_position()
            return
            
        # 计算对焦评分
        z_micron = self.scan_positions[self.current_scan_index]
        score = self.calculate_focus_score(frame)
        
        # 记录位置和评分
        self.focus_points.append((z_micron, score))
        
        # 更新最佳对焦位置
        if score > self.best_score:
            self.best_score = score
            self.best_z_micron = z_micron
            
        # 计算进度
        progress = 0
        if len(self.scan_positions) > 0:
            progress = (self.current_scan_index + 1) / len(self.scan_positions) * 100
            
        # 调用进度回调
        if self.on_focus_progress:
            self.on_focus_progress(progress, z_micron, score)
            
        # 移动到下一个位置
        self.current_scan_index += 1
        self._move_to_next_position()
    
    def _finish_focus_scan(self):
        """完成对焦扫描"""
        was_focusing = self.is_focusing
        current_level = self.focus_level
        self.is_focusing = False
        
        if self.abort_focusing:
            print("对焦操作已被中止")
            if self.on_focus_completed and was_focusing:
                self.on_focus_completed(False, 0, 0)
            return
            
        # 检查是否找到有效的对焦位置
        success = self.focus_points and self.best_z_micron > 0
        
        if success:
            print(f"找到最佳对焦位置: {self.best_z_micron:.2f} μm, 得分: {self.best_score:.2f}")
            
            # 判断是否需要进入下一级对焦
            if self.auto_next_level and current_level == "coarse":
                print("粗对焦完成，进入中等精度对焦...")
                self.start_medium_focus(auto_continue=True, auto_goto_best=self.auto_goto_best, use_hill_climbing=self.use_hill_climbing)
                return
            elif self.auto_next_level and current_level == "medium":
                print("中等精度对焦完成，进入精细对焦...")
                self.start_fine_focus(auto_goto_best=self.auto_goto_best, use_hill_climbing=self.use_hill_climbing)
                return
            elif self.auto_goto_best and not self.moving_to_best:
                print(f"对焦完成，自动移动到最佳位置: {self.best_z_micron:.2f} μm")
                self.move_to_best_focus()
                return
        else:
            print("未找到有效的对焦位置")
            
        # 调用完成回调，只在实际执行了对焦过程且没有继续进入下一级且不是移动到最佳位置时调用
        if self.on_focus_completed and was_focusing and not self.is_focusing and not self.moving_to_best:
            self.on_focus_completed(success, self.best_z_micron, self.best_score)

class CombinedControlUI(QMainWindow):
    """Z轴控制和自动对焦的集成界面"""
    
    def __init__(self, com_port="COM8", motor_id=Z_MOTOR_ID):
        super().__init__()
        self.com_port = com_port
        self.motor_id = motor_id
        
        # 初始化相机
        self.camera = Tucam()
        if not self.camera.OpenCamera(0):
            QMessageBox.critical(self, "错误", "无法打开相机，请检查相机连接")
            raise RuntimeError("无法打开相机")
            
        # 开始相机捕获
        self.camera.StartCapture()
        
        # 创建电机控制线程
        self.motor_worker = ZMotorWorker(com_port, motor_id)
        self.motor_worker.positionUpdated.connect(self.update_position_display)
        self.motor_worker.operationFinished.connect(self.handle_operation_result)
        self.motor_worker.progressUpdated.connect(self.update_progress)
        self.motor_worker.start()
        
        # 创建自动对焦控制器 - 使用Autofocus.py的实现
        self.auto_focus = AutoFocus(self.motor_worker, self.camera)
        self.auto_focus.on_focus_progress = self.update_focus_progress
        self.auto_focus.on_focus_completed = self.on_focus_completed
        
        # 设置默认值
        self.current_position_pulse = 0
        self.current_position_micron = 0.0
        self.step_micron = 10.0  # 默认步进值 (微米)
        self.invert_display = True  # 是否反转坐标显示
        
        # 图像刷新计时器
        self.update_timer = QTimer(self)
        self.update_timer.timeout.connect(self.update_camera_display)
        
        self.init_ui()
        
        # 启动计时器
        self.update_timer.start(50)  # 20fps刷新率
        
    def init_ui(self):
        """初始化界面"""
        self.setWindowTitle(f"显微镜Z轴控制与自动对焦 - {self.com_port}")
        self.setGeometry(50, 50, 1200, 800)
        
        # 创建中央部件和主布局
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        main_layout = QHBoxLayout(central_widget)
        
        # 创建分割器：左侧为Z轴和对焦控制，右侧为相机显示
        splitter = QSplitter(Qt.Horizontal)
        main_layout.addWidget(splitter)
        
        # 左侧控制面板
        left_panel = QWidget()
        left_layout = QVBoxLayout(left_panel)
        left_panel.setMinimumWidth(400)
        left_panel.setMaximumWidth(500)
        
        # 右侧相机面板
        right_panel = QWidget()
        right_layout = QVBoxLayout(right_panel)
        
        splitter.addWidget(left_panel)
        splitter.addWidget(right_panel)
        splitter.setSizes([400, 800])
        
        # === 左侧控制面板 ===
        # 创建标签页，分为Z轴控制和自动对焦两个页面
        tab_widget = QTabWidget()
        left_layout.addWidget(tab_widget)
        
        # 位置信息显示组
        position_group = QGroupBox("当前位置信息")
        position_layout = QGridLayout()
        
        # 当前位置显示（脉冲/微米）
        position_layout.addWidget(QLabel("当前位置:"), 0, 0)
        self.pulse_label = QLabel("0 脉冲")
        position_layout.addWidget(self.pulse_label, 0, 1)
        
        self.micron_label = QLabel("0.000 μm")
        position_layout.addWidget(self.micron_label, 0, 2)
        
        # 最佳对焦位置显示
        position_layout.addWidget(QLabel("最佳对焦位置:"), 1, 0)
        self.best_focus_label = QLabel("无")
        position_layout.addWidget(self.best_focus_label, 1, 1, 1, 2)
        
        # 添加进度条
        position_layout.addWidget(QLabel("操作进度:"), 2, 0)
        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setValue(0)
        position_layout.addWidget(self.progress_bar, 2, 1, 1, 2)
        
        position_group.setLayout(position_layout)
        left_layout.addWidget(position_group)
        
        # Z轴控制标签页
        z_control_tab = QWidget()
        z_control_layout = QVBoxLayout(z_control_tab)
        
        # 自动对焦标签页
        auto_focus_tab = QWidget()
        auto_focus_layout = QVBoxLayout(auto_focus_tab)
        
        tab_widget.addTab(z_control_tab, "Z轴控制")
        tab_widget.addTab(auto_focus_tab, "自动对焦")
        
        # --- Z轴控制页面 ---
        # 精确移动控制组
        move_group = QGroupBox("精确移动控制")
        move_layout = QGridLayout()
        
        # 目标位置设置
        move_layout.addWidget(QLabel("目标位置 (μm):"), 0, 0)
        self.target_micron_spin = QDoubleSpinBox()
        self.target_micron_spin.setRange(0, MAX_Z_MICRON)
        self.target_micron_spin.setValue(0)
        self.target_micron_spin.setDecimals(3)
        self.target_micron_spin.setSingleStep(10)
        move_layout.addWidget(self.target_micron_spin, 0, 1)
        
        self.move_to_btn = QPushButton("移动到目标位置")
        self.move_to_btn.clicked.connect(self.on_move_to_clicked)
        move_layout.addWidget(self.move_to_btn, 0, 2)
        
        move_group.setLayout(move_layout)
        z_control_layout.addWidget(move_group)
        
        # 步进控制组
        step_group = QGroupBox("步进控制")
        step_layout = QGridLayout()
        
        # 步进距离设置
        step_layout.addWidget(QLabel("步进距离 (μm):"), 0, 0)
        self.step_micron_spin = QDoubleSpinBox()
        self.step_micron_spin.setRange(0.1, 1000)
        self.step_micron_spin.setValue(self.step_micron)
        self.step_micron_spin.setDecimals(1)
        self.step_micron_spin.setSingleStep(1)
        self.step_micron_spin.valueChanged.connect(self.on_step_micron_changed)
        step_layout.addWidget(self.step_micron_spin, 0, 1)
        
        # 创建步进值快速按钮
        step_btn_layout = QHBoxLayout()
        for value in [1, 10, 100, 500]:
            btn = QPushButton(f"{value}μm")
            btn.setMaximumWidth(60)
            btn.clicked.connect(lambda checked, v=value: self.step_micron_spin.setValue(v))
            step_btn_layout.addWidget(btn)
        
        step_layout.addLayout(step_btn_layout, 1, 0, 1, 2)
        
        # 添加滑动条调整步进距离
        self.step_slider = QSlider(Qt.Horizontal)
        self.step_slider.setRange(1, 1000)  # 0.1μm到100μm
        self.step_slider.setValue(int(self.step_micron))
        self.step_slider.valueChanged.connect(lambda v: self.step_micron_spin.setValue(v))
        step_layout.addWidget(self.step_slider, 2, 0, 1, 2)
        
        # 上下按钮
        button_layout = QHBoxLayout()
        self.up_btn = QPushButton("向上 ↑")
        self.up_btn.clicked.connect(self.on_up_clicked)
        button_layout.addWidget(self.up_btn)
        
        self.down_btn = QPushButton("向下 ↓")
        self.down_btn.clicked.connect(self.on_down_clicked)
        button_layout.addWidget(self.down_btn)
        
        step_layout.addLayout(button_layout, 3, 0, 1, 2)
        
        step_group.setLayout(step_layout)
        z_control_layout.addWidget(step_group)
        
        # 位置控制组
        control_group = QGroupBox("位置控制")
        control_layout = QHBoxLayout()
        
        self.home_btn = QPushButton("回零")
        self.home_btn.clicked.connect(self.on_home_clicked)
        control_layout.addWidget(self.home_btn)
        
        self.center_btn = QPushButton("居中")
        self.center_btn.clicked.connect(self.on_center_clicked)
        control_layout.addWidget(self.center_btn)
        
        self.stop_btn = QPushButton("停止")
        self.stop_btn.clicked.connect(self.on_stop_clicked)
        control_layout.addWidget(self.stop_btn)
        
        control_group.setLayout(control_layout)
        z_control_layout.addWidget(control_group)
        
        # 添加拉伸空间
        z_control_layout.addStretch()
        
        # --- 自动对焦页面 ---
        # 对焦算法选择
        algo_group = QGroupBox("对焦算法")
        algo_layout = QVBoxLayout()
        
        self.method_combo = QComboBox()
        # 基础算法
        self.method_combo.addItem("拉普拉斯算法 (Laplacian)", "laplacian")
        self.method_combo.addItem("索贝尔算子 (Sobel)", "sobel")
        self.method_combo.addItem("Tenengrad算法", "tenengrad")
        self.method_combo.addItem("方差算法 (Variance)", "variance")
        
        # 高级算法 - 使用PyTorch实现
        self.method_combo.addItem("频域分析算法 (FFT)", "fft") 
        
        # OpenCV高级算法
        self.method_combo.addItem("OpenCV增强拉普拉斯算法", "opencv_laplacian")
        self.method_combo.addItem("OpenCV Canny边缘检测", "opencv_canny")
        self.method_combo.addItem("OpenCV景深评估", "opencv_dof")
        self.method_combo.addItem("OpenCV相位相关算法", "opencv_phase_corr")
        self.method_combo.addItem("OpenCV FFT锐度评估", "opencv_fft_sharpness")
        self.method_combo.addItem("OpenCV SMD算法", "opencv_smd")
        self.method_combo.addItem("OpenCV能量梯度算法", "opencv_energy_gradient")
        
        # 如果EfficientNet可用则添加
        if hasattr(self.auto_focus, 'focus_engine') and self.auto_focus.focus_engine is not None:
            # 检查是否支持EfficientNet
            try:
                self.method_combo.addItem("EfficientNetV2-S深度学习", "efficientnet")
            except:
                pass
            
        self.method_combo.currentIndexChanged.connect(self.on_method_changed)
        algo_layout.addWidget(self.method_combo)
        
        # 添加GPU加速状态指示
        gpu_layout = QHBoxLayout()
        gpu_layout.addWidget(QLabel("加速状态:"))
        self.gpu_status_label = QLabel("检测中...")
        
        # 检查PyTorch是否可用
        if hasattr(self.auto_focus, 'focus_engine') and self.auto_focus.focus_engine is not None:
            if self.auto_focus.use_gpu and torch.cuda.is_available():
                self.gpu_status_label.setText(f"GPU加速已启用 ({torch.cuda.get_device_name(0)})")
                self.gpu_status_label.setStyleSheet("color: green; font-weight: bold;")
            else:
                self.gpu_status_label.setText("CPU模式 (PyTorch)")
                self.gpu_status_label.setStyleSheet("color: blue;")
        else:
            self.gpu_status_label.setText("回退模式 (OpenCV)")
            self.gpu_status_label.setStyleSheet("color: orange;")
            
        gpu_layout.addWidget(self.gpu_status_label)
        algo_layout.addLayout(gpu_layout)
        
        algo_group.setLayout(algo_layout)
        auto_focus_layout.addWidget(algo_group)
        
        # 扫描参数设置
        scan_group = QGroupBox("扫描参数")
        scan_layout = QGridLayout()
        
        scan_layout.addWidget(QLabel("粗略扫描范围 (μm):"), 0, 0)
        self.coarse_range_spin = QDoubleSpinBox()
        self.coarse_range_spin.setRange(100, 10000)
        self.coarse_range_spin.setValue(2000)
        self.coarse_range_spin.setSingleStep(100)
        scan_layout.addWidget(self.coarse_range_spin, 0, 1)
        
        scan_layout.addWidget(QLabel("粗略扫描步长 (μm):"), 1, 0)
        self.coarse_step_spin = QDoubleSpinBox()
        self.coarse_step_spin.setRange(1, 500)
        self.coarse_step_spin.setValue(100)
        self.coarse_step_spin.setSingleStep(10)
        scan_layout.addWidget(self.coarse_step_spin, 1, 1)
        
        scan_layout.addWidget(QLabel("中等扫描范围 (μm):"), 2, 0)
        self.medium_range_spin = QDoubleSpinBox()
        self.medium_range_spin.setRange(50, 1000)
        self.medium_range_spin.setValue(300)
        self.medium_range_spin.setSingleStep(10)
        scan_layout.addWidget(self.medium_range_spin, 2, 1)
        
        scan_layout.addWidget(QLabel("中等扫描步长 (μm):"), 3, 0)
        self.medium_step_spin = QDoubleSpinBox()
        self.medium_step_spin.setRange(1, 50)
        self.medium_step_spin.setValue(10)
        self.medium_step_spin.setSingleStep(1)
        scan_layout.addWidget(self.medium_step_spin, 3, 1)
        
        scan_layout.addWidget(QLabel("精细扫描范围 (μm):"), 4, 0)
        self.fine_range_spin = QDoubleSpinBox()
        self.fine_range_spin.setRange(10, 1000)
        self.fine_range_spin.setValue(50)
        self.fine_range_spin.setSingleStep(10)
        scan_layout.addWidget(self.fine_range_spin, 4, 1)
        
        scan_layout.addWidget(QLabel("精细扫描步长 (μm):"), 5, 0)
        self.fine_step_spin = QDoubleSpinBox()
        self.fine_step_spin.setRange(0.5, 50)
        self.fine_step_spin.setValue(0.5)
        self.fine_step_spin.setSingleStep(0.5)
        scan_layout.addWidget(self.fine_step_spin, 5, 1)
        
        # 应用参数按钮
        self.apply_params_btn = QPushButton("应用参数")
        self.apply_params_btn.clicked.connect(self.apply_scan_params)
        scan_layout.addWidget(self.apply_params_btn, 6, 0, 1, 2)
        
        scan_group.setLayout(scan_layout)
        auto_focus_layout.addWidget(scan_group)
        
        # 对焦控制
        focus_group = QGroupBox("对焦控制")
        focus_layout = QVBoxLayout()
        
        # 添加自动化选项
        auto_layout = QGridLayout()
        self.auto_goto_best_checkbox = QCheckBox("对焦完成后自动移动到最佳位置")
        self.auto_goto_best_checkbox.setChecked(True)
        auto_layout.addWidget(self.auto_goto_best_checkbox, 0, 0, 1, 2)
        
        # 添加爬山算法选项
        self.hill_climbing_checkbox = QCheckBox("使用爬山算法加速对焦过程")
        self.hill_climbing_checkbox.setChecked(True)
        self.hill_climbing_checkbox.setToolTip("使用爬山算法可以大幅提高对焦速度，但在某些情况下可能找不到全局最优解")
        auto_layout.addWidget(self.hill_climbing_checkbox, 1, 0, 1, 2)
        
        focus_layout.addLayout(auto_layout)
        
        # 对焦按钮
        buttons_layout = QGridLayout()
        
        self.three_level_focus_btn = QPushButton("三级自动对焦")
        self.three_level_focus_btn.clicked.connect(self.start_three_level_focus)
        buttons_layout.addWidget(self.three_level_focus_btn, 0, 0, 1, 2)
        
        self.coarse_focus_btn = QPushButton("粗略对焦")
        self.coarse_focus_btn.clicked.connect(self.start_coarse_focus)
        buttons_layout.addWidget(self.coarse_focus_btn, 1, 0)
        
        self.medium_focus_btn = QPushButton("中等精度对焦")
        self.medium_focus_btn.clicked.connect(self.start_medium_focus)
        buttons_layout.addWidget(self.medium_focus_btn, 1, 1)
        
        self.fine_focus_btn = QPushButton("精细对焦")
        self.fine_focus_btn.clicked.connect(self.start_fine_focus)
        buttons_layout.addWidget(self.fine_focus_btn, 2, 0)
        
        self.goto_best_btn = QPushButton("移至最佳位置")
        self.goto_best_btn.clicked.connect(self.move_to_best_focus)
        buttons_layout.addWidget(self.goto_best_btn, 2, 1)
        
        self.stop_focus_btn = QPushButton("停止对焦")
        self.stop_focus_btn.clicked.connect(self.stop_focus)
        buttons_layout.addWidget(self.stop_focus_btn, 3, 0, 1, 2)
        
        focus_layout.addLayout(buttons_layout)
        
        focus_group.setLayout(focus_layout)
        auto_focus_layout.addWidget(focus_group)
        
        # 添加拉伸空间
        auto_focus_layout.addStretch()
        
        # 状态栏
        self.status_label = QLabel("就绪")
        left_layout.addWidget(self.status_label)
        
        # === 右侧相机预览区 ===
        preview_group = QGroupBox("相机预览")
        preview_layout = QVBoxLayout()
        
        # 相机图像显示
        self.image_label = QLabel()
        self.image_label.setAlignment(Qt.AlignCenter)
        self.image_label.setMinimumSize(600, 400)
        self.image_label.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        preview_layout.addWidget(self.image_label)
        
        # 对焦评分显示
        score_layout = QHBoxLayout()
        score_layout.addWidget(QLabel("对焦评分:"))
        self.score_label = QLabel("0.00")
        score_layout.addWidget(self.score_label)
        score_layout.addStretch()
        preview_layout.addLayout(score_layout)
        
        # 截图按钮
        capture_btn = QPushButton("截图")
        capture_btn.clicked.connect(self.capture_image)
        preview_layout.addWidget(capture_btn)
        
        preview_group.setLayout(preview_layout)
        right_layout.addWidget(preview_group)
        
        # 初始化UI状态
        self.update_focus_ui_state(False)
        
    def update_position_display(self, position_pulse: int):
        """更新位置显示"""
        self.current_position_pulse = position_pulse
        # 转换脉冲到微米
        raw_micron = position_pulse / PULSES_PER_MICRON_Z  
        self.current_position_micron = raw_micron
        
        # 更新标签
        self.pulse_label.setText(f"{position_pulse} 脉冲")
        self.micron_label.setText(f"{raw_micron:.3f} μm")
    
    def update_camera_display(self):
        """更新相机图像显示"""
        frame = self.camera.get_frame()
        if frame is not None:
            # 显示图像
            pixmap = QPixmap.fromImage(frame)
            scaled_pixmap = pixmap.scaled(
                self.image_label.size(),
                Qt.KeepAspectRatio,
                Qt.SmoothTransformation
            )
            self.image_label.setPixmap(scaled_pixmap)
            
            # 计算并显示对焦评分
            score = self.auto_focus.calculate_focus_score(frame)
            self.score_label.setText(f"{score:.2f}")
    
    def capture_image(self):
        """捕获当前图像"""
        # 创建保存目录如果不存在
        import os
        from datetime import datetime
        
        save_dir = "captures"
        if not os.path.exists(save_dir):
            os.makedirs(save_dir)
            
        # 生成带时间戳的文件名
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"{save_dir}/capture_{timestamp}"
        
        if self.camera.SaveImage(filename):
            self.status_label.setText(f"图像已保存: {filename}")
        else:
            self.status_label.setText("保存图像失败")
    
    def handle_operation_result(self, success: bool, message: str):
        """处理操作结果"""
        if success:
            self.status_label.setText(f"成功: {message}")
        else:
            self.status_label.setText(f"错误: {message}")
            QMessageBox.warning(self, "操作失败", message)
    
    def update_progress(self, value: int):
        """更新进度条"""
        self.progress_bar.setValue(value)
    
    def on_step_micron_changed(self, value: float):
        """步进距离改变处理"""
        self.step_micron = value
        # 同步更新滑块，避免循环
        self.step_slider.blockSignals(True)
        self.step_slider.setValue(int(value))
        self.step_slider.blockSignals(False)
    
    def on_move_to_clicked(self):
        """移动到指定位置"""
        target_micron_display = self.target_micron_spin.value()
        
        # 如果启用了反转显示，需要转换回实际值
        if self.invert_display:
            target_micron_actual = MAX_Z_MICRON - target_micron_display
        else:
            target_micron_actual = target_micron_display
        
        # 转换微米到脉冲
        target_pulse = int(target_micron_actual * PULSES_PER_MICRON_Z)
        
        # 检查脉冲范围
        max_pulse = int(MAX_Z_MICRON * PULSES_PER_MICRON_Z)
        if not (0 <= target_pulse <= max_pulse):
            QMessageBox.warning(self, "无效目标", f"目标位置超出范围 [0, {MAX_Z_MICRON}] μm")
            return
        
        # 发送移动命令
        self.status_label.setText(f"移动到 {target_micron_display:.3f} μm...")
        self.motor_worker.move_to_position(target_pulse)
    
    def on_up_clicked(self):
        """向上移动一步 - 物理上载物台向上移动"""
        # 计算步进脉冲值
        step_pulse = int(self.step_micron * PULSES_PER_MICRON_Z)
        self.status_label.setText(f"向上移动 {self.step_micron:.1f} μm...")
        self.motor_worker.step_move(True, step_pulse)
    
    def on_down_clicked(self):
        """向下移动一步 - 物理上载物台向下移动"""
        # 计算步进脉冲值
        step_pulse = int(self.step_micron * PULSES_PER_MICRON_Z)
        self.status_label.setText(f"向下移动 {self.step_micron:.1f} μm...")
        self.motor_worker.step_move(False, step_pulse)
    
    def on_home_clicked(self):
        """回零"""
        reply = QMessageBox.question(
            self, "确认回零", 
            "确定要将Z轴移动到零位置吗？", 
            QMessageBox.Yes | QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            self.status_label.setText("正在回零...")
            self.motor_worker.home()
    
    def on_center_clicked(self):
        """移动到中心位置"""
        self.status_label.setText("正在移动到中心位置...")
        self.motor_worker.center()
    
    def on_stop_clicked(self):
        """停止当前移动"""
        if not self.motor_worker.simulation_mode and self.motor_worker.motor:
            try:
                self.motor_worker.motor.stop()
                self.status_label.setText("已停止移动")
            except Exception as e:
                self.status_label.setText(f"停止失败: {str(e)}")
        else:
            self.status_label.setText("已停止移动 (模拟模式)")
    
    def on_method_changed(self, index):
        """对焦方法改变事件"""
        method = self.method_combo.currentData()
        self.auto_focus.set_focus_method(method)
        self.status_label.setText(f"对焦算法已设置为: {self.method_combo.currentText()}")
    
    def apply_scan_params(self):
        """应用扫描参数"""
        coarse_range = self.coarse_range_spin.value()
        coarse_step = self.coarse_step_spin.value()
        medium_range = self.medium_range_spin.value()
        medium_step = self.medium_step_spin.value()
        fine_range = self.fine_range_spin.value()
        fine_step = self.fine_step_spin.value()
        
        self.auto_focus.set_coarse_scan_params(coarse_range, coarse_step)
        self.auto_focus.set_medium_scan_params(medium_range, medium_step)
        self.auto_focus.set_fine_scan_params(fine_range, fine_step)
        
        self.status_label.setText("扫描参数已更新")
    
    def start_three_level_focus(self):
        """开始三级自动对焦"""
        use_hill_climbing = self.hill_climbing_checkbox.isChecked()
        if self.auto_focus.start_three_level_focus(use_hill_climbing=use_hill_climbing):
            self.update_focus_ui_state(True)
            self.status_label.setText("正在进行三级自动对焦...")
            self.progress_bar.setValue(0)
    
    def start_coarse_focus(self):
        """开始粗略对焦"""
        auto_goto_best = self.auto_goto_best_checkbox.isChecked()
        use_hill_climbing = self.hill_climbing_checkbox.isChecked()
        if self.auto_focus.start_coarse_focus(auto_continue=False, auto_goto_best=auto_goto_best, use_hill_climbing=use_hill_climbing):
            self.update_focus_ui_state(True)
            self.status_label.setText("正在进行粗略对焦...")
            self.progress_bar.setValue(0)
    
    def start_medium_focus(self):
        """开始中等精度对焦"""
        auto_goto_best = self.auto_goto_best_checkbox.isChecked()
        use_hill_climbing = self.hill_climbing_checkbox.isChecked()
        if self.auto_focus.start_medium_focus(auto_continue=False, auto_goto_best=auto_goto_best, use_hill_climbing=use_hill_climbing):
            self.update_focus_ui_state(True)
            self.status_label.setText("正在进行中等精度对焦...")
            self.progress_bar.setValue(0)
    
    def start_fine_focus(self):
        """开始精细对焦"""
        auto_goto_best = self.auto_goto_best_checkbox.isChecked()
        use_hill_climbing = self.hill_climbing_checkbox.isChecked()
        if self.auto_focus.start_fine_focus(auto_goto_best=auto_goto_best, use_hill_climbing=use_hill_climbing):
            self.update_focus_ui_state(True)
            self.status_label.setText("正在进行精细对焦...")
            self.progress_bar.setValue(0)
    
    def move_to_best_focus(self):
        """移动到最佳对焦位置"""
        if self.auto_focus.move_to_best_focus():
            self.update_focus_ui_state(True)
            self.status_label.setText(f"正在移动到最佳对焦位置: {self.auto_focus.best_z_micron:.2f} μm...")
    
    def stop_focus(self):
        """停止对焦"""
        if self.auto_focus.stop_focus():
            self.update_focus_ui_state(False)
            self.status_label.setText("对焦操作已停止")
    
    def update_focus_progress(self, progress, z_position, score):
        """更新对焦进度
        
        Args:
            progress: 进度百分比
            z_position: 当前Z位置
            score: 当前对焦评分
        """
        self.progress_bar.setValue(int(progress))
        self.status_label.setText(f"扫描进度: {progress:.1f}%, 位置: {z_position:.2f} μm, 评分: {score:.2f}")
    
    def on_focus_completed(self, success, best_z, best_score):
        """对焦完成回调
        
        Args:
            success: 是否成功找到最佳位置
            best_z: 最佳对焦位置
            best_score: 最佳对焦评分
        """
        self.update_focus_ui_state(False)
        
        if success:
            self.best_focus_label.setText(f"{best_z:.2f} μm")
            self.status_label.setText(f"对焦完成: 最佳位置 {best_z:.2f} μm, 评分 {best_score:.2f}")
        else:
            self.status_label.setText("未找到有效的对焦位置")
    
    def update_focus_ui_state(self, is_focusing):
        """更新对焦UI状态
        
        Args:
            is_focusing: 是否正在对焦
        """
        # 在对焦过程中禁用控制按钮
        self.three_level_focus_btn.setEnabled(not is_focusing)
        self.coarse_focus_btn.setEnabled(not is_focusing)
        self.medium_focus_btn.setEnabled(not is_focusing)
        self.fine_focus_btn.setEnabled(not is_focusing)
        self.goto_best_btn.setEnabled(not is_focusing and self.auto_focus.best_z_micron > 0)
        self.stop_focus_btn.setEnabled(is_focusing)
        self.apply_params_btn.setEnabled(not is_focusing)
        self.method_combo.setEnabled(not is_focusing)
        self.auto_goto_best_checkbox.setEnabled(not is_focusing)
        
        # 在对焦过程中禁用参数控件
        self.coarse_range_spin.setEnabled(not is_focusing)
        self.coarse_step_spin.setEnabled(not is_focusing)
        self.medium_range_spin.setEnabled(not is_focusing)
        self.medium_step_spin.setEnabled(not is_focusing)
        self.fine_range_spin.setEnabled(not is_focusing)
        self.fine_step_spin.setEnabled(not is_focusing)
        
        # 也禁用Z轴控制按钮
        self.move_to_btn.setEnabled(not is_focusing)
        self.up_btn.setEnabled(not is_focusing)
        self.down_btn.setEnabled(not is_focusing)
        self.home_btn.setEnabled(not is_focusing)
        self.center_btn.setEnabled(not is_focusing)
    
    def closeEvent(self, event):
        """窗口关闭事件，确保释放所有资源"""
        try:
            # 停止定时器
            if hasattr(self, 'update_timer') and self.update_timer:
                self.update_timer.stop()

            # 在此处停止并等待所有线程完成
            # 停止自动对焦
            if hasattr(self, 'auto_focus') and self.auto_focus:
                self.auto_focus.stop_focus()
                print("已停止自动对焦")
            
            # 停止电机工作线程，但不要在closeEvent中等待，只设置退出标志
            if hasattr(self, 'motor_worker') and self.motor_worker:
                print("正在停止电机线程...")
                self.motor_worker.running = False
                # 发出停止信号，但不等待
                self.motor_worker.stop()

            # 停止相机
            if hasattr(self, 'camera') and self.camera:
                try:
                    self.camera.StopCapture()
                    print("已停止相机捕获")
                except Exception as e:
                    print(f"停止相机捕获时出错: {str(e)}")

            # 立即接受关闭事件，确保界面不卡顿
            event.accept()
            
            # 使用QTimer的singleShot机制在事件循环中安排资源清理
            # 这样可以避免在closeEvent中阻塞UI线程
            QTimer.singleShot(100, self._cleanup_resources)
            
        except Exception as e:
            print(f"关闭时出错: {str(e)}")
            event.accept()
    
    def _cleanup_resources(self):
        """在主窗口关闭后清理资源，防止界面卡住"""
        try:
            print("开始清理资源...")
            
            # 停止并等待电机工作线程完成
            if hasattr(self, 'motor_worker') and self.motor_worker:
                # 安全终止线程
                try:
                    # 给线程一个短暂的时间自行退出
                    if self.motor_worker.isRunning():
                        if not self.motor_worker.wait(500):  # 500ms超时
                            print("电机线程未响应，强制终止")
                            self.motor_worker.terminate()
                            # 给terminate一点时间生效
                            self.motor_worker.wait(100)
                    print("电机线程已停止")
                except Exception as e:
                    print(f"终止电机线程时出错: {str(e)}")
                
            # 关闭相机连接
            if hasattr(self, 'camera') and self.camera:
                try:
                    self.camera.CloseCamera()
                    print("已关闭相机")
                except Exception as e:
                    print(f"关闭相机时出错: {str(e)}")
                    
            # 最后再释放相机API
            if hasattr(self, 'camera') and self.camera:
                try:
                    self.camera.UnInitApi()
                    print("已释放相机API")
                except Exception as e:
                    print(f"释放相机API时出错: {str(e)}")
                
            print("所有资源已释放")
            
            # 确保应用退出 - 使用延迟，确保此函数完成后才退出
            QTimer.singleShot(200, lambda: QApplication.instance().quit())
            
        except Exception as e:
            print(f"清理资源时出错: {str(e)}")
            # 即使出错也确保应用退出
            QTimer.singleShot(200, lambda: QApplication.instance().quit())

def main():
    """主函数入口，显示Z轴控制和自动对焦的集成界面"""
    com_port = "COM8"  # 根据实际情况更改COM端口
    
    app = QApplication(sys.argv)
    
    try:
        # 创建并显示集成界面
        window = CombinedControlUI(com_port=com_port)
        window.show()
        
        sys.exit(app.exec())
    except Exception as e:
        QMessageBox.critical(None, "启动错误", f"程序启动失败: {str(e)}")
        print(f"程序启动失败: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
