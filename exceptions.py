#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
自定义异常类和异常处理工具
"""

from typing import Optional, Any
import traceback


class MicroscopeException(Exception):
    """显微镜系统基础异常类"""
    
    def __init__(self, message: str, error_code: Optional[str] = None, 
                 details: Optional[dict] = None):
        super().__init__(message)
        self.message = message
        self.error_code = error_code
        self.details = details or {}
    
    def __str__(self):
        result = self.message
        if self.error_code:
            result = f"[{self.error_code}] {result}"
        if self.details:
            result += f" (详情: {self.details})"
        return result


class MotorException(MicroscopeException):
    """电机相关异常"""
    pass


class MotorConnectionException(MotorException):
    """电机连接异常"""
    pass


class MotorMovementException(MotorException):
    """电机移动异常"""
    pass


class MotorLimitException(MotorException):
    """电机限位异常"""
    pass


class CameraException(MicroscopeException):
    """相机相关异常"""
    pass


class CameraConnectionException(CameraException):
    """相机连接异常"""
    pass


class CameraConfigurationException(CameraException):
    """相机配置异常"""
    pass


class CameraCaptureException(CameraException):
    """相机拍摄异常"""
    pass


class AutoFocusException(MicroscopeException):
    """自动对焦异常"""
    pass


class AutoFocusInitException(AutoFocusException):
    """自动对焦初始化异常"""
    pass


class AutoFocusCalculationException(AutoFocusException):
    """自动对焦计算异常"""
    pass


class ScanException(MicroscopeException):
    """扫描相关异常"""
    pass


class ScanConfigurationException(ScanException):
    """扫描配置异常"""
    pass


class ScanExecutionException(ScanException):
    """扫描执行异常"""
    pass


class ConfigurationException(MicroscopeException):
    """配置相关异常"""
    pass


class UIException(MicroscopeException):
    """UI相关异常"""
    pass


class ExceptionHandler:
    """异常处理器"""
    
    @staticmethod
    def handle_motor_exception(e: Exception, operation: str = "", 
                             axis: Optional[str] = None) -> MotorException:
        """处理电机异常"""
        details = {"operation": operation}
        if axis:
            details["axis"] = axis
        
        if "连接" in str(e) or "connection" in str(e).lower():
            return MotorConnectionException(f"电机连接失败: {str(e)}", 
                                          "MOTOR_CONNECTION_ERROR", details)
        elif "限位" in str(e) or "limit" in str(e).lower():
            return MotorLimitException(f"电机限位错误: {str(e)}", 
                                     "MOTOR_LIMIT_ERROR", details)
        elif "移动" in str(e) or "move" in str(e).lower():
            return MotorMovementException(f"电机移动失败: {str(e)}", 
                                        "MOTOR_MOVEMENT_ERROR", details)
        else:
            return MotorException(f"电机操作失败: {str(e)}", 
                                "MOTOR_GENERAL_ERROR", details)
    
    @staticmethod
    def handle_camera_exception(e: Exception, operation: str = "") -> CameraException:
        """处理相机异常"""
        details = {"operation": operation}
        
        if "连接" in str(e) or "connection" in str(e).lower():
            return CameraConnectionException(f"相机连接失败: {str(e)}", 
                                           "CAMERA_CONNECTION_ERROR", details)
        elif "配置" in str(e) or "config" in str(e).lower():
            return CameraConfigurationException(f"相机配置失败: {str(e)}", 
                                              "CAMERA_CONFIG_ERROR", details)
        elif "拍摄" in str(e) or "capture" in str(e).lower():
            return CameraCaptureException(f"相机拍摄失败: {str(e)}", 
                                        "CAMERA_CAPTURE_ERROR", details)
        else:
            return CameraException(f"相机操作失败: {str(e)}", 
                                 "CAMERA_GENERAL_ERROR", details)
    
    @staticmethod
    def handle_autofocus_exception(e: Exception, operation: str = "") -> AutoFocusException:
        """处理自动对焦异常"""
        details = {"operation": operation}
        
        if "初始化" in str(e) or "init" in str(e).lower():
            return AutoFocusInitException(f"自动对焦初始化失败: {str(e)}", 
                                        "AUTOFOCUS_INIT_ERROR", details)
        elif "计算" in str(e) or "calculation" in str(e).lower():
            return AutoFocusCalculationException(f"对焦评分计算失败: {str(e)}", 
                                               "AUTOFOCUS_CALC_ERROR", details)
        else:
            return AutoFocusException(f"自动对焦操作失败: {str(e)}", 
                                    "AUTOFOCUS_GENERAL_ERROR", details)
    
    @staticmethod
    def handle_scan_exception(e: Exception, operation: str = "") -> ScanException:
        """处理扫描异常"""
        details = {"operation": operation}
        
        if "配置" in str(e) or "config" in str(e).lower():
            return ScanConfigurationException(f"扫描配置错误: {str(e)}", 
                                            "SCAN_CONFIG_ERROR", details)
        elif "执行" in str(e) or "execution" in str(e).lower():
            return ScanExecutionException(f"扫描执行失败: {str(e)}", 
                                        "SCAN_EXECUTION_ERROR", details)
        else:
            return ScanException(f"扫描操作失败: {str(e)}", 
                               "SCAN_GENERAL_ERROR", details)


def safe_execute(func, *args, exception_handler=None, default_return=None, 
                 logger=None, **kwargs):
    """
    安全执行函数，自动处理异常
    
    Args:
        func: 要执行的函数
        *args: 函数参数
        exception_handler: 异常处理器函数
        default_return: 异常时的默认返回值
        logger: 日志器
        **kwargs: 函数关键字参数
    
    Returns:
        函数执行结果或默认返回值
    """
    try:
        return func(*args, **kwargs)
    except Exception as e:
        if logger:
            logger.error(f"执行函数 {func.__name__} 时发生异常", exception=e)
        
        if exception_handler:
            handled_exception = exception_handler(e)
            if logger:
                logger.error(f"处理后的异常: {handled_exception}")
            raise handled_exception
        
        return default_return


def retry_on_exception(max_retries: int = 3, delay: float = 1.0, 
                      exceptions: tuple = (Exception,), logger=None):
    """
    重试装饰器
    
    Args:
        max_retries: 最大重试次数
        delay: 重试间隔（秒）
        exceptions: 需要重试的异常类型
        logger: 日志器
    """
    def decorator(func):
        def wrapper(*args, **kwargs):
            import time
            
            last_exception = None
            for attempt in range(max_retries + 1):
                try:
                    return func(*args, **kwargs)
                except exceptions as e:
                    last_exception = e
                    if attempt < max_retries:
                        if logger:
                            logger.warning(f"函数 {func.__name__} 第 {attempt + 1} 次执行失败，"
                                         f"{delay}秒后重试: {str(e)}")
                        time.sleep(delay)
                    else:
                        if logger:
                            logger.error(f"函数 {func.__name__} 重试 {max_retries} 次后仍然失败")
                        raise last_exception
            
            # 这行代码理论上不会执行到
            raise last_exception
        
        return wrapper
    return decorator


def validate_parameter(value: Any, param_name: str, expected_type: type = None, 
                      min_value: Optional[float] = None, max_value: Optional[float] = None,
                      allowed_values: Optional[list] = None):
    """
    参数验证函数
    
    Args:
        value: 要验证的值
        param_name: 参数名称
        expected_type: 期望的类型
        min_value: 最小值
        max_value: 最大值
        allowed_values: 允许的值列表
    
    Raises:
        ValueError: 参数验证失败
    """
    if expected_type and not isinstance(value, expected_type):
        raise ValueError(f"参数 {param_name} 类型错误，期望 {expected_type.__name__}，"
                        f"实际 {type(value).__name__}")
    
    if min_value is not None and value < min_value:
        raise ValueError(f"参数 {param_name} 值 {value} 小于最小值 {min_value}")
    
    if max_value is not None and value > max_value:
        raise ValueError(f"参数 {param_name} 值 {value} 大于最大值 {max_value}")
    
    if allowed_values is not None and value not in allowed_values:
        raise ValueError(f"参数 {param_name} 值 {value} 不在允许的值列表中: {allowed_values}")


if __name__ == "__main__":
    # 测试异常处理
    from logger import get_logger
    
    logger = get_logger("test")
    
    # 测试安全执行
    def test_function(x, y):
        if x == 0:
            raise ValueError("x不能为0")
        return x / y
    
    result = safe_execute(test_function, 0, 1, 
                         exception_handler=lambda e: ValueError(f"处理后: {e}"),
                         default_return=-1, logger=logger)
    print(f"安全执行结果: {result}")
    
    # 测试重试装饰器
    @retry_on_exception(max_retries=2, delay=0.1, logger=logger)
    def unstable_function():
        import random
        if random.random() < 0.7:
            raise ConnectionError("连接失败")
        return "成功"
    
    try:
        result = unstable_function()
        print(f"重试结果: {result}")
    except Exception as e:
        print(f"最终失败: {e}")
    
    # 测试参数验证
    try:
        validate_parameter(5, "test_param", int, min_value=0, max_value=10)
        print("参数验证通过")
    except ValueError as e:
        print(f"参数验证失败: {e}")
