#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
代码质量检查和改进工具
提供代码风格检查、类型注解验证和文档生成功能
"""

import ast
import os
import re
import inspect
from typing import List, Dict, Any, Optional, Tuple
from pathlib import Path
from dataclasses import dataclass
from datetime import datetime

from logger import get_logger


@dataclass
class CodeIssue:
    """代码问题数据类"""
    file_path: str
    line_number: int
    issue_type: str  # "style", "type", "doc", "complexity"
    severity: str    # "error", "warning", "info"
    message: str
    suggestion: Optional[str] = None


@dataclass
class CodeMetrics:
    """代码指标数据类"""
    file_path: str
    lines_of_code: int
    lines_of_comments: int
    lines_of_docstrings: int
    cyclomatic_complexity: int
    functions_count: int
    classes_count: int
    type_annotations_coverage: float
    docstring_coverage: float


class CodeAnalyzer:
    """代码分析器"""
    
    def __init__(self):
        self.logger = get_logger("code_quality")
        self.issues: List[CodeIssue] = []
        self.metrics: List[CodeMetrics] = []
    
    def analyze_file(self, file_path: str) -> Tuple[List[CodeIssue], CodeMetrics]:
        """分析单个文件"""
        self.logger.info(f"分析文件: {file_path}")
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 解析AST
            tree = ast.parse(content, filename=file_path)
            
            # 分析代码
            issues = []
            issues.extend(self._check_style(file_path, content))
            issues.extend(self._check_type_annotations(file_path, tree))
            issues.extend(self._check_docstrings(file_path, tree))
            issues.extend(self._check_complexity(file_path, tree))
            
            # 计算指标
            metrics = self._calculate_metrics(file_path, content, tree)
            
            return issues, metrics
            
        except Exception as e:
            self.logger.error(f"分析文件 {file_path} 时发生错误", exception=e)
            return [], CodeMetrics(file_path, 0, 0, 0, 0, 0, 0, 0.0, 0.0)
    
    def analyze_directory(self, directory: str, 
                         extensions: List[str] = ['.py']) -> Tuple[List[CodeIssue], List[CodeMetrics]]:
        """分析目录中的所有文件"""
        all_issues = []
        all_metrics = []
        
        for root, dirs, files in os.walk(directory):
            # 跳过隐藏目录和__pycache__
            dirs[:] = [d for d in dirs if not d.startswith('.') and d != '__pycache__']
            
            for file in files:
                if any(file.endswith(ext) for ext in extensions):
                    file_path = os.path.join(root, file)
                    issues, metrics = self.analyze_file(file_path)
                    all_issues.extend(issues)
                    all_metrics.append(metrics)
        
        return all_issues, all_metrics
    
    def _check_style(self, file_path: str, content: str) -> List[CodeIssue]:
        """检查代码风格"""
        issues = []
        lines = content.split('\n')
        
        for i, line in enumerate(lines, 1):
            # 检查行长度
            if len(line) > 120:
                issues.append(CodeIssue(
                    file_path, i, "style", "warning",
                    f"行长度超过120字符 ({len(line)}字符)",
                    "考虑将长行拆分为多行"
                ))
            
            # 检查尾随空格
            if line.endswith(' ') or line.endswith('\t'):
                issues.append(CodeIssue(
                    file_path, i, "style", "info",
                    "行末有尾随空格",
                    "删除行末的空格"
                ))
            
            # 检查制表符
            if '\t' in line:
                issues.append(CodeIssue(
                    file_path, i, "style", "warning",
                    "使用了制表符而不是空格",
                    "使用4个空格代替制表符"
                ))
        
        return issues
    
    def _check_type_annotations(self, file_path: str, tree: ast.AST) -> List[CodeIssue]:
        """检查类型注解"""
        issues = []
        
        class TypeAnnotationChecker(ast.NodeVisitor):
            def visit_FunctionDef(self, node):
                # 检查函数参数类型注解
                for arg in node.args.args:
                    if arg.annotation is None and arg.arg != 'self':
                        issues.append(CodeIssue(
                            file_path, node.lineno, "type", "info",
                            f"函数 {node.name} 的参数 {arg.arg} 缺少类型注解",
                            f"添加类型注解: {arg.arg}: <type>"
                        ))
                
                # 检查返回值类型注解
                if node.returns is None:
                    issues.append(CodeIssue(
                        file_path, node.lineno, "type", "info",
                        f"函数 {node.name} 缺少返回值类型注解",
                        "添加返回值类型注解: -> <type>"
                    ))
                
                self.generic_visit(node)
            
            def visit_AsyncFunctionDef(self, node):
                self.visit_FunctionDef(node)
        
        TypeAnnotationChecker().visit(tree)
        return issues
    
    def _check_docstrings(self, file_path: str, tree: ast.AST) -> List[CodeIssue]:
        """检查文档字符串"""
        issues = []
        
        class DocstringChecker(ast.NodeVisitor):
            def visit_ClassDef(self, node):
                if not ast.get_docstring(node):
                    issues.append(CodeIssue(
                        file_path, node.lineno, "doc", "warning",
                        f"类 {node.name} 缺少文档字符串",
                        "添加类文档字符串说明类的用途"
                    ))
                self.generic_visit(node)
            
            def visit_FunctionDef(self, node):
                if not ast.get_docstring(node) and not node.name.startswith('_'):
                    issues.append(CodeIssue(
                        file_path, node.lineno, "doc", "info",
                        f"函数 {node.name} 缺少文档字符串",
                        "添加函数文档字符串说明参数、返回值和功能"
                    ))
                self.generic_visit(node)
            
            def visit_AsyncFunctionDef(self, node):
                self.visit_FunctionDef(node)
        
        DocstringChecker().visit(tree)
        return issues
    
    def _check_complexity(self, file_path: str, tree: ast.AST) -> List[CodeIssue]:
        """检查复杂度"""
        issues = []
        
        class ComplexityChecker(ast.NodeVisitor):
            def visit_FunctionDef(self, node):
                complexity = self._calculate_cyclomatic_complexity(node)
                if complexity > 10:
                    issues.append(CodeIssue(
                        file_path, node.lineno, "complexity", "warning",
                        f"函数 {node.name} 的圈复杂度过高 ({complexity})",
                        "考虑将函数拆分为更小的函数"
                    ))
                self.generic_visit(node)
            
            def visit_AsyncFunctionDef(self, node):
                self.visit_FunctionDef(node)
            
            def _calculate_cyclomatic_complexity(self, node):
                """计算圈复杂度"""
                complexity = 1  # 基础复杂度
                
                for child in ast.walk(node):
                    if isinstance(child, (ast.If, ast.While, ast.For, ast.AsyncFor)):
                        complexity += 1
                    elif isinstance(child, ast.ExceptHandler):
                        complexity += 1
                    elif isinstance(child, ast.With, ast.AsyncWith):
                        complexity += 1
                    elif isinstance(child, ast.BoolOp):
                        complexity += len(child.values) - 1
                
                return complexity
        
        ComplexityChecker().visit(tree)
        return issues
    
    def _calculate_metrics(self, file_path: str, content: str, tree: ast.AST) -> CodeMetrics:
        """计算代码指标"""
        lines = content.split('\n')
        
        # 计算代码行数
        lines_of_code = len([line for line in lines if line.strip() and not line.strip().startswith('#')])
        
        # 计算注释行数
        lines_of_comments = len([line for line in lines if line.strip().startswith('#')])
        
        # 计算文档字符串行数
        lines_of_docstrings = 0
        for node in ast.walk(tree):
            docstring = ast.get_docstring(node)
            if docstring:
                lines_of_docstrings += len(docstring.split('\n'))
        
        # 计算函数和类的数量
        functions_count = len([node for node in ast.walk(tree) 
                              if isinstance(node, (ast.FunctionDef, ast.AsyncFunctionDef))])
        classes_count = len([node for node in ast.walk(tree) if isinstance(node, ast.ClassDef)])
        
        # 计算类型注解覆盖率
        total_functions = functions_count
        annotated_functions = 0
        for node in ast.walk(tree):
            if isinstance(node, (ast.FunctionDef, ast.AsyncFunctionDef)):
                if node.returns is not None:
                    annotated_functions += 1
        
        type_annotations_coverage = (annotated_functions / total_functions * 100) if total_functions > 0 else 0
        
        # 计算文档字符串覆盖率
        total_definitions = functions_count + classes_count
        documented_definitions = 0
        for node in ast.walk(tree):
            if isinstance(node, (ast.FunctionDef, ast.AsyncFunctionDef, ast.ClassDef)):
                if ast.get_docstring(node):
                    documented_definitions += 1
        
        docstring_coverage = (documented_definitions / total_definitions * 100) if total_definitions > 0 else 0
        
        # 计算平均圈复杂度
        total_complexity = 0
        for node in ast.walk(tree):
            if isinstance(node, (ast.FunctionDef, ast.AsyncFunctionDef)):
                complexity = 1
                for child in ast.walk(node):
                    if isinstance(child, (ast.If, ast.While, ast.For, ast.AsyncFor)):
                        complexity += 1
                total_complexity += complexity
        
        avg_complexity = total_complexity // functions_count if functions_count > 0 else 0
        
        return CodeMetrics(
            file_path=file_path,
            lines_of_code=lines_of_code,
            lines_of_comments=lines_of_comments,
            lines_of_docstrings=lines_of_docstrings,
            cyclomatic_complexity=avg_complexity,
            functions_count=functions_count,
            classes_count=classes_count,
            type_annotations_coverage=type_annotations_coverage,
            docstring_coverage=docstring_coverage
        )


class CodeQualityReporter:
    """代码质量报告生成器"""
    
    def __init__(self):
        self.logger = get_logger("code_quality")
    
    def generate_report(self, issues: List[CodeIssue], metrics: List[CodeMetrics], 
                       output_file: str = "code_quality_report.html"):
        """生成HTML格式的代码质量报告"""
        try:
            html_content = self._generate_html_report(issues, metrics)
            
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(html_content)
            
            self.logger.info(f"代码质量报告已生成: {output_file}")
            return True
            
        except Exception as e:
            self.logger.error(f"生成代码质量报告失败", exception=e)
            return False
    
    def _generate_html_report(self, issues: List[CodeIssue], metrics: List[CodeMetrics]) -> str:
        """生成HTML报告内容"""
        # 统计信息
        total_issues = len(issues)
        error_count = len([i for i in issues if i.severity == "error"])
        warning_count = len([i for i in issues if i.severity == "warning"])
        info_count = len([i for i in issues if i.severity == "info"])
        
        # 按文件分组问题
        issues_by_file = {}
        for issue in issues:
            if issue.file_path not in issues_by_file:
                issues_by_file[issue.file_path] = []
            issues_by_file[issue.file_path].append(issue)
        
        # 生成HTML
        html = f"""
<!DOCTYPE html>
<html>
<head>
    <title>代码质量报告</title>
    <meta charset="utf-8">
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; }}
        .header {{ background-color: #f0f0f0; padding: 20px; border-radius: 5px; }}
        .summary {{ display: flex; gap: 20px; margin: 20px 0; }}
        .metric {{ background-color: #e8f4f8; padding: 15px; border-radius: 5px; flex: 1; }}
        .issues {{ margin: 20px 0; }}
        .file-section {{ margin: 20px 0; border: 1px solid #ddd; border-radius: 5px; }}
        .file-header {{ background-color: #f8f8f8; padding: 10px; font-weight: bold; }}
        .issue {{ padding: 10px; border-bottom: 1px solid #eee; }}
        .error {{ border-left: 4px solid #ff4444; }}
        .warning {{ border-left: 4px solid #ffaa00; }}
        .info {{ border-left: 4px solid #4444ff; }}
        .metrics-table {{ width: 100%; border-collapse: collapse; margin: 20px 0; }}
        .metrics-table th, .metrics-table td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
        .metrics-table th {{ background-color: #f2f2f2; }}
    </style>
</head>
<body>
    <div class="header">
        <h1>代码质量报告</h1>
        <p>生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
    </div>
    
    <div class="summary">
        <div class="metric">
            <h3>总问题数</h3>
            <p style="font-size: 24px; margin: 0;">{total_issues}</p>
        </div>
        <div class="metric">
            <h3>错误</h3>
            <p style="font-size: 24px; margin: 0; color: #ff4444;">{error_count}</p>
        </div>
        <div class="metric">
            <h3>警告</h3>
            <p style="font-size: 24px; margin: 0; color: #ffaa00;">{warning_count}</p>
        </div>
        <div class="metric">
            <h3>信息</h3>
            <p style="font-size: 24px; margin: 0; color: #4444ff;">{info_count}</p>
        </div>
    </div>
    
    <h2>代码指标</h2>
    <table class="metrics-table">
        <tr>
            <th>文件</th>
            <th>代码行数</th>
            <th>注释行数</th>
            <th>函数数量</th>
            <th>类数量</th>
            <th>类型注解覆盖率</th>
            <th>文档覆盖率</th>
        </tr>
"""
        
        for metric in metrics:
            html += f"""
        <tr>
            <td>{os.path.basename(metric.file_path)}</td>
            <td>{metric.lines_of_code}</td>
            <td>{metric.lines_of_comments}</td>
            <td>{metric.functions_count}</td>
            <td>{metric.classes_count}</td>
            <td>{metric.type_annotations_coverage:.1f}%</td>
            <td>{metric.docstring_coverage:.1f}%</td>
        </tr>
"""
        
        html += """
    </table>
    
    <h2>问题详情</h2>
    <div class="issues">
"""
        
        for file_path, file_issues in issues_by_file.items():
            html += f"""
        <div class="file-section">
            <div class="file-header">{file_path} ({len(file_issues)} 个问题)</div>
"""
            for issue in file_issues:
                html += f"""
            <div class="issue {issue.severity}">
                <strong>行 {issue.line_number}</strong> - {issue.issue_type.upper()}: {issue.message}
                {f'<br><em>建议: {issue.suggestion}</em>' if issue.suggestion else ''}
            </div>
"""
            html += """
        </div>
"""
        
        html += """
    </div>
</body>
</html>
"""
        return html


if __name__ == "__main__":
    # 测试代码质量检查
    from logger import setup_logging
    
    setup_logging("quality_test.log", "DEBUG")
    
    analyzer = CodeAnalyzer()
    reporter = CodeQualityReporter()
    
    # 分析当前目录
    issues, metrics = analyzer.analyze_directory(".", ['.py'])
    
    print(f"发现 {len(issues)} 个问题")
    print(f"分析了 {len(metrics)} 个文件")
    
    # 生成报告
    reporter.generate_report(issues, metrics)
    print("代码质量报告已生成: code_quality_report.html")
