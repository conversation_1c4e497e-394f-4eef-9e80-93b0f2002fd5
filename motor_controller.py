#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
优化的电机控制模块
提供高性能的电机操作和运动控制功能
"""

import time
import threading
from typing import Optional, Dict, List, Tuple, Callable
from queue import Queue, Empty
from dataclasses import dataclass

from PySide6.QtCore import QThread, Signal, QMutex, QObject

from DBDynamics import BeeS
from config import Axis, get_config, CoordinateConverter
from logger import get_logger, LoggerMixin
from exceptions import (
    MotorException, MotorConnectionException, MotorMovementException,
    ExceptionHandler, safe_execute, retry_on_exception, validate_parameter
)


@dataclass
class MotorPosition:
    """电机位置数据类"""
    x_pulse: int = 0
    y_pulse: int = 0
    z_pulse: int = 0
    timestamp: float = 0.0
    
    @property
    def physical_position(self) -> Tuple[float, float, float]:
        """获取物理位置"""
        return CoordinateConverter.xyz_pulse_to_physical(
            self.x_pulse, self.y_pulse, self.z_pulse
        )


@dataclass
class MotorCommand:
    """电机命令数据类"""
    axis: Axis
    target_position: int
    speed: Optional[int] = None
    acceleration: Optional[int] = None
    callback: Optional[Callable] = None


class MotorCommandQueue:
    """电机命令队列"""
    
    def __init__(self, max_size: int = 100):
        self.queue = Queue(maxsize=max_size)
        self.lock = threading.Lock()
        self.current_command: Optional[MotorCommand] = None
    
    def add_command(self, command: MotorCommand) -> bool:
        """添加命令到队列"""
        try:
            with self.lock:
                if self.queue.full():
                    # 如果队列满了，移除最旧的命令
                    try:
                        self.queue.get_nowait()
                    except Empty:
                        pass
                
                self.queue.put_nowait(command)
                return True
        except Exception:
            return False
    
    def get_command(self, timeout: float = 0.1) -> Optional[MotorCommand]:
        """从队列获取命令"""
        try:
            command = self.queue.get(timeout=timeout)
            with self.lock:
                self.current_command = command
            return command
        except Empty:
            return None
    
    def clear(self):
        """清空队列"""
        with self.lock:
            while not self.queue.empty():
                try:
                    self.queue.get_nowait()
                except Empty:
                    break
            self.current_command = None
    
    @property
    def size(self) -> int:
        """获取队列大小"""
        return self.queue.qsize()


class OptimizedMotorController(LoggerMixin):
    """优化的电机控制器"""
    
    def __init__(self, com_port: str):
        super().__init__()
        self.config = get_config()
        self.com_port = com_port
        self.motor: Optional[BeeS] = None
        self.is_connected = False
        
        # 电机状态
        self.motor_ids: List[int] = []
        self.axis_connected = {axis: False for axis in Axis}
        self.current_position = MotorPosition()
        
        # 命令队列
        self.command_queue = MotorCommandQueue()
        
        # 性能优化
        self.position_cache = {}
        self.cache_timeout = 0.1  # 位置缓存超时时间（秒）
        
        # 线程安全
        self.lock = threading.RLock()
        
        # 回调函数
        self.position_callback: Optional[Callable[[MotorPosition], None]] = None
        self.movement_callback: Optional[Callable[[Axis, bool], None]] = None
    
    @retry_on_exception(max_retries=3, delay=1.0)
    def connect(self) -> bool:
        """连接电机控制器"""
        try:
            with self.lock:
                if self.is_connected:
                    self.logger.warning("电机控制器已连接")
                    return True
                
                self.motor = BeeS(self.com_port)
                self.logger.info(f"成功连接到端口 {self.com_port}")
                
                # 扫描电机
                self.motor_ids = self.motor.scanDevices()
                self.logger.info(f"发现 {len(self.motor_ids)} 个电机: {self.motor_ids}")
                
                if not self.motor_ids:
                    raise MotorConnectionException("未发现任何电机")
                
                # 检查轴连接状态
                self._update_axis_connection_status()
                
                # 初始化电机
                self._initialize_motors()
                
                self.is_connected = True
                self.logger.info("电机控制器连接并初始化完成")
                return True
                
        except Exception as e:
            handled_exception = ExceptionHandler.handle_motor_exception(e, "连接")
            self.logger.error("电机控制器连接失败", exception=handled_exception)
            raise handled_exception
    
    def disconnect(self):
        """断开电机控制器连接"""
        try:
            with self.lock:
                if self.motor and self.is_connected:
                    # 停止所有电机
                    for motor_id in self.motor_ids:
                        try:
                            self.motor.setPowerOff(motor_id)
                        except Exception as e:
                            self.logger.warning(f"停止电机 {motor_id} 时发生错误: {e}")
                    
                    self.motor.stop()
                    self.motor = None
                
                self.is_connected = False
                self.command_queue.clear()
                self.position_cache.clear()
                self.logger.info("电机控制器断开连接")
                
        except Exception as e:
            self.logger.error("断开电机控制器连接时发生错误", exception=e)
    
    def _update_axis_connection_status(self):
        """更新轴连接状态"""
        self.axis_connected[Axis.X] = Axis.X in self.motor_ids
        self.axis_connected[Axis.Y] = Axis.Y in self.motor_ids
        self.axis_connected[Axis.Z] = Axis.Z in self.motor_ids
        
        connected_axes = [axis.name for axis, connected in self.axis_connected.items() if connected]
        self.logger.info(f"已连接轴: {', '.join(connected_axes)}")
    
    def _initialize_motors(self):
        """初始化电机"""
        soft_limits = self.config.motor.soft_limit_pulse
        
        for motor_id in self.motor_ids:
            try:
                # 设置软限位
                if motor_id < len(soft_limits):
                    self.motor.setLimitPositionN(motor_id, soft_limits[motor_id][0])
                    self.motor.setLimitPositionP(motor_id, soft_limits[motor_id][1])
                
                # 使能电机
                self.motor.setPowerOnPro(motor_id, limit_soft=1, limit_off=0, auto_recovery=1)
                
                # 设置位置模式
                self.motor.setPositionMode(motor_id)
                
                # 设置默认速度和加速度
                self.motor.setTargetVelocity(motor_id, self.config.motor.default_speed)
                self.motor.setAccTime(motor_id, self.config.motor.default_acc_time)
                
                self.logger.debug(f"电机 {motor_id} 初始化完成")
                
            except Exception as e:
                self.logger.error(f"初始化电机 {motor_id} 失败", exception=e)
    
    def move_to_position(self, axis: Axis, position: int, 
                        speed: Optional[int] = None, 
                        acceleration: Optional[int] = None,
                        wait: bool = True) -> bool:
        """移动到指定位置"""
        try:
            validate_parameter(axis, "axis", Axis)
            validate_parameter(position, "position", int)
            
            if not self.is_connected or not self.motor:
                raise MotorConnectionException("电机控制器未连接")
            
            if not self.axis_connected[axis]:
                raise MotorMovementException(f"{axis.name}轴未连接")
            
            # 检查限位
            self._check_position_limits(axis, position)
            
            with self.lock:
                motor_id = int(axis)
                
                # 设置速度和加速度（如果提供）
                if speed is not None:
                    self.motor.setTargetVelocity(motor_id, speed)
                if acceleration is not None:
                    self.motor.setAccTime(motor_id, acceleration)
                
                # 发送移动命令
                self.motor.setTargetPosition(motor_id, position)
                
                self.logger.log_motor_operation(axis.name, "移动", position)
                
                # 等待移动完成
                if wait:
                    self.motor.waitTargetPositionReached(motor_id)
                    
                    # 更新位置缓存
                    self._update_position_cache(axis, position)
                    
                    # 调用回调函数
                    if self.movement_callback:
                        safe_execute(self.movement_callback, axis, True, logger=self.logger)
                
                return True
                
        except Exception as e:
            handled_exception = ExceptionHandler.handle_motor_exception(e, "移动", axis.name)
            self.logger.error(f"{axis.name}轴移动失败", exception=handled_exception)
            
            # 调用回调函数
            if self.movement_callback:
                safe_execute(self.movement_callback, axis, False, logger=self.logger)
            
            raise handled_exception
    
    def get_position(self, axis: Axis, use_cache: bool = True) -> Optional[int]:
        """获取轴位置"""
        try:
            validate_parameter(axis, "axis", Axis)
            
            if not self.is_connected or not self.motor:
                return None
            
            if not self.axis_connected[axis]:
                return None
            
            # 检查缓存
            if use_cache:
                cached_pos, timestamp = self.position_cache.get(axis, (None, 0))
                if cached_pos is not None and (time.time() - timestamp) < self.cache_timeout:
                    return cached_pos
            
            with self.lock:
                motor_id = int(axis)
                position = self.motor.getActualPosition(motor_id)
                
                # 更新缓存
                if position is not None:
                    self._update_position_cache(axis, position)
                
                return position
                
        except Exception as e:
            self.logger.error(f"获取{axis.name}轴位置失败", exception=e)
            return None
    
    def get_all_positions(self, use_cache: bool = True) -> MotorPosition:
        """获取所有轴位置"""
        position = MotorPosition()
        position.timestamp = time.time()
        
        if self.axis_connected[Axis.X]:
            position.x_pulse = self.get_position(Axis.X, use_cache) or 0
        if self.axis_connected[Axis.Y]:
            position.y_pulse = self.get_position(Axis.Y, use_cache) or 0
        if self.axis_connected[Axis.Z]:
            position.z_pulse = self.get_position(Axis.Z, use_cache) or 0
        
        self.current_position = position
        
        # 调用位置回调
        if self.position_callback:
            safe_execute(self.position_callback, position, logger=self.logger)
        
        return position
    
    def _check_position_limits(self, axis: Axis, position: int):
        """检查位置限位"""
        soft_limits = self.config.motor.soft_limit_pulse
        if int(axis) < len(soft_limits):
            min_pos, max_pos = soft_limits[int(axis)]
            if not (min_pos <= position <= max_pos):
                raise MotorMovementException(
                    f"{axis.name}轴目标位置 {position} 超出限位范围 [{min_pos}, {max_pos}]"
                )
    
    def _update_position_cache(self, axis: Axis, position: int):
        """更新位置缓存"""
        self.position_cache[axis] = (position, time.time())
    
    def home_axis(self, axis: Axis, direction: int = -1, speed: int = 500) -> bool:
        """轴回零"""
        try:
            validate_parameter(axis, "axis", Axis)
            
            if not self.is_connected or not self.motor:
                raise MotorConnectionException("电机控制器未连接")
            
            if not self.axis_connected[axis]:
                raise MotorMovementException(f"{axis.name}轴未连接")
            
            with self.lock:
                motor_id = int(axis)
                
                self.motor.setTargetVelocity(motor_id, speed)
                self.motor.setHomingDirection(motor_id, direction)
                self.motor.setHomingLevel(motor_id, 0)
                self.motor.setHomingMode(motor_id)
                self.motor.waitHomingDone(motor_id)
                
                # 清除位置缓存
                if axis in self.position_cache:
                    del self.position_cache[axis]
                
                self.logger.log_motor_operation(axis.name, "回零")
                return True
                
        except Exception as e:
            handled_exception = ExceptionHandler.handle_motor_exception(e, "回零", axis.name)
            self.logger.error(f"{axis.name}轴回零失败", exception=handled_exception)
            raise handled_exception
    
    def set_position_callback(self, callback: Callable[[MotorPosition], None]):
        """设置位置回调函数"""
        self.position_callback = callback
    
    def set_movement_callback(self, callback: Callable[[Axis, bool], None]):
        """设置移动回调函数"""
        self.movement_callback = callback
    
    def get_controller_info(self) -> dict:
        """获取控制器信息"""
        return {
            "connected": self.is_connected,
            "com_port": self.com_port,
            "motor_ids": self.motor_ids,
            "axis_connected": dict(self.axis_connected),
            "command_queue_size": self.command_queue.size,
            "position_cache_size": len(self.position_cache),
            "current_position": self.current_position.physical_position
        }
