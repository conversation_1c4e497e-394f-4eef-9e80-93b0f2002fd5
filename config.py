#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
统一配置管理模块
包含系统的所有配置参数和常量定义
"""

import os
import json
from enum import IntEnum
from typing import Dict, Any, Optional, Tuple
from dataclasses import dataclass, asdict
from pathlib import Path


class Axis(IntEnum):
    """轴定义枚举"""
    X = 0
    Y = 1
    Z = 2


@dataclass
class MotorConfig:
    """电机配置"""
    max_x_mm: float = 65.0
    max_y_mm: float = 45.0
    max_z_micron: float = 12000.0
    
    pulses_per_mm: int = 51200
    pulses_per_micron_z: float = 51200 / 100
    
    center_x_mm: float = 27.0
    center_y_mm: float = 20.4
    center_z_micron: float = 2000.0
    
    default_speed: int = 3000
    default_acc_time: int = 100
    
    @property
    def soft_limit_pulse(self) -> list:
        """软件限位脉冲值"""
        return [
            (0, int(self.pulses_per_mm * self.max_x_mm)),
            (0, int(self.pulses_per_mm * self.max_y_mm)),
            (0, int(self.pulses_per_micron_z * self.max_z_micron))
        ]


@dataclass
class CameraConfig:
    """相机配置"""
    default_exposure_time: int = 2
    default_gain: int = 0
    auto_exposure: bool = False
    frame_buffer_size: int = 10
    fps_update_interval: float = 1.0


@dataclass
class AutoFocusConfig:
    """自动对焦配置"""
    default_method: str = "laplacian"
    coarse_range_micron: float = 2000.0
    coarse_step_micron: float = 200.0
    medium_range_micron: float = 200.0
    medium_step_micron: float = 10.0
    fine_range_micron: float = 10.0
    fine_step_micron: float = 0.5
    use_gpu: bool = True
    hill_climbing_step_size: float = 50.0
    hill_climbing_min_step: float = 1.0
    hill_climbing_max_steps: int = 100


@dataclass
class ScanConfig:
    """扫描配置"""
    default_grid_width: float = 2.4202
    default_grid_height: float = 2.0247
    default_overlap: float = 0.10
    default_save_path: str = "./scans"
    image_format: str = "jpg"
    generate_tile_config: bool = True


@dataclass
class UIConfig:
    """UI配置"""
    window_title: str = "显微镜控制系统"
    default_window_size: Tuple[int, int] = (1200, 800)
    plot_update_interval: int = 100
    position_update_interval: int = 200
    focus_score_update_interval: int = 10


@dataclass
class SystemConfig:
    """系统总配置"""
    motor: MotorConfig
    camera: CameraConfig
    autofocus: AutoFocusConfig
    scan: ScanConfig
    ui: UIConfig
    
    # 系统设置
    debug_mode: bool = False
    log_level: str = "INFO"
    log_file: str = "microscope.log"
    
    # 串口设置
    default_com_port: str = "COM8"
    com_timeout: float = 5.0
    
    def __post_init__(self):
        """初始化后处理"""
        # 确保保存路径存在
        os.makedirs(self.scan.default_save_path, exist_ok=True)


class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_file: str = "config.json"):
        self.config_file = Path(config_file)
        self._config: Optional[SystemConfig] = None
        self.load_config()
    
    def load_config(self) -> SystemConfig:
        """加载配置"""
        if self.config_file.exists():
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                self._config = self._dict_to_config(data)
                print(f"配置已从 {self.config_file} 加载")
            except Exception as e:
                print(f"加载配置文件失败: {e}，使用默认配置")
                self._config = self._create_default_config()
        else:
            print("配置文件不存在，使用默认配置")
            self._config = self._create_default_config()
            self.save_config()
        
        return self._config
    
    def save_config(self) -> bool:
        """保存配置"""
        try:
            data = self._config_to_dict(self._config)
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            print(f"配置已保存到 {self.config_file}")
            return True
        except Exception as e:
            print(f"保存配置文件失败: {e}")
            return False
    
    @property
    def config(self) -> SystemConfig:
        """获取当前配置"""
        if self._config is None:
            self._config = self.load_config()
        return self._config
    
    def _create_default_config(self) -> SystemConfig:
        """创建默认配置"""
        return SystemConfig(
            motor=MotorConfig(),
            camera=CameraConfig(),
            autofocus=AutoFocusConfig(),
            scan=ScanConfig(),
            ui=UIConfig()
        )
    
    def _config_to_dict(self, config: SystemConfig) -> Dict[str, Any]:
        """配置对象转字典"""
        return {
            'motor': asdict(config.motor),
            'camera': asdict(config.camera),
            'autofocus': asdict(config.autofocus),
            'scan': asdict(config.scan),
            'ui': asdict(config.ui),
            'debug_mode': config.debug_mode,
            'log_level': config.log_level,
            'log_file': config.log_file,
            'default_com_port': config.default_com_port,
            'com_timeout': config.com_timeout
        }
    
    def _dict_to_config(self, data: Dict[str, Any]) -> SystemConfig:
        """字典转配置对象"""
        return SystemConfig(
            motor=MotorConfig(**data.get('motor', {})),
            camera=CameraConfig(**data.get('camera', {})),
            autofocus=AutoFocusConfig(**data.get('autofocus', {})),
            scan=ScanConfig(**data.get('scan', {})),
            ui=UIConfig(**data.get('ui', {})),
            debug_mode=data.get('debug_mode', False),
            log_level=data.get('log_level', 'INFO'),
            log_file=data.get('log_file', 'microscope.log'),
            default_com_port=data.get('default_com_port', 'COM8'),
            com_timeout=data.get('com_timeout', 5.0)
        )


# 全局配置管理器实例
config_manager = ConfigManager()


# 便捷访问函数
def get_config() -> SystemConfig:
    """获取系统配置"""
    return config_manager.config


def save_config() -> bool:
    """保存当前配置"""
    return config_manager.save_config()


# 坐标转换工具函数
class CoordinateConverter:
    """坐标转换工具类"""
    
    @staticmethod
    def pulse_to_physical(pulse_value: int, axis: Axis) -> float:
        """脉冲转物理单位"""
        motor_config = get_config().motor
        
        if axis in (Axis.X, Axis.Y):
            return pulse_value / motor_config.pulses_per_mm if motor_config.pulses_per_mm else 0.0
        elif axis == Axis.Z:
            return pulse_value / motor_config.pulses_per_micron_z if motor_config.pulses_per_micron_z else 0.0
        else:
            print(f"警告: 未知轴编号 {axis}")
            return 0.0
    
    @staticmethod
    def physical_to_pulse(value: float, axis: Axis) -> int:
        """物理单位转脉冲"""
        motor_config = get_config().motor
        
        if axis in (Axis.X, Axis.Y):
            return round(value * motor_config.pulses_per_mm)
        elif axis == Axis.Z:
            return round(value * motor_config.pulses_per_micron_z)
        return 0
    
    @staticmethod
    def xyz_pulse_to_physical(x_pulse: int, y_pulse: int, z_pulse: int) -> Tuple[float, float, float]:
        """XYZ脉冲转物理单位"""
        return (
            CoordinateConverter.pulse_to_physical(x_pulse, Axis.X),
            CoordinateConverter.pulse_to_physical(y_pulse, Axis.Y),
            CoordinateConverter.pulse_to_physical(z_pulse, Axis.Z)
        )
    
    @staticmethod
    def xyz_physical_to_pulse(x_mm: float, y_mm: float, z_micron: float) -> Tuple[int, int, int]:
        """XYZ物理单位转脉冲"""
        return (
            CoordinateConverter.physical_to_pulse(x_mm, Axis.X),
            CoordinateConverter.physical_to_pulse(y_mm, Axis.Y),
            CoordinateConverter.physical_to_pulse(z_micron, Axis.Z)
        )


if __name__ == "__main__":
    # 测试配置管理器
    config = get_config()
    print(f"电机配置: {config.motor}")
    print(f"相机配置: {config.camera}")
    print(f"自动对焦配置: {config.autofocus}")
